import { LightningElement, track, wire, api } from 'lwc';
import { loadStyle } from "lightning/platformResourceLoader";
import { NavigationMixin } from 'lightning/navigation'; //For Navigation
import {ShowToastEvent} from "lightning/platformShowToastEvent";
import bankFormsCss from '@salesforce/resourceUrl/bankFormsConsentsCSS'; //CSS from static resource

import getGenericText from '@salesforce/apex/BankFormsConsentController.getFormImportantInformation';

export default class BankFormsConsents extends NavigationMixin(LightningElement) {
    @api redirectUrl;
    @api selectedConsent=[];
    @api isLoaded = false;
    @track genTextList = [];
    @track declaration;
    @track dfrId;
    @track isPrevious = false;
    @track isPartialDischarge = false;
    @track pdCheckboxLabel = false;

    formName;
    pageNumber = 3;

    constructor() {
        super();
        Promise.all([
            loadStyle(this, bankFormsCss)
        ]).then(() => {
            // console.log("Loaded css");
        }).catch(error => {
            console.error('Error loading static resources', error);
        });
    }

    connectedCallback() {
        let consent= [];
        consent=this.selectedConsent;
        this.dfrId = consent.dfr.Id;
        this.formName = consent.formName;
        let form = consent.dfr.Form_Name__c;

        if(form == 'Partial loan discharge authority'){
            //this.formName = form + ' request';
            this.isPartialDischarge = true;
            this.pdCheckboxLabel = 'I wish to partially discharge the security(ies) in accordance with my instructions in this form and electronically signed this form.';
        }
        if(form == 'Security substitution'){
            //this.formName = form + ' request';
            this.isPartialDischarge = true;
            this.pdCheckboxLabel = 'I wish to substitute the security(ies) in accordance with my instructions in this form and electronically signed this form.';
        }

        getGenericText({ formname: form })
            .then(data => {
                this.isLoaded = !this.isLoaded;
                if (data) {
                    for (let genText of data) {
                        if (genText.Form_Generic_Text__r.Important_information__c) {
                            //array of generic text
                            this.genTextList.push(genText);
                        }
                        if (genText.Form_Generic_Text__r.Declaration__c) {
                            this.declaration = genText.Form_Generic_Text__r.Current_Display_Text__c;
                        }


                    }

                    //if there are no generic text, we set this variable to false in template
                    if (this.genTextList.length === 0) {
                        this.genTextList = false;
                    }

                } else {
                    this.hasError = true;
                    console.error('Error fetching generic text: ', 'No data recieved');
                    this.showToast('An unexpected error occurred when retrieving generic text', 'error', 'Please contact AMP Bank.');
                }

            }).catch(error => {
                console.error('Error fetching generic text: ', error);
                this.hasError = true;
                this.showToast('An unexpected error occurred when retrieving generic text', 'error', 'Please contact AMP Bank.');
            });

    }

    handleContinue(event) {

        const allValid = [
            ...this.template.querySelectorAll('lightning-input'),
        ].reduce((validSoFar, inputCmp) => {
            inputCmp.reportValidity();
            return validSoFar && inputCmp.checkValidity();
        }, true);
        if (allValid) {
            //alert('All form entries look valid. Ready to submit!');
            this.pageNumber = this.pageNumber + 1;
        }

       


    }

    handlePages() {
        //handle event to return to first page from second page
        //decrement current page number by 1 (authorisation page)
        this.pageNumber = this.pageNumber - 1;
    }

    handleBack() {
        this.isPrevious = true;
        //window.history.back();
    }

    //getter to render page by page number
    get renderPageNumber() {
        let renderPage;
        if (this.pageNumber === 3) {
            renderPage = true;
        }
        else {
            renderPage = false;
        }
        return renderPage;
    }

    //handle toast events
    showToast(toastTitle, toastVariant, toastMessage){
        this.dispatchEvent(
            new ShowToastEvent({
                title: toastTitle,
                variant: toastVariant,
                message: toastMessage
            }),
        );

        //if error occurs, we keep the spinner loading
        if(toastVariant === 'error'){
            //this.loaded = false;
        }
    }
}