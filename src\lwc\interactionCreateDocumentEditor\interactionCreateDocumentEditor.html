<template>
    <div class="slds-table slds-table_cell-buffer slds-no-row-hover slds-table_bordered slds-border_bottom slds-border_left slds-border_right">
        <lightning-layout multiple-rows>
            <!-- headers -->
            <div class="slds-border_bottom docHeader">
                <lightning-layout-item size="3">
                    <div class="slds-var-p-horizontal_medium">
                        <span class="slds-form-element__label"><strong>OUTCOME</strong></span>
                    </div>
                </lightning-layout-item>
                <lightning-layout-item size="9">
                    <div class="slds-var-p-horizontal_medium">
                        <span class="slds-form-element__label"><strong>PRACTICE TEMPLATE</strong></span>
                    </div>
                </lightning-layout-item>
            </div>
            
            <!-- body -->
            <template if:true={tableData}>
                <template for:each={tableData} for:item="item">
                    <!-- input rows-->
                    <lightning-layout-item size="12" key={item.outcome}>
                        <div class=" slds-border_bottom slds-border_top">
                            <c-outcome-to-conga-template-inputs 
                                outcome={item} 
                                ontemplatechange={handleTemplateChange}
                                onremoverowchange={handleTableChange}>
                            </c-outcome-to-conga-template-inputs>
                        </div>
                    </lightning-layout-item>
                </template>
            </template>
            <template if:false={tableData.length}>
                <!-- show no data -->
                <lightning-layout-item size="12">
                    <div class="slds-var-p-horizontal_medium slds-var-p-vertical_x-small slds-border_top slds-border_bottom">
                        No data to show
                    </div>
                </lightning-layout-item>
            </template>
            
            
            <!-- footer -->
            <lightning-layout-item size="12">
                <div class="slds-var-p-around_xx-small slds-text-align_right addButton">
                    <lightning-button-icon icon-name="utility:add" alternative-text="Add" class="slds-var-m-left_xx-small" title="Add" onclick={handleMiniModalOpen}>
                    </lightning-button-icon>
                </div>
                <template if:true={isMiniModalVisible}>
                    <div class="outcomeSelector">
                        <div>
    
                            <div class="roundedBackground">
    
                                <span class="heading">Select an Outcome</span>
    
                                <lightning-button-icon 
                                    class="closeMiniModal"
                                    icon-name="utility:close" 
                                    variant="bare" 
                                    onclick={handleMiniModalClose} 
                                    alternative-text="Close" 
                                    title="Close">
                                </lightning-button-icon>
                                
                                <ul class="slds-listbox slds-listbox_vertical">
                                    <template for:each={availableOutcomeOptions} for:item="item">
                                        <li class="slds-listbox__item" key={item.value} data-value={item.value} onclick={handleOutcomeSelected}>
                                            <p class="slds-listbox__option slds-listbox__option_plain slds-media slds-media_small"  data-selected-index={index}>
                                                <span class="slds-media__body">
                                                    <span title={item.label} class="slds-truncate">
                                                        {item.label}
                                                    </span>
                                                    <span title={item.description} class="item-description slds-truncate">
                                                        {item.description}
                                                    </span>
                                                </span>
                                            </p>
                                        </li>
                                    </template>
                                </ul>
    
                            </div>
                        </div>
                        <div  class="slds-backdrop slds-backdrop_open"></div>
                    </div>
                </template>
            </lightning-layout-item>
        </lightning-layout>
    </div>
</template>