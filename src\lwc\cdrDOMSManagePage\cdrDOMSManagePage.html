<template> 
    <div class="content__body">
        <br>
        <br>
        <!-- loading -->
        <template if:false={loaded}>
            <lightning-spinner class="loading slds-is-relative" alternative-text="Loading" size="large" style="position: fixed;"></lightning-spinner>
        </template>

        <!-- Loaded -->
        <template if:true={loaded}>
            <!-- Header -->
            <!-- Header text -->
            <!-- SFP-60529 DOMS Start-->
            <div>
                <h2 class="slds-text-align_left header-text hurme-text m-margin">Manage joint account data sharing</h2>
            </div>

            <template if:true={hasError}>
                <div class="accounts-list-box slds-border_left slds-border_bottom slds-border_right" role="status">
                    <div class="slds-media__figure">
                        <lightning-icon icon-name="utility:warning" variant="warning"></lightning-icon>
                    </div>
                    <div class="slds-media__body">
                        <p class="authorisation-text">Error retrieving financial account details</p>
                    </div>
                </div>
            </template>

            <template if:false={hasError}>
                <div class="accounts-list-box slds-border_left slds-border_bottom slds-border_right">
                    <lightning-layout class="slds-grid_vertical-align-center fin-acct-details financial-accounts-padding">
                        <lightning-layout-item size="10" flexibility="auto" padding="horizontal-none" class="slds-text-align_left slds-align-middle slds-grid slds-grid_vertical">
                            <div class="slds-col">
                                <h6 class="acc-name-label">{fa.Name}
                                </h6>
                            </div>
                            <div class="slds-col">
                                <p class="text-gray fin-accts-number-text"> 
                                    <template if:true={fa.Bank_BSB__c}>
                                        BSB <span class="acc-bsb-label">{faBsbLabel}</span> &nbsp;
                                    </template> 
                                        ACC&nbsp;
                                    <span class="acc-bsb-label">{faAcctLabel}</span>
                                </p>
                            </div>
                            <div class="slds-col fa_status">
                                <div class="slds-grid slds-grid_vertical-align-center">
                                    <div class="slds-col slds-grow-none " style="max-width: 50px;">
                                        <template if:true={faIsDOMSStatusEnabled}>
                                            <svg xmlns="http://www.w3.org/2000/svg" class="svg-check status_icon" aria-label="green check">;
                                                <use xlink:href={svgURL}></use>
                                            </svg>
                                        </template>
                                        <template if:false={faIsDOMSStatusEnabled}>
                                            <svg xmlns="http://www.w3.org/2000/svg" class="svg-joint-warning status_icon" aria-label="yellow warning">;
                                                <use xlink:href={svgURLWarning}></use>
                                            </svg>
                                        </template>
                                    </div>
                                    <div class="slds-col slds-size_8-of-8">
                                        <div class="slds-col slds-size_6-of-6">
                                            <h6>{faStatus}</h6>
                                        </div>
                                        <div class="slds-col slds-size_6-of-6">
                                            <p>{faSubStatus}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="slds-col fa_status">
                                <template if:true={isClosedAccount}>
                                    <div class="slds-grid slds-grid_vertical-align-center">
                                        <div class="slds-col slds-grow-none " style="max-width: 50px;">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="svg-error status_icon" aria-label="gray cross">;
                                                <use xlink:href={svgURLError}></use>
                                            </svg>
                                        </div>
                                        <div class="slds-col slds-size_8-of-8">
                                            <div class="slds-col slds-size_6-of-6">
                                                <h6>Closed joint account</h6>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </lightning-layout-item>
                    </lightning-layout>
                </div>
    
                <!-- Enabling / disabling FAQ-->
                <div class="accounts-list-box slds-border_left slds-border_bottom slds-border_right data-shared-section slds-var-m-top_large">
                    <div style="padding-top : 24px ;" class="slds-grid_vertical-align-center fin-acct-details financial-accounts-padding">
                        <template if:false={farIsThisUserEnabled}>
                            <h6>If you enable data sharing:</h6>
                            <br>
                            <ul class="slds-list_dotted text-gray bullet-text">
                                <lightning-formatted-rich-text value={label.enablingFaqs} class="linkTextUnderLine"> </lightning-formatted-rich-text>
                            </ul>
                        </template>
                        <template if:true={farIsThisUserEnabled}>
                            <h6>If you disable data sharing:</h6>
                            <br>
                            <ul class="slds-list_dotted text-gray bullet-text">
                                <lightning-formatted-rich-text value={label.disablingFaqs} class="linkTextUnderLine"> </lightning-formatted-rich-text>
                            </ul>
                        </template>
                    </div>
                </div>
    
                <!-- Confirmation modal -->
                <template if:true={isModalOpen}>
                    <section aria-modal="true" class="slds-modal slds-fade-in-open">
                        <div class="slds-modal__container ">
                            <header class="confirmation-modal-header">
                                <h3 class="confirmation-modal-text">Are you sure?</h3>
                            </header>
                            <div class="slds-modal__content ">
                                <div class="confirmation-modal-body">
                                    <template if:false={farIsThisUserEnabled}>
                                        <lightning-formatted-rich-text value={label.enablingConfirmation} > </lightning-formatted-rich-text>
                                    </template>
                                    <template if:true={farIsThisUserEnabled}>
                                        <lightning-formatted-rich-text value={label.disablingConfirmation} > </lightning-formatted-rich-text>
                                    </template>
                                </div>
                            </div>
                            <footer class="">
                                <div class="slds-grid confirmation-modal-footer">
                                    <div class="slds-col slds-size_6-of-12 confirmation-modal-button-container-left">
                                        <button class="slds-button slds-align_absolute-center confirmation_modal_button confirmNo" onclick={handleCloseModal} ><p class="manage-label confirmNo">No, go back</p></button>
                                    </div>
                                    <div class="slds-col slds-size_6-of-12 confirmation-modal-button-container-right">
                                        <button class="slds-button slds-align_absolute-center confirmation_modal_button confirmYes" onclick={handleChangeDisclosure} ><p class="manage-label confirmYes">Yes, I confirm</p></button>
                                    </div>
                                </div>
                            </footer>
                        </div>
                    </section>
                    <div class="slds-backdrop slds-backdrop_open"></div>
                </template>
    
                <!-- Enable / diable data sharing buttons -->
                <template if:false={farIsThisUserEnabled}>
                    <div>
                        <br>
                        <h5 class="hurme-text">
                            <button data-id={faId} type="button"  class="slds-button slds-button_brand button__medium button__stop-sharing" onclick={handleOpenModal}>Enable data sharing</button>
                        </h5>
                    </div>
                </template>
                
                <template if:true={farIsThisUserEnabled}>
                    <div>
                        <p class="slds-var-m-top_large slds-text-align_left text-gray header-spacing_margin">You can stop sharing from the joint account by disabling data sharing</p>
                        <br>
                        <h5 class="hurme-text">
                            <button data-id={faId} type="button" class="slds-button slds-button_neutral button__medium button__stop-sharing" onclick={handleOpenModal}>Disable data sharing</button>
                        </h5>
                    </div>
                </template>

            </template><!--End if hasError false -->
        </template><!--End if loaded true -->
    </div><!--End of page container -->
    
</template>