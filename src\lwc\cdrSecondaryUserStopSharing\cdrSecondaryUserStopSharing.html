<!-- 
- Description   : SFP-74556 CDR Stop sharing page for Consent<PERSON> in the Manage/Consent Dashboard - Secondary Users Data Sharing June '23
-->

<template> 
    <div class="content__body-dashboard">
        <!-- loading -->
        <template if:false={loaded}>
            <lightning-spinner class="loading slds-is-relative" alternative-text="Loading" size="large" style="position: fixed;"></lightning-spinner>
        </template>

        <template if:true={loaded}>
            <template if:true={isRecordFound}>
                <!-- FIRST SCREEN -->
                <template if:true={isFirstPage}>
                    <h2 class="slds-text-align_left header-spacing font-style_light-bold m-margin">What happens when you stop sharing?</h2>
                    <br/>
                    <h3 class="slds-text-align_left header-spacing font-style_light-bold header-spacing slds-p-top_none">Things you should know before you stop sharing</h3>
                    <!-- Thing you should know info -->
                    <div class="content__body-main-dashboard-shadow slds-var-p-left_large slds-var-p-right_large slds-var-p-top_large slds-var-p-bottom_large slds-border_bottom slds-border_right slds-border_left">
                        <h6 class="slds-text-align_left">{labels.SERVICE_IMPACT_LABEL}</h6>
                        <p class="ds-text-font-style_small text-gray float_none slds-p-top_x-small">{labels.SERVICE_IMPACT_BODY} {recipientName} before you do this.</p>

                        <!-- SFP-49839 R.A.Ferenal (Accenture) - message revision for Consent Withdrawal-->
                        <h6 class="slds-text-align_left slds-p-top_large">{labels.SHARED_DATA_LABEL}</h6>
                        <template if:true={isBusiness}>
                            <p class="ds-text-font-style_small text-gray float_none slds-p-top_x-small">Before you stop sharing, you can ask {recipientName} to delete your data once it's no longer required.</p>
                        </template>
                        <template if:false={isBusiness}>
                            <p class="ds-text-font-style_small text-gray float_none slds-p-top_x-small">Before you stop sharing, you can ask {recipientName} {labels.SHARED_DATA_INFO1}</p>
                            <br/>
                            <p class="ds-text-font-style_small text-gray float_none">{recipientName} {labels.SHARED_DATA_INFO2}</p>
                        </template>
                        <!-- SFP-65084 AC2 - Claud-->
                        <template if:true={isContainsActiveJoint}>
                            <hr>
                            <h6 class="slds-text-align_left ">When you stop sharing joint account data</h6><br/>
                            <ul class="slds-list_dotted text-gray bullet-text">
                                <li>Data will no longer be shared from this joint account with this accredited data recipient.</li><br/>
                                <li>You and other account holders will still be able to share data with other accredited data recipients that have your consent.</li><br/>
                                <li>Other account holders will be notified that consent to share data with this accredited data recipient has been withdrawn.</li><br/>
                                <li>We're here to help. For any questions, please visit <lightning-formatted-rich-text value={labels.stopShareAtleast1Joint} class="linkTextUnderLine"></lightning-formatted-rich-text>.</li><br/>
                            </ul>
                        </template>
                    </div>

                    <!-- Continue and cancel buttons -->
                    <div  class="continue-cancel-footer slds-var-p-bottom_large  slds-grid_vertical-align-center slds-grid_horizontal-align-center">
                        <h5 class="hurme-text">
                            <button type="button" class="slds-button slds-button_brand button__large button-width_medium ss-button-cont" disabled={disableContinue} onclick={handleNext}>Continue</button>
                            <button data-id={consentRecordId} type="button" class="slds-button slds-button_border button__large  button-width_medium ss-button-cancel slds-align_absolute-center" onclick={handleCancel}>Cancel</button>
                        </h5>
                    </div>
                </template>

                <!-- SECOND SCREEN -->
                <template if:false={isFirstPage}>
                    <h2 class="slds-text-align_left header-spacing font-style_light-bold m-margin">Are you sure you want to stop sharing?</h2>
                    <br/>
                    <h3 class="slds-text-align_left header-spacing font-style_light-bold header-spacing slds-p-top_none slds-p-bottom_none">When you stop sharing</h3>
                    <p class="ds-text-font-style_small text-gray float_none header-spacing_margin cdr-p-top_x-small cdr-p-bottom_x-large">{recipientName} will stop collecting and using the data you shared with them below.</p>
                    
                    <!-- Data cluster info (data shared) -->
                    <div class="">
                        <template for:each={dataClusterToPermLangMap} for:item="mapKey">
                            <ul class="" key={mapKey.key}>
                                <li class="slds-p-bottom_x-small">
                                    <c-cdr-data-shared-section key={mapKey.key} map-key={mapKey.key} value={mapKey.value} header={mapKey.header} body={mapKey.body} consent-start={startDate} consent-end={endDate} recipient-name={recipientName}></c-cdr-data-shared-section>
                                </li>
                            </ul>
                        </template>
                    </div>

                   
                    <br/>
                    <div class="content__body-main-dashboard-shadow slds-var-p-around_large slds-border_bottom slds-border_right slds-border_left">
                        <h6>Do you want to stop sharing?</h6>
                        <p class="ds-text-font-style_small text-gray float_none cdr-p-top_x-small cdr-p-bottom_x-large">You should check with {recipientName} to understand the consequences.</p>
                        <h6 style="width: 100%;">
                            <button data-id={consentRecordId} type="button" class="slds-button slds-button_brand button__medium button-width_medium ss-button-cont" onclick={stopSharing}>Yes, stop sharing</button>
                            <button data-id={consentRecordId} type="button" class="slds-button slds-button_border button__medium  button-width_medium ss-button-cancel slds-align_absolute-center" onclick={handleCancel}>No, go back </button>
                        </h6>
                    </div>
                </template>
            </template>

            <template if:false={isRecordFound}>
                <h2 class="slds-text-align_left header-spacing font-style_light-bold">{errorMessage}</h2>
            </template>
        </template>
    </div>
</template>