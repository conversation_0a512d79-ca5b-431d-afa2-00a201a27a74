/**
 * SFP-66666 - CDR Notifications Preferences for Joint Accounts
*/

import { LightningElement, track, wire, api } from 'lwc';
import { loadStyle } from "lightning/platformResourceLoader";

//For navigation
import { NavigationMixin } from 'lightning/navigation';

// CSS from static resource
import cdrCss from '@salesforce/resourceUrl/CDR_Styles';
import cdrNotifCss from '@salesforce/resourceUrl/cdrNotifCSS';


// Apex controller methods
import getNotificationFrequency from '@salesforce/apex/CdrDOMSLandingController.getAccountNotifFrequency';
import accNotifFrequencyUpdate from '@salesforce/apex/CdrDOMSLandingController.accNotifFrequencyUpdate';

//Labels
import frequencyImmediate from '@salesforce/label/c.CDR_DOMS_Notif_Immediate';
import frequencyMonthly from '@salesforce/label/c.CDR_DOMS_Notif_Monthly';
import notifDisable from '@salesforce/label/c.DOMS_Notification_Disable_Confirmation';
import notifEnable from '@salesforce/label/c.DOMS_Notification_Enable_Confirmation';

//toast
import { ShowToastEvent } from 'lightning/platformShowToastEvent';


export default class cdrJointAcctNotificationsSettings extends NavigationMixin(LightningElement) {

    @track loaded;
    @track cdrNotificationsSettings;
    @track accountId;
    @track notificationFrequency;
    @track notifToggle = true;
    @track frequencyValue;
    @track isModalOpen;
    frequencyPicklistValue = '';
    isToggled = false;
    //@track radioClicked = false;


    labels = {
        frequencyImmediate,
        frequencyMonthly,
        notifDisable,
        notifEnable
    };
    
    


    get frequencyOptions() {
        return [
            { label: frequencyImmediate , value: 'notifImmediate' },
            { label: frequencyMonthly , value: 'notifMonthly' }
        ];
    }

    constructor() { 
        super(); 
        Promise.all([ 
            loadStyle(this, cdrCss),
            loadStyle(this, cdrNotifCss)
        ]).then(() => { 
            //console.log("Loaded css");
        }).catch(error => { 
            console.error('Error loading static resources', error); 
        }); 
    }

    //This is the page initialization method
    connectedCallback(){
        this.parameters = this.getQueryParameters();
        this.loaded = true;

        
        getNotificationFrequency()
        .then(data => {
            if(data){
                this.cdrNotificationsSettings = data;
                const settingsArray = this.cdrNotificationsSettings.split('-');
                this.accountId = settingsArray[0];
                this.notificationFrequency = settingsArray[1];
                if(this.notificationFrequency == 'Immediate'){
                    this.notifToggle = true;
                    this.frequencyValue = 'notifImmediate';
                }else if(this.notificationFrequency == 'Monthly'){
                    this.notifToggle = true;
                    this.frequencyValue = 'notifMonthly';
                }else if(this.notificationFrequency == 'Do not Send'){
                    this.notifToggle = false;
                }else{
                    this.notifToggle = true;
                    this.frequencyValue = 'notifImmediate';
                }
                //console.log('accountId ' + this.accountId);
                //console.log('notificationFrequency ' + this.notificationFrequency);
                //console.log('notifToggle ' + this.notifToggle);
            }else if(error){
                console.log('error ' + error);
            }
            
        }).catch(error => {
            //console.error(error); //This is needed in case there are untracebale error in JavaScript such as the profile doesn't have access to this JS
            console.error('Error fetching notification frequency information: ', error); 
            this.hasError = true;
            this.showToast('An unexpected error occurred when retrieving notifcation settings', 'error', 'Please contact AMP Bank.');
        });
    }


   //Get the URL parameters (if there's any)
    getQueryParameters() {
    var params = {};
    var search = location.search.substring(1);
    if (search) {
        params = JSON.parse('{"' + search.replace(/&/g, '","').replace(/=/g, '":"') + '"}', (key, value) => {
            return key === "" ? value : decodeURIComponent(value)
        });
    }
    return params;
    }
    

    handleToggleChange(event){
       this.notifToggle = event.target.checked;
       this.isToggled = true;
       //console.log('Toggle value: ' + this.notifToggle);
       //console.log('isToggled: ' + this.isToggled);
       if(this.notifToggle){
        this.frequencyPicklistValue = 'Immediate';
       }
    }

    handleRadioChange(event){
        const selectedFrequency = event.detail.value;
        if(selectedFrequency == 'notifImmediate'){
            this.frequencyPicklistValue = 'Immediate';
        }else if (selectedFrequency == 'notifMonthly'){
            this.frequencyPicklistValue = 'Monthly';
        }
        //console.log('Option selected with value: ' + selectedFrequency);
    }


    handleCloseModal(event){
        this.isModalOpen = false;
    }
    handleOpenModal(event){
        this.isModalOpen = true
    }

    showToast(toastTitle, toastVariant, toastMessage){
        this.dispatchEvent(
            new ShowToastEvent({
                title: toastTitle,
                variant: toastVariant,
                message: toastMessage
            }),
        );
    }

    handleClick(event){
        event.preventDefault();
        this.loaded = false; //show the spinner during far update
        if(this.notifToggle == false){
            this.frequencyPicklistValue = 'Do not Send';
        }else if(this.frequencyPicklistValue == ''){
            this.frequencyPicklistValue = this.notificationFrequency;
        }
        //console.log('notificationFrequency: ' + this.notificationFrequency);
        //console.log('frequencyPicklistValue: ' + this.frequencyPicklistValue);
        //console.log('isToggled: ' + this.isToggled);

        //The CdrDOMSLandingController method call
        accNotifFrequencyUpdate({
        accId : this.accountId,
        frequencySetting : this.frequencyPicklistValue
        })
        .then(result => {  
            if(result == 'Success'){
                event.preventDefault();
                this.initializeToast = '1'; //Var for banner
                this.dataSharingNotif = '1';

                //Go back to DOMS landing page when update success
                this[NavigationMixin.Navigate]({
                    type: 'comm__namedPage',
                    attributes: {
                        pageName: 'joint-accounts-sharing'
                    },
                   state: {
                       'notif' : this.initializeToast,
                       'dataSharingNotif': this.dataSharingNotif
                    }
                });
            } else {
                //Show a toast when update fails
                console.error('Error changing data sharing notifications frequency: ', result); 
                this.showToast('An unexpected error occurred when data sharing notifications frequency', 'error', 'Please contact AMP Bank.');
                this.loaded = true;
            }
        }).catch (error => {    
            this.isModalOpen = false;
            this.loaded = true;
            console.error('Error changing data sharing notifications frequency: ', error);
            this.showToast('An unexpected error occurred','error','Please contact AMP Bank');
        })
    }

}