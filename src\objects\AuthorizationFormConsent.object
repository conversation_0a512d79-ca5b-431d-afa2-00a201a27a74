<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <compactLayoutAssignment>SYSTEM</compactLayoutAssignment>
    <enableFeeds>false</enableFeeds>
    <externalSharingModel>Private</externalSharingModel>
    <fields>
        <fullName>AuthorizationFormTextId</fullName>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>Browser</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>ConsentCapturedDateTime</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>ConsentCapturedSource</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>ConsentCapturedSourceType</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>ConsentExpirationDateTime</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>ConsentGiverId</fullName>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>Consent_Arrangement_Record__c</fullName>
        <deleteConstraint>SetNull</deleteConstraint>
        <externalId>false</externalId>
        <label>Consent Arrangement Record</label>
        <referenceTo>Consent_Arrangement_Record__c</referenceTo>
        <relationshipLabel>Authorization Form Consents</relationshipLabel>
        <relationshipName>Authorization_Form_Consents</relationshipName>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>Consent_End_Date__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Consent End Date</label>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <type>DateTime</type>
    </fields>
    <fields>
        <fullName>DocumentVersionId</fullName>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>Email</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>InfoAuthRequestFormId</fullName>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>Ip</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>Location</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>Name</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>RelatedRecordId</fullName>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>Scope__c</fullName>
        <deleteConstraint>Restrict</deleteConstraint>
        <externalId>false</externalId>
        <label>Scope</label>
        <referenceTo>Scope__c</referenceTo>
        <relationshipLabel>Authorization Form Consents</relationshipLabel>
        <relationshipName>Authorization_Form_Consents</relationshipName>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>Status</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <listViews>
        <fullName>All_AuthorizationFormConsents</fullName>
        <columns>Id</columns>
        <columns>Name</columns>
        <columns>Consent_Arrangement_Record__c</columns>
        <columns>Scope__c</columns>
        <columns>InfoAuthRequestForm</columns>
        <columns>ConsentCapturedSourceType</columns>
        <columns>Status</columns>
        <columns>ConsentCapturedDateTime</columns>
        <columns>Consent_End_Date__c</columns>
        <filterScope>Everything</filterScope>
        <label>All Authorization Form Consents</label>
    </listViews>
    <searchLayouts>
        <customTabListAdditionalFields>ConsentGiver</customTabListAdditionalFields>
        <customTabListAdditionalFields>AuthorizationFormText</customTabListAdditionalFields>
        <customTabListAdditionalFields>ConsentCapturedSource</customTabListAdditionalFields>
        <customTabListAdditionalFields>ConsentCapturedSourceType</customTabListAdditionalFields>
        <customTabListAdditionalFields>Status</customTabListAdditionalFields>
        <customTabListAdditionalFields>LastModifiedDate</customTabListAdditionalFields>
        <customTabListAdditionalFields>LastModifiedBy</customTabListAdditionalFields>
        <searchResultsAdditionalFields>ConsentGiver</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>AuthorizationFormText</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>ConsentCapturedSource</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>ConsentCapturedSourceType</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Status</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>LastModifiedDate</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>LastModifiedBy</searchResultsAdditionalFields>
    </searchLayouts>
    <sharingModel>Private</sharingModel>
</CustomObject>
