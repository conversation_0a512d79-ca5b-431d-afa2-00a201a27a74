/** popover **/
.inlineBubble{
    position: absolute;
    opacity: 0;
    top: -99999px;
    transition: opacity 0.2s cubic-bezier(.17,.67,.83,.67);
    color: #fff;
    background: #d4504c;
}

.inlineBubble.inlineBubble_active{
    opacity: 1;
}

/** layout **/
.mainLayout{
    align-items: stretch;
}
.mainPanel{
    flex-direction: column;
    height: 100%;
}
.accountTeamsPanel{
    align-self: center;
}
.filterContainer{
    margin: 0 auto 0.75rem;
}

.accountsTreeGrid{
    opacity: 1;
    transition: opacity 0.2s cubic-bezier(.17,.67,.83,.67); 
}
.accountsTreeGrid_hidden{
    opacity: 0;
}