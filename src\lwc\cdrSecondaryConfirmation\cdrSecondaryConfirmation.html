<template>
    <div class="content__body-dashboard">
        <template if:true={loaded}>
            <h2 class="slds-text-align_left hurme-text header-spacing font-style_light-bold ">Review and confirm</h2>
            <p class="slds-text-align_left header-spacing_margin">{labels.CDR_Secondary_Review_and_Confirm_1}</p><br/>
            <p class="slds-text-align_left header-spacing_margin">{labels.CDR_Secondary_Review_and_Confirm_2}</p>

            <fieldset class="content__body-main-dashboard mtop">
                <!-- <legend class={legend_color}>
                    <p>SHARING ENABLED</p>
                </legend> --> 

                <div class="slds-border_bottom slds-media slds-var-p-around_medium dashboard-tile">
                    <div class="slds-media__figure slds-media__figure_fixed-width slds-align_absolute-center">
                        <div class="slds-welcome-mat__tile-icon-container">
                            <span class="slds-icon_container">
                                <div class="slds-icon slds-icon-text-default"> <!--existing-->
                                    <img src={cdrLogo} alt="company logo" class="arrangement-image">
                                </div>
                            </span>
                        </div>
                    </div>

                    <div class="slds-media__body slds-grid slds-grid_vertical-align-center slds-var-p-top_xx-small ">
                        <div class="slds-col slds-size_11-of-12">
                            <div class="slds-grid slds-wrap">
                                <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-large-size_1-of-2"
                                    aria-hidden="false">
                                    <h6 class="inline-text">{secondaryUser}</h6>
                                </div>
                                <!-- <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-large-size_1-of-2"
                                    aria-hidden="false">
                                    <p class="inline-text">TBC/ acc number/acc type?</p>
                                </div> -->
                            </div>
                        </div>
                    </div>
                </div>
            </fieldset>
            <!-- Account selection header -->

            <!-- Available account selection -->
            <template if:true={showSelectedFinAccts}>
                <h5 class="slds-text-align_left hurme-text header-spacing font-style_light-bold ">Account(s) secondary user's sharing access is enabled</h5>
                    <div class="accounts-list-box slds-border_left slds-border_bottom slds-border_right">
                        <template for:each={selectedFinAccts} for:item="mapKey">
                            <div key={mapKey.finAccId}>
                                <lightning-layout class="slds-grid_vertical-align-center fin-acct-details financial-accounts-padding">
                                    <lightning-layout-item size="9" flexibility="auto" padding="horizontal-none" class="slds-text-align_left slds-align-middle slds-grid slds-grid_vertical">
                                        <div class="slds-col">
                                            <h6 class="acc-name-label">{mapKey.finAccName}</h6>
                                        </div>

                                        <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-large-size_1-of-2" aria-hidden="false">
                                            <p class="slds-text-align_left text-gray consent-owner-text">
                                                <template if:true={mapKey.bsbNumber}>
                                                    BSB <span class="acc-bsb-label">{mapKey.bsbNumber}</span> &nbsp;
                                                </template>
                                                ACC <span class="acc-bsb-label">{mapKey.accNumber}</span> &nbsp;
                                            </p>
                                        </div>
                                    </lightning-layout-item>
                                    <lightning-layout-item size="12" small-device-size="2" padding="around-none"
                                        class="slds-text-align_right svg-check-spacing">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="svg-check"
                                            aria-label="green check">;
                                            <use xlink:href={svgURL}></use>
                                        </svg>
                                    </lightning-layout-item>
                                </lightning-layout>
                                <div class="slds-border_bottom border-style"></div>
                            </div>
                        </template>
                    </div>
            </template>

            <!-- unAvailable account selection -->
            <template if:true={showStopSharingAccounts}>
                <h5 class="slds-text-align_left hurme-text header-spacing font-style_light-bold ">Account(s) secondary user's sharing access is withdrawn</h5>
                    <div class="accounts-list-box slds-border_left slds-border_bottom slds-border_right">
                        <template for:each={stopSharingAccounts} for:item="mapKey">
                            <div key={mapKey.finAccId}>
                                <lightning-layout
                                    class="slds-grid_vertical-align-center fin-acct-details financial-accounts-padding">
                                    <lightning-layout-item size="9" flexibility="auto" padding="horizontal-none"
                                        class="slds-text-align_left slds-align-middle slds-grid slds-grid_vertical">
                                        <div class="slds-col">
                                            <h6 class="acc-name-label">{mapKey.finAccName}
                                            </h6>
                                        </div>

                                        <div class="slds-size_1-of-1 slds-medium-size_1-of-2 slds-large-size_1-of-2"
                                            aria-hidden="false">
                                            <p class="slds-text-align_left text-gray consent-owner-text">
                                                <template if:true={mapKey.bsbNumber}>
                                                    BSB <span class="acc-bsb-label">{mapKey.bsbNumber}</span> &nbsp;
                                                </template>
                                                ACC <span class="acc-bsb-label">{mapKey.accNumber}</span> &nbsp;
                                            </p>
                                        </div>


                                    </lightning-layout-item>
                                    <lightning-layout-item size="12" small-device-size="2" padding="around-none"
                                        class="slds-text-align_right svg-check-spacing">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="svg-circle-minus"
                                            aria-label="green check">;
                                            <use xlink:href={svgRemovedIcon}></use>
                                        </svg>
                                    </lightning-layout-item>

                                </lightning-layout>

                                <div class="slds-border_bottom border-style "></div>
                            </div>

                        </template>
                    </div>
            </template>

            <!--SFP-75138 start: Remove disclaimer text for disabling secondary user permissions, Liz Lau have confirmed-->
            <!--<h5 class="slds-text-align_left hurme-text header-spacing font-style_light-bold ">When you stop sharing</h5>-->
            <div
                class="content__body-main-dashboard-shadow slds-var-p-around_large slds-border_bottom slds-border_right slds-border_left slds-m-top_large">

                <p class="field-title bolded">What happens next</p>
                <!--SFP-75804 <p class=" text-gray float_none slds-var-p-top_large">{labels.CDR_Secondary_Review_and_Confirm_3}</p> -->
                <p class=" text-gray float_none slds-var-p-top_large">{labels.CDR_Secondary_Review_and_Confirm_4}</p>
                <p class=" text-gray float_none slds-var-p-top_large">{labels.CDR_Secondary_Review_and_Confirm_5}</p>

                <p class="field-title bolded slds-var-p-top_large">Manage data sharing</p>
                <p class=" text-gray float_none slds-var-p-top_large">{labels.CDR_Secondary_Review_and_Confirm_6}</p>

                <template if:true={showStopSharingAccounts}> <!--SFP-75804-->
                <p class="field-title bolded slds-var-p-top_large">When you stop sharing a joint account</p>
                <ul class="slds-list_dotted text-gray bullet-text">
                    <li>This may impact existing arrangements you and other account holders have setup with {dRecipient}. You may want to check with them before continuing.</li>
                    <li>{labels.CDR_Secondary_Review_and_Confirm_8}</li>
                    <li>{labels.CDR_Secondary_Review_and_Confirm_9}</li>
                    <li>{labels.CDR_Secondary_Review_and_Confirm_10}</li>
                    <li>At any time, you or other account holder(s) can disable data sharing from this account on My AMP app by going to <b>settings > manage data sharing > joint account data sharing</b> or log into My AMP at <a href='https://www.amp.com.au/'><u>amp.com.au</u></a> and under your name select <b>manage data sharing</b>.</li>
                    <li>We&apos;re here to help. For any questions, please visit&nbsp;<a href="https://www.amp.com.au/banking/open-banking" rel="noopener noreferrer" target="_blank"><u>Consumer Data Right FAQ</u></a>.</li>
                </ul>
                </template>
            </div> 
            <!--SFP-75138 end-->

            <p class="slds-text-align_left header-spacing_margin">{labels.CDR_Secondary_Review_and_Confirm_13}</p>
            <div
                class="continue-cancel-footer slds-var-p-bottom_large  slds-grid_vertical-align-center slds-grid_horizontal-align-center">
                <div>
                    <h5 class="hurme-text">
                        <button type="button"
                            class="slds-button slds-button_brand button__large button-width_medium ss-button-cont"
                            onclick={handleConfirm}>Yes, I confirm</button>
                        <button type="button"
                            class="slds-button slds-button_border button__large  button-width_medium ss-button-cancel slds-align_absolute-center button_color-primaryBlue"
                            onclick={handleCancel}>No, go back</button>
                    </h5>
                </div>
            </div>
        </template>
    </div>
</template>