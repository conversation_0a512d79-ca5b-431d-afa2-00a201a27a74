/**
 * <AUTHOR>
 * @CreatedDate				January 06, 2025
 * @description				Custom "Intrafund Advice Client" (IFA) section in the account record page.
 * @JIRA					SFP-79549
 * @RevisionHistory         -
 */
import { LightningElement, api, wire } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { getRecord, getFieldValue } from 'lightning/uiRecordApi';

// Custom Labels
import IFA_SECTION_LABEL from "@salesforce/label/c.IFA_Section_Label";
import IFA_CHECKBOX_LABEL from "@salesforce/label/c.IFA_Checkbox_Label";
import IFA_TOAST_TITLE from "@salesforce/label/c.IFA_Overall_Toast_Title";
import IFA_ACCESS_ISSUES_TOAST_VARIANT from "@salesforce/label/c.IFA_Access_Issues_Toast_Variant";
import IFA_ACCESS_GRANTED_TOAST_VARIANT from "@salesforce/label/c.IFA_Access_Granted_Toast_Variant";
import IFA_ACCESS_GRANTED_TOAST_MESSAGE from "@salesforce/label/c.IFA_Access_Granted_Toast_Message";
import IFA_ACCESS_REVOKED_TOAST_VARIANT from "@salesforce/label/c.IFA_Access_Revoked_Toast_Variant";
import IFA_ACCESS_REVOKED_TOAST_MESSAGE from "@salesforce/label/c.IFA_Access_Revoked_Toast_Message";

// Intrafund Advice Checkbox Field
import ACCOUNT_INTRAFUND_ADVICE_CLIENT_FIELD from '@salesforce/schema/Account.Intrafund_Advice_Client__c';

// APEX Controller
import processIFATeamAccess from "@salesforce/apex/AccountIntrafundAdviceController.processIntrafundAdviceTeamAccess";

export default class AccountIntrafundAdviceClient extends LightningElement {
    // Account Record Id
    @api recordId;

    checkboxVal;
    isIFAClient;
    isEditMode = false;
    chevronDown = true;
    isProcessDone = true;
    disableSaveBtn = true;
    ifaSectionLabel = IFA_SECTION_LABEL;
    ifaCheckboxLabel = IFA_CHECKBOX_LABEL;

    // Retrieves the account's "Intrafund Advice Client" checkbox field value
    @wire(getRecord, {
        recordId: '$recordId',
        fields: [ACCOUNT_INTRAFUND_ADVICE_CLIENT_FIELD]
    })
    accountRecord({error, data}){
        if(data){
            let ifaClient = getFieldValue(data, ACCOUNT_INTRAFUND_ADVICE_CLIENT_FIELD);
            if(ifaClient){
                this.isIFAClient = ifaClient;
                this.checkboxVal = ifaClient;
            }
        }
    }

    // Enables edit mode
    enableEditMode(){
        this.isEditMode = true;
    }

    // Cancels edit mode
    cancelEditMode(){
        this.isEditMode = false;
        this.disableSaveBtn = true;
    }

    // Handles the checkbox changes
    handleCheckboxChange(event){
        this.disableSaveBtn = this.isIFAClient == event.target.checked;
        this.checkboxVal = event.target.checked;
    }

    // Toggles the "Intrafund Advice Clien" (IFA) section display (Open/Close)
    toggleIFASection(event){
        this.template.querySelector('[data-id="ifa-checkbox-section"]').classList.toggle('slds-section_hide');
        this.chevronDown = !this.chevronDown;
        event.stopPropagation();
    }

    // Processes the IFA team's access to related records
    procGoldenRecAccess(){
        try{
            this.isProcessDone = false;
            processIFATeamAccess({
                accountId : this.recordId,
                ifaClientCheck : this.checkboxVal
            }).then((result) => {
                // Display toast notification based on results
                if(result.isSuccess){
                    if(this.checkboxVal) {
                        this.showToast(
                            IFA_ACCESS_GRANTED_TOAST_MESSAGE,
                            IFA_ACCESS_GRANTED_TOAST_VARIANT
                        );
                    } else {
                        this.showToast(
                            IFA_ACCESS_REVOKED_TOAST_MESSAGE,
                            IFA_ACCESS_REVOKED_TOAST_VARIANT
                        );
                    }
                    this.isIFAClient = this.checkboxVal;
                } else {
                    this.showToast(result.errorMessage, IFA_ACCESS_ISSUES_TOAST_VARIANT);
                }
                this.isEditMode = false;
                this.isProcessDone = true;
                this.disableSaveBtn = true;
            });
        } catch(error){
            console.log('@@@ [IFA] ERROR: ', error);
        }
    }

    // Displays the custom toast
    showToast(toastMsg, toastVar) {
        const event = new ShowToastEvent({
            title: IFA_TOAST_TITLE,
            variant: toastVar,
            message: toastMsg
        });
        this.dispatchEvent(event);
    }
}