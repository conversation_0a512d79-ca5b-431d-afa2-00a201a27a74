//lwc decorators
import { LightningElement, track, wire, api } from 'lwc';
//from static resource
import AMP_LOGO from '@salesforce/resourceUrl/AMP_HD_Logo';
import CDR_LOGO from '@salesforce/resourceUrl/CDR_Logo';
import LODASH from '@salesforce/resourceUrl/lodash';
import JOINT_ACCOUNT_LOGO from '@salesforce/resourceUrl/joint_account_logo'; //Joint changes

//For navigation
import { NavigationMixin } from 'lightning/navigation'; 
//server-side controllers
import getFinancialAccountDetails from '@salesforce/apex/CdrAuthBusinessAccController.getFinancialAccountDetails'; 
//Added by <PERSON> (Accenture) SFP-29251 April 5, 2020
import getInitialDataResponse from '@salesforce/apex/CdrAuthorisationController.getInitialDataResponse';
import fetchCDRSettings from '@salesforce/apex/CdrAuthorisationController.fetchCDRSettings'; //SFP-29240
import ifLoggedInUserIsNomRep from '@salesforce/apex/CdrAuthBusinessAccController.ifLoggedInUserIsNomRep'
import ifLoggedInUserIsIndividualTrustee from '@salesforce/apex/CdrAuthBusinessAccController.ifLoggedInUserIsIndividualTrustee'
import isSecondaryUser from '@salesforce/apex/CdrLandingDashboardController.isLoggedInUserSecondaryUser'; 
//labels
import unavailableFaReasonsLabel from '@salesforce/label/c.CDR_Reasons_for_Unavailable_FAs';
import AMP_CONTACT_INFO from '@salesforce/label/c.CDR_AMP_Bank_Contact_Info';
import CDR_ACCREDITATION_LINK from '@salesforce/label/c.CDR_Accreditation_Link';
import CDR_NOM_REP_FORM_URL from '@salesforce/label/c.CDR_Nom_Rep_Form_URL';
import CDR_NOM_REP_FORM_LABEL from '@salesforce/label/c.CDR_Nom_Rep_Form_Label';
import {loadScript, loadStyle } from "lightning/platformResourceLoader";
import {ShowToastEvent} from "lightning/platformShowToastEvent"
//CSS from static resource
import cdrCss from '@salesforce/resourceUrl/CDR_Styles';
import ERROR_LOGO from '@salesforce/resourceUrl/error_logo';
import LOGIN_LOGO from '@salesforce/resourceUrl/login_logo';
import AUTH_ERROR from '@salesforce/resourceUrl/cdr_authorisation_error';
import WARNING_LOGO from '@salesforce/resourceUrl/warning_logo';

export default class cdrAuthorisationBusinessAccts extends NavigationMixin(LightningElement){

    @api originatedFromArrangement = false;
    @api finAcctDetailsWrapper;
    @api parameters = {};

    error;
    @track deselectedAllAcc = false;
    @track availableFinancialAccounts = [];
    @track unavailableFinancialAccounts = [];
    @track selectedFinancialAccounts = [];
    @track loaded;
    @track unavailableCollapsed = false;
    @track IfUnavailableHide = false;
    @track initialDataResponse;
    @track invalidSharingRequest = false;
    @track unauthorizedError = false;
    @track cdrFAQLink = '';
    @track isLoggedInUserNomRep = false;
    @track isLoggedInUserIndividualTrustee = false;
    @track isLoggedInUserSecondaryUser = false;
    
    currentDate;
    recipient;
    recipientNumber;
    faDataMap = new Map();
    numberOfSelectedFas = 0;
    selectedFaSet = [];
    pageNumber = 1; //default page is first page
    ampLogo = AMP_LOGO;
    cdrLogo = CDR_LOGO;
    loginLogo = LOGIN_LOGO;
    authErrorIcon = AUTH_ERROR;
    // jointAccountLogo = JOINT_ACCOUNT_LOGO; //Joint changes
    svgURLJointAccount = `${JOINT_ACCOUNT_LOGO}#joint_account_logo`; //Joint changes
    svgURLWarning = `${WARNING_LOGO}#warning_logo`;
    unavailableFaReasonsArray = unavailableFaReasonsLabel;
    helpText;
    isRecordFound = true;
    labels = {                  // added by J.Mendoza (SFP-33107) 
        AMP_CONTACT_INFO,
        CDR_ACCREDITATION_LINK,  // added by J.Mendoza (SFP-48940)
        CDR_NOM_REP_FORM_URL,
        CDR_NOM_REP_FORM_LABEL
    }
    isRenewal = false; // Added by Johnroy (SFP-42825)

    // SFP-31540 by J.Mendoza
    recipientStatus;
    softwareProductName = '';
    softwareProductStatus;
    isOwnedByLoggedInUser = true; //Joint changes

    businessAccId;
    companyName;
    companyType;
    isBusiness = false;

    uriKey;
    scvid;
    adr;

    constructor() {
        super();
        Promise.all([
            loadStyle(this, cdrCss)
        ]).then(() => {
            // console.log("Loaded css");
        }).catch(error => {
            console.error('Error loading static resources', error);
        });
    }

    svgURL = `${ERROR_LOGO}#error_logo`

    //handle ENTER key press on LWC checkbox
    changeToggleValue(event){
        if(event.which == 13){  
            //alert("Selected"+event.target.checked);
            event.target.checked = !event.target.checked;
            this.handleSelect(event);
        }
    }


    //Select/Deselect all
    handleSelectAll() {
        //Select all
        if(this.deselectedAllAcc) {
            //If deselection, we make checked to false
            for(let toggle of this.template.querySelectorAll('input.toggleSelection')) {
                toggle.checked = false;
            }
            //Clear the selected Fa
            this.selectedFaSet = [];
            //change the elements in available set to all deselected
            //clone the list so we can set the property
            let newFaSet = _.cloneDeep(this.availableFinancialAccounts);
            for(let fa of newFaSet) {
                fa.selected = false;
            }
            this.availableFinancialAccounts = newFaSet;
        } else {
            //If select all, we add the 'checked' attribute to the toggle selections
            for(let toggle of this.template.querySelectorAll('input.toggleSelection')) {
                toggle.checked = true;
            }
            //Add all available fa to selected set
            this.selectedFaSet = [];
            for (let faData of this.availableFinancialAccounts) {
                this.selectedFaSet.push(faData);
            }
            //change the elements in available set to all selected
            //clone the list so we can set the property
            let newFaSet = _.cloneDeep(this.availableFinancialAccounts);
            for(let fa of newFaSet) {
                fa.selected = true;
            }
            this.availableFinancialAccounts = newFaSet;
        }
        this.deselectedAllAcc = !this.deselectedAllAcc;
        this.numberOfSelectedFas = this.selectedFaSet.length;
    }

    connectedCallback() {

        if(this.originatedFromArrangement == true){
            this.parameters = this.getQueryParameters();
        }

        if(this.getBusinessAccId()){
            this.businessAccId = this.getBusinessAccId();
            this.isBusiness = true;
        }

        //loading lodash
        Promise.all([
            loadScript(this, LODASH)
        ])
        .then(() => {
        })
        .catch(error => {
            //console.log('Error loading script', error);
            this.dispatchEvent(
                new ShowToastEvent({
                    title: 'Error loading Scripts',
                    variant: 'error',
                    message: error
                }),
            );
        });
        //SFP-78773 - Jeff Gacusan
        //Adobe Analytics - PageLoad Tagging
        if (!window.digitalData) {
            console.log('No digitalData');
            //window.digitalData = {};
        }else{
            if (window.digitalData && window.digitalData.userInfo) {
                //console.log('DD on');
                this.uriKey = window.digitalData.userInfo.profile.requestUriKey? window.digitalData.userInfo.profile.requestUriKey:"";
                this.scvid = window.digitalData.userInfo.profile.userID? window.digitalData.userInfo.profile.userID:"";
                this.adr = window.digitalData.page.pageInfo.ADR? window.digitalData.page.pageInfo.ADR:"";
    
                window.digitalData = {
                    event: "pageload",
                    page: {
                        pageInfo: {
                            stage: "accountSelected",
                            timestamp: Date.now(), // or a specific timestamp if you have one
                            ADR: this.adr
                        },
                    },
                    userInfo: {
                        profile: {
                            requestUriKey: this.uriKey,
                            consentId: "",
                            userID: this.scvid
                        }
                    }
                };
            } else {
                console.log('digitalData.user is not available on page load.');
            }
            console.log('window.digitalData:', window.digitalData);;
        }

        ifLoggedInUserIsNomRep({
            accId: this.businessAccId
        }).then(data => {
            this.isLoggedInUserNomRep = data;
            if(this.isLoggedInUserNomRep){
                this.method();
            }
        }).catch(error => {
            this.showToast('An unexpected error occurred', error, 'Please contact your system administrator.');
        });

        ifLoggedInUserIsIndividualTrustee({
            accId: this.businessAccId
        }).then(data => {
            this.isLoggedInUserIndividualTrustee = data;
            if(this.isLoggedInUserIndividualTrustee && !this.isLoggedInUserSecondaryUser){
                this.method();
            }
        }).catch(error => {
            this.showToast('An unexpected error occurred', error, 'Please contact your system administrator.');
        });

        isSecondaryUser({
            accId: this.businessAccId
        }).then(data => {
            this.isLoggedInUserSecondaryUser = data;
            console.log('isSecondaryUser' + this.isLoggedInUserSecondaryUser);
            if(this.isLoggedInUserSecondaryUser && !this.isLoggedInUserIndividualTrustee){
                this.method();
            }
        }).catch(error => {
            this.showToast('An unexpected error occurred', error, 'Please contact your system administrator.');
        });

        //Added by Eugene Ray Perfecio (Accenture) SFP-29251 April 5, 2020
        getInitialDataResponse({ //SFP-34910 by Heither Ann Ballero
            originatedFromArrangement: this.originatedFromArrangement,
            queryParameter: this.parameters,
            cdrRecord: 'CDR Authorisation Settings', //Added by Johnroy for SFP-48659
            businessAccountId: this.businessAccId
        }).then(data => {
            if(data){
                
                this.initialDataResponse = data;
                //check CDR Setting
                //console.log('isBusiness? ' + this.initialDataResponse.isBusiness);
                //console.log('isAuthSelectionRequired? ' + this.initialDataResponse.isAuthSelectionRequired);
                //console.log('hasOnlyConfirmationScope? ' + this.initialDataResponse.hasOnlyConfirmationScope);
                //console.log('scopeIds? ' + this.initialDataResponse.scopeIds);
                //console.log('scopeToAuthSelReqMap? ' + JSON.stringify(this.initialDataResponse.scopeToAuthSelReqMap));
                if(this.initialDataResponse.cdrSettings){
                    //console.log(this.initialDataResponse.cdrSettings);
                    let cdrAuthSettings = JSON.parse(this.initialDataResponse.cdrSettings);
                    this.cdrFAQLink = cdrAuthSettings.CDR_FAQ__c;
                    //console.log("cdrFAQLink: ",this.cdrFAQLink);
                }
                //console.log("@@@ this.initialDataResponse: ", JSON.stringify(this.initialDataResponse));
                if(this.initialDataResponse.dataRecipientSoftwareProduct){
                    this.softwareProductName = this.initialDataResponse.dataRecipientSoftwareProduct.Software_Product_Name__c;  // SFP-31540 by J.Mendoza
                    this.softwareProductStatus = this.initialDataResponse.dataRecipientSoftwareProduct.Status__c;               // SFP-31540 by J.Mendoza

                    if(this.initialDataResponse.dataRecipientSoftwareProduct.Data_Recipient_Brand__r 
                        && this.initialDataResponse.dataRecipientSoftwareProduct.Data_Recipient_Brand__r.Data_Recipient__r){
                            this.recipient = this.initialDataResponse.dataRecipientSoftwareProduct.Data_Recipient_Brand__r.Data_Recipient__r.Legal_Name__c;
                            this.recipientNumber = this.initialDataResponse.dataRecipientSoftwareProduct.Data_Recipient_Brand__r.Data_Recipient__r.Legal_Entity_ID__c;
                            this.recipientStatus = this.initialDataResponse.dataRecipientSoftwareProduct.Data_Recipient_Brand__r.Data_Recipient__r.Status__c; // SFP-31540 by J.Mendoza
                        }
                }
                
                // Added by Johnroy (SFP-42825)
                if(this.initialDataResponse.isRenewal){
                    this.isRenewal = true;
                }

                if(!this.initialDataResponse.isOwnedByLoggedInUser){
                    this.isOwnedByLoggedInUser = false;
                }


                if(this.originatedFromArrangement == true){
                    let startDate = new Date(this.initialDataResponse.sharingStartDate);
                    this.currentDate =  startDate.toLocaleDateString('en-GB', { day: 'numeric', month: 'long', year: 'numeric' }).replace(/ /g, ' ');

                } else{
                    let today = new Date();
                    this.currentDate =  today.toLocaleDateString('en-GB', { day: 'numeric', month: 'long', year: 'numeric' }).replace(/ /g, ' ');
                }

                //Added by Eugene Ray Perfecio (Accenture) SFP-33122
                if(this.initialDataResponse.errorMessage){
                    this.loaded = true;
                    switch(this.initialDataResponse.errorMessage){
                        case 'Invalid sharing request': //error due to unauthorized scope ids
                            this.invalidSharingRequest = true;
                            break;
                        case 'Unauthorized Error': //error due to not customer not meeting criteria
                            this.invalidSharingRequest = true;
                            this.unauthorizedError = true;
                            break;
                        case 'Page can not be found.':
                            if(this.originatedFromArrangement == true){
                                this.isRecordFound = false;
                            }
                        default:
                            //else show toast when error is caused by other data issues
                            this.showToast('Error occurred: ' +data.errorMessage, 'error', 'Please contact your system administrator.');
                    }
                }
            }      
            else{
                this.error = error;
                this.showToast('An unexpected error occurred', 'error', 'Please contact your system administrator.');
            }
        }).catch(error => {
            this.showToast('An unexpected error occurred', 'error', 'Please contact your system administrator.');
        });

        console.log('isLoggedInUserSecondaryUser' + this.isLoggedInUserSecondaryUser);
        console.log('isLoggedInUserIndividualTrustee' + this.isLoggedInUserIndividualTrustee);
        console.log('ifLoggedInUserIsNomRep' + this.isLoggedInUserNomRep);
        //SFP-41725
        /*fetchCDRSettings({ 
            cdrRecord: 'CDR Authorisation Settings'
        }).then(data => {
            if(data){
                let cdrAuthSettings = JSON.parse(data);
                this.cdrFAQLink = cdrAuthSettings.CDR_FAQ__c;
            }      
            else{
                //console.log('Error fetching CDR Authorisation Settings: ', error);
            }
        }).catch(error => {
            //console.log('Error fetching CDR Authorisation Settings: ', error);
        }); */

    }
    async method(){
        getFinancialAccountDetails({ //SFP-34910 by Heither Ann Ballero
            originatedFromArrangement: this.originatedFromArrangement,
            queryParameter: this.parameters,
            businessAccountId: this.businessAccId, 
            loggedInUserIsSecondary: this.isLoggedInUserSecondaryUser
        }).then(data => {
            if(data){
                this.finAcctDetailsWrapper = data;
                //console.log('finacct: ' + this.finAcctDetailsWrapper);
                //remove spinner when loaded
                this.loaded = true;
                //this.showToast('An unexpected error occurred', 'error', 'Please contact your system administrator.', 'sticky');
                //iterate through list of wrappers passed from APEX controller
                for(let faData of data){
                    if(!this.faDataMap.has(faData.finAcct.id)){
                        //key-> FA number and value is the each wrapper containing financial account details, availability, unavailability reasons and badge styling
                        this.faDataMap.set(faData.finAcct.Id, faData);
                        this.companyName = faData.companyName;
                        this.companyType = faData.companyType;
                        if(faData.finAcct.Bank_BSB__c != null){
                            faData.bsbLabel = faData.finAcct.Bank_BSB__c.split('').join(' ');
                        }
                        faData.accLabel = faData.finAcct.FinServ__FinancialAccountNumber__c.split('').join(' ');

                        if(faData.isFinAcctAvailable){
                            //console.log("faData Name: ", faData.finAcct.Name);
                            //console.log("faData.unavailableReason: " ,faData.unavailableReason);
                            //SFP-54778 added Closed Joint Account tag
                            if(faData.unavailableReason == 'Closed account' || faData.unavailableReason == 'Closed joint account'){
                                faData.closedAccount = true;
                                //console.log("id is closed");
                            } else {
                                faData.closedAccount = false;
                            }
                            //array of available FAs to be shared
                            this.availableFinancialAccounts.push(faData);
                        }
                        else {
                            //SFP-54778 added Closed Joint Account tag
                            if(faData.unavailableReason == 'Closed account' || (faData.unavailableReason == 'Closed joint account' && faData.isCloseJointMoreThan2Years)){
                                faData.closedAccount = true;
                            } else {
                                faData.closedAccount = false;
                                this.unavailableFinancialAccounts.push(faData);//SFP-54699 Only push if it is not closed
                                this.IfUnavailableHide = true;                 //SFP-54699 Only push if it is not closed
                            }
                            //array of unavailable FAs to be shared
                            //this.unavailableFinancialAccounts.push(faData);  //SFP-54699 Only push if it is not closed
                            //this.IfUnavailableHide = true;                   //SFP-54699 Only push if it is not closed

                        }
                    }
                    if(faData.isRenewal){this.isRenewal = true;} // Added by Johnroy (SFP-42825)
                }
    
                //split to store array of reasons for fa unavailability to be displayed in bulleted form
                //this.unavailableFaReasonsArray = this.unavailableFaReasonsArray.split(';');
    
                //if there are no financial accounts available, we set this variable to false in template
                if(this.availableFinancialAccounts.length === 0){
                    this.availableFinancialAccounts = false;
                }
    
                //SFP-34910 by Heither Ann Ballero
                //If originated from arrangement, put the selected  financial accounts from the consent to the selectedFaSet
                if(this.originatedFromArrangement == true || this.isRenewal == true){ // Added by Johnroy (SFP-42825)
                    let newFaSet = _.cloneDeep(this.availableFinancialAccounts);
                    for(let fa of newFaSet) {
                        if(fa.selected == true){
                            this.selectedFinancialAccounts.push(fa);
                        }
                    }
                    this.availableFinancialAccounts = newFaSet;
                    this.selectedFaSet = this.selectedFinancialAccounts;
                    this.numberOfSelectedFas = this.selectedFaSet.length;

                    //we set deselectedAllAcc flag to render either Select All/Deselect All label 
                    if(this.selectedFaSet.length === 0){
                        this.deselectedAllAcc = false;
                    }
                    else{
                        this.deselectedAllAcc = true;
                    }
                }
            } else {
                console.log('Error fetching Financial Account information: ', error);
                this.showToast('An unexpected error occurred when retrieving financial accounts', 'error', 'Please contact AMP Bank.');
            }
        }).catch(error => {
            if(this.isRecordFound == true){
                console.log('Error fetching Financial Account information: ', error);
                this.showToast('An unexpected error occurred when retrieving financial accounts', 'error', 'Please contact AMP Bank.');
            }
        });
    }
    renderedCallback(){
        //we persists the toggle on the buttons when we move from 2nd page to first page
        for(let selectedFA of this.selectedFaSet){
            for(let btn of this.template.querySelectorAll('input.toggleSelection')){
                //we check if the button value == FA number and we indicate the button is checked
                if(btn.value === selectedFA.finAcct.Id){
                    btn.checked = true;
                }
            }
        }
    }

    handleSelect(event){
        if(event.target.checked){
            //if checked, we check through the FA map if the key is present and push the entire value(faData) in the array
            if(!this.selectedFaSet.includes(this.faDataMap.get(event.target.value))){
                this.selectedFaSet.push(this.faDataMap.get(event.target.value));
                //Find the element in the available fa and set it to selected
                for(let i = 0; i < this.availableFinancialAccounts.length; i++) {
                    if(this.availableFinancialAccounts[i].finAcct.Id === event.target.value) {
                        //clone the list so we can set the property
                        let newFaSet = _.cloneDeep(this.availableFinancialAccounts);
                        newFaSet[i].selected = true;
                        this.availableFinancialAccounts = newFaSet;
                        break;
                    }
                }
            }
        }
        else{
            //we remove the element when it is in the selectedFaSet array
            for(let i = 0; i < this.selectedFaSet.length; i++) {
                if(this.selectedFaSet[i].finAcct.Id === event.target.value) {
                    this.selectedFaSet.splice(i, 1);
                    break;
                }
            }
            //Find the element in the available fa and set it to deselected
            for(let i = 0; i < this.availableFinancialAccounts.length; i++) {
                if(this.availableFinancialAccounts[i].finAcct.Id === event.target.value) {
                    //clone the list so we can set the property
                    let newFaSet = _.cloneDeep(this.availableFinancialAccounts);
                    newFaSet[i].selected = false;
                    this.availableFinancialAccounts = newFaSet;
                    break;
                }
            }
        }

        //we set deselectedAllAcc flag to render either Select All/Deselect All label 
        if(this.selectedFaSet.length === 0){
            this.deselectedAllAcc = false;
        }
        else{
            this.deselectedAllAcc = true;
        }
        this.numberOfSelectedFas = this.selectedFaSet.length;
    }


    handleUnavailableCollapse(){
        //allow collapse of unavailable FAs
        this.unavailableCollapsed = !this.unavailableCollapsed;
    }
    
    handleContinue(){
        //move to second page
        //increment current page number by 2 (confirmation page)
        this.pageNumber = this.pageNumber + 1;
    }

    handlePages(){
        //handle event to return to first page from second page
        //decrement current page number by 1 (authorisation page)
        this.pageNumber = this.pageNumber -1;
    }

    //show tooltip when hovered over
    showHelpText(){
        this.helpText = true;
    }

    showHelpTextViaKey(event){
        if(event.which == 13 || event.which==32){
            this.helpText = !this.helpText;
        }
    }

    //hides tooltip when no longer hovered over
    hideHelpText(){
        this.helpText = false;
    }

    //getter to render page by page number
    get renderPageNumber(){
        let renderPage;
        if(this.pageNumber === 1){
            renderPage = true;
        }
        else{
            renderPage = false;
        }

        return renderPage;
    }

    //handle toast events
    showToast(toastTitle, toastVariant, toastMessage, toastMode){
        this.dispatchEvent(
            new ShowToastEvent({
                title: toastTitle,
                variant: toastVariant,
                message: toastMessage,
                mode: toastMode
            }),
        );

        //if error occurs, we keep the spinner loading
        if(toastVariant === 'error'){
            //this.loaded = false;
        }
    }

    //getter function to handle all logics regarding the disabling of the continue button
    get disableContinue(){
        let disableContinue;
        //console.log('isOwnedByLoggedInUser',this.isOwnedByLoggedInUser);
        if(this.selectedFaSet.length !== 0 || !this.initialDataResponse.isAuthSelectionRequired 
            || (!this.availableFinancialAccounts && this.initialDataResponse.hasOnlyConfirmationScope)){
            disableContinue = false;
        }
        else{
            if(this.isOwnedByLoggedInUser){
                disableContinue = true;
            }else{
                disableContinue = false;
            }
        }

        return disableContinue;
    }

    errorCallback(error, stack){
        this.showToast('Unexpected error occurred.', 'error', 'Please contact your system administrator.');
    }

    //SFP-34910 by Heither Ann Ballero
    //Method for getting the query parameters
    getQueryParameters() {
        var params = {};
        var search = location.search.substring(1);
        if (search) {
            params = JSON.parse('{"' + search.replace(/&/g, '","').replace(/=/g, '":"') + '"}', (key, value) => {
                return key === "" ? value : decodeURIComponent(value)
            });
        }
        return params;
    }

    //SFP-34910 by Heither Ann Ballero
    handleCancel(event){
        if(this.originatedFromArrangement == true){
            window.history.back();
        }else{
            //SFP-29240
            fetchCDRSettings({ 
                cdrRecord: 'CDR Authorisation Settings'
            }).then(data => {
                if(data){
                    let endpoint = JSON.parse(data).Authorisation_Completion_Endpoint__c;
                    if(endpoint){
                        window.open(endpoint + '?action=SF_Cancel', "_self");
                    }
                } else {
                    this.showToast('An unexpected error occurred when retrieving CDR Authorisation Settings', 'error', 'Please contact your system administrator.');
                }
            }).catch(error => {
                this.showToast('An unexpected error occurred when retrieving CDR Authorisation Settings', 'error', 'Please contact your system administrator.');
            });
        }
    }

    handleBack() {
        window.history.back();
    }

    getBusinessAccId() {
        const queryString = window.location.search;
        
        const urlParams = new URLSearchParams(queryString);

        const account = urlParams.get('account');

        //console.log('account Id: ' + account);

        return account;
        
    }

    get isTypeCompany() {
        let isTypeCompany = false;
        if(this.companyType){
            if(this.companyType == 'Company' || this.companyType == 'Partner' || this.companyType == 'Company Trust'){
                isTypeCompany = true;
            }
        }
        return isTypeCompany;
    }
}