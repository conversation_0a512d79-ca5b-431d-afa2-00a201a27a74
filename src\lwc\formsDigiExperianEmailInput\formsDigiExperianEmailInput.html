<template> 
	<lightning-input 
		type="email" 
		label="Email"
		value={emailAddress} 
		onblur={checkInput} 
		max-length="80" 
		message-when-bad-input={validationMessage}
		message-when-pattern-mismatch={validationMessage}
		pattern="(?!(.)\1)[a-zA-Z0-9.!#$%&’*+\/=?^_`(){|}~-]+@[a-zA-Z0-9()-]+(?:\.[a-zA-Z0()-9-]+)*$"
		required={isRequired}
		disabled={isDisabled}
	></lightning-input>
	<template if:true={isWarning}>
		<div style='font-size: 14px'>
			<c-scoped-notification message={warningMessage} warning="true"></c-scoped-notification>
			<!-- <c-scoped-notification if:true={isError} message={errorMessage} error="true"></c-scoped-notification> -->
		</div>
	</template>
</template> 