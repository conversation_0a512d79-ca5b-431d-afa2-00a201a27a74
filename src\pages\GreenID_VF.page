<apex:page title="GreenID Verification" controller="GreenIDVerificationController" showHeader="false" sidebar="false" action="{!startVerification}">
    <apex:slds />

    <apex:includeScript value="{!$Label.GreenIdConfig_JS}"/>
    <apex:includeScript value="{!$Label.GreenIdUI_JS}"/>


    <apex:stylesheet value="{!$Label.GreenIdConfig_CSS}"/>
    <apex:stylesheet value="{!$Resource.GreenIDCSS}" />
    
    <script type="text/javascript">
    let onError = function(event){
            console.log('onError',event);
        }
    let onSessionComplete = function(verificationToken, overallState ){
            console.log('verificationToken onSession Complete', verificationToken,  overallState );
            updVerificationStatus();
        }
    let onSourceAttempt = function(veritificationToken , sourceName, checkState , overallState){
            console.log('onSourceAttempt', veritificationToken , sourceName, checkState , overallState );
        }
    let onSessionCancel = function(event){
            console.log('onSessionCancel',event);
            updVerificationStatus();
        }
    let myValidator =function(event){
            console.log('myValidator',event);
        return true;
        }
        
    greenidJQuery("document").ready(function() {
        console.log("B");
        greenidUI.setup({
            environment: "{!$Label.GreenID_Setup_Environment}",
            frameId: "greenid-div",
            errorCallback: onError,
            sessionCompleteCallback: onSessionComplete,
            sourceAttemptCallback: onSourceAttempt,
            sessionCancelledCallback: onSessionCancel,
            preSubmitValidationCallback: myValidator
        });
        greenidConfig.setOverrides({"enable_save_and_complete_later" : false, "dnb_tandc_text" : "By accepting these terms and conditions you give consent for AMP to disclose your name, residential address and date of birth to a credit reporting agency and ask the credit reporting agency to provide an assessment of whether the personal information so provided matches (in whole or in part) personal information contained in a credit information file in the possession or control of the credit reporting agency to assist in verifying your identity for the purposes of the Anti-Money Laundering and Counter-Terrorism Act 2006. The credit reporting agency may prepare and provide AMP with such an assessment and may use your personal information including the names, residential addresses and dates of birth contained in credit information files of you and other individuals for the purposes of preparing such an assessment. If you disagree with having your identity verified by a credit reporting agency, please select another data source or contact AMP so that we can discuss other options with you."});
        greenidConfig.setOverrides({"intro_introText0" : "<h1>We are required to comply with the Anti-Money Laundering and Counter-Terrorism Financing Act 2006. This means that we may need to obtain additional identification details when you commence a new account or undertake transactions in relation to your account.<br>Your identification may need to be verified before we can approve your request. We may decide to delay or refuse any request or transaction, including suspending a withdrawal application, if we’re concerned that there may be a breach of our legal obligations.<br>If you require further information, please contact your financial adviser or call AMP.<br>To verify your identity you'll need to match your details against one or more ID sources.<br>Get started with your first ID source below.</h1>"});
        greenidConfig.setOverrides({"intro_introText1" : "<h1>We are required to comply with the Anti-Money Laundering and Counter-Terrorism Financing Act 2006. This means that we may need to obtain additional identification details when you commence a new account or undertake transactions in relation to your account.<br>Your identification may need to be verified before we can approve your request. We may decide to delay or refuse any request or transaction, including suspending a withdrawal application, if we’re concerned that there may be a breach of our legal obligations.<br>If you require further information, please contact your financial adviser or call AMP.<br>To verify your identity you'll need to match your details against one or more ID sources.<br>Get started with your first ID source below.</h1>"});		
        greenidConfig.setOverrides({"intro_introText2" : "<h1>We are required to comply with the Anti-Money Laundering and Counter-Terrorism Financing Act 2006. This means that we may need to obtain additional identification details when you commence a new account or undertake transactions in relation to your account.<br>Your identification may need to be verified before we can approve your request. We may decide to delay or refuse any request or transaction, including suspending a withdrawal application, if we’re concerned that there may be a breach of our legal obligations.<br>If you require further information, please contact your financial adviser or call AMP.<br>To verify your identity you'll need to match your details against one or more ID sources.<br>Get started with your first ID source below.</h1>"});
        let verificationToken = '{!verificationToken}';
        let isVerified = '{!verified}';
        let isVerificationSessionComplete = '{!verificationSessionComplete}';
        let accountID = '{!accountID}';
        let accountPW = '{!accountPW}';
        console.log("verificationToken: " + verificationToken);
        console.log("isVerified: " + isVerified);
        console.log("isVerificationSessionComplete: " + isVerificationSessionComplete);
        if (verificationToken) {
            console.log("verificationToken found.");
            if (isVerified == "false" && isVerificationSessionComplete == "false") {
                //console.log("Calling continue with verification.");
                greenidUI.show(accountID, accountPW, verificationToken);
            }
        }
    });
    
    </script>
    <div class="slds-scope">
        <div id="greenid-div">
        </div>
        <apex:form >
        <apex:actionFunction name="updVerificationStatus" action="{!updateVerificationStatus}"/>

        <apex:outputPanel id="greenIdVerifProgress" title="greenIdVerifProgress" rendered="{!(!verified) && (!verificationSessionComplete)}">
            <p />
        </apex:outputPanel>

        <apex:outputPanel id="greenIdVerifOnHold" title="greenIdVerifOnHold" rendered="{!((!verified) && verificationSessionComplete)}">
            <p>Verification on hold. You can come back to it at any time.</p>
        </apex:outputPanel>

        <apex:outputPanel id="greenIdVerifComp" title="greenIdVerifComp" rendered="{!verified}">
            <p>Thank you. Click next to proceed.</p>
        </apex:outputPanel>

        </apex:form>

    </div>
    
</apex:page>