/*----------------------------------------------------------------
* @Description: Trigger for the Contact_Identity_Verification_Source__c object
* @Author: <PERSON> (Accenture) (November 2021)
* @TestClass: ContactIdVerifySourceTrigger_Test
------------------------------------------------------------------*/
trigger ContactIdentityVerificationSourceTrigger on Contact_Identity_Verification_Source__c (before insert, before update, before delete, after insert, after update, after delete) {
    TriggerFactory.createTriggerDispatcher(Contact_Identity_Verification_Source__c.SObjectType);
}