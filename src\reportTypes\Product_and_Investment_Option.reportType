<?xml version="1.0" encoding="UTF-8"?>
<ReportType xmlns="http://soap.sforce.com/2006/04/metadata">
    <baseObject>FinServ__FinancialHolding__c</baseObject>
    <category>other</category>
    <deployed>true</deployed>
    <description>To obtain data based on a product and Investment option.</description>
    <label>Product and Investment Option</label>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Type__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Security_Name__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Shares__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Balance_Calculation__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>APIR_Code__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedDate</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Effective_Date__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <masterLabel>Financial Holdings (Investment Detail Section)</masterLabel>
    </sections>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Account Number</displayNameOverride>
            <field>FinServ__FinancialAccount__c.FinServ__FinancialAccountNumber__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Financial Account Number</displayNameOverride>
            <field>FinServ__FinancialAccount__c.Account_Number_ProductCode__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Primary Owner</displayNameOverride>
            <field>FinServ__FinancialAccount__c.FinServ__PrimaryOwner__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Financial Account Product name</displayNameOverride>
            <field>FinServ__FinancialAccount__c.Product__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Status</displayNameOverride>
            <field>FinServ__FinancialAccount__c.FinServ__Status__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Financial Account Owner Name</displayNameOverride>
            <field>FinServ__FinancialAccount__c.Owner.Name</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Financial Account Name</displayNameOverride>
            <field>FinServ__FinancialAccount__c.Name</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <masterLabel>Financial Account Lookup</masterLabel>
    </sections>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Postal Address</displayNameOverride>
            <field>FinServ__PrimaryOwner__c.Postal_Address__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Postal Address Invalid</displayNameOverride>
            <field>FinServ__PrimaryOwner__c.Postal_Address_Invalid__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Email Address</displayNameOverride>
            <field>FinServ__PrimaryOwner__c.Primary_Contact_Email__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Email Address In Invalid Format</displayNameOverride>
            <field>FinServ__PrimaryOwner__c.Email_Address_In_Invalid_Format__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Mobile Phone Number</displayNameOverride>
            <field>FinServ__PrimaryOwner__c.Mobile_for_Account_Update__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Mobile Invalid</displayNameOverride>
            <field>FinServ__PrimaryOwner__c.Mobile_Invalid__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Annual Report</displayNameOverride>
            <field>FinServ__PrimaryOwner__c.Annual_Report__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Statements &amp; updates about your products</displayNameOverride>
            <field>FinServ__PrimaryOwner__c.Statements_updates_about_your_products__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Account Email</displayNameOverride>
            <field>FinServ__PrimaryOwner__c.Account_Email__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Account Owner</displayNameOverride>
            <field>FinServ__PrimaryOwner__c.Account_Owner__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Servicing SMS messages</displayNameOverride>
            <field>FinServ__PrimaryOwner__c.Servicing_SMS_messages__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Online Statements Commencement Note Sent</displayNameOverride>
            <field>FinServ__PrimaryOwner__c.Online_Statements_Commencement_Note_Sent__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <masterLabel>Account Object(Primary Owner) Lookup</masterLabel>
    </sections>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Residential Address</displayNameOverride>
            <field>FinServ__PrimaryOwner__c.FinServ__PrimaryContact__c.Residential_Address_Formula__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Residential Address Invalid</displayNameOverride>
            <field>FinServ__PrimaryOwner__c.FinServ__PrimaryContact__c.Residential_Address_Invalid__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <masterLabel>Contact Object(Primary Contact) Lookup</masterLabel>
    </sections>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Servicing Adviser First Name</displayNameOverride>
            <field>FinServ__FinancialAccount__c.Servicing_Adviser__c.FirstName</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Servicing Adviser Full Name</displayNameOverride>
            <field>FinServ__FinancialAccount__c.Servicing_Adviser__c.Name</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Servicing Adviser Last Name</displayNameOverride>
            <field>FinServ__FinancialAccount__c.Servicing_Adviser__c.LastName</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Servicing Adviser Email Valid</displayNameOverride>
            <field>FinServ__FinancialAccount__c.Servicing_Adviser__c.Email_Invalid__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Servicing Practice Licensee Name</displayNameOverride>
            <field>FinServ__FinancialAccount__c.Servicing_Practice__c.Licensee_Name__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Adviser Sales Id</displayNameOverride>
            <field>FinServ__FinancialAccount__c.Sales_Id__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Servicing Adviser Email</displayNameOverride>
            <field>FinServ__FinancialAccount__c.Servicing_Adviser__c.Email</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <masterLabel>Servicing Adviser Lookup</masterLabel>
    </sections>
</ReportType>
