<!--
 - Created by jerry.poon on 18/06/2021.
 -->

<!-- Client Comm Xplan Goals --> 
<template>
    <!-- Spinner -->
    <template if:false={loaded}>
        <div class="slds-is-relative">
            <lightning-spinner alternative-text="Loading" size="large"></lightning-spinner>
        </div>
    </template>

    <!-- Header -->
<!--    <div class="slds-text-heading_small slds-p-around_small">Goals</div> -->

    <!--Goals-->
    <template if:true={loadSuccess}>
        <template for:each={goalDetails} for:item="detail">
            <div class="slds-p-bottom_small slds-col slds-size_1-of-1" key={detail.External_Id__c}>
                <c-client-comm-xplan-goal-row details={detail}></c-client-comm-xplan-goal-row>
            </div>
        </template>
    </template>

    <!-- Error handling -->
    <template if:true={loadFail}>
        {errorMsg}
    </template>
</template>