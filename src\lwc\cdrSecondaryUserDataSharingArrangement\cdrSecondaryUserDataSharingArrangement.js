/**
 * Created by <PERSON><PERSON> (Accenture) SFP-32835 
 */
import { LightningElement, track, wire, api } from 'lwc';
import { loadStyle } from "lightning/platformResourceLoader";
//For navigation
import { NavigationMixin } from 'lightning/navigation';
// CSS from static resource
import cdrCss from '@salesforce/resourceUrl/CDR_Styles';
import CDR_LOGO from '@salesforce/resourceUrl/CDR_Logo';
import CHECK_LOGO from '@salesforce/resourceUrl/check_logo';
import WARNING_LOGO from '@salesforce/resourceUrl/warning_logo';
// Toast events
import {ShowToastEvent} from "lightning/platformShowToastEvent"
// Apex controller
import getConsentArragementRecord from '@salesforce/apex/CdrLandingDashboardController.getConsentArragementRecordForSecondaryUsers';
// Custom labels
import CDR_ACCREDITATION_LINK from '@salesforce/label/c.CDR_Accreditation_Link';
export default class cdrSecondaryUserDataSharingArrangement extends NavigationMixin(LightningElement) {
    @api recordId;
    @api consentRecord;
    @api recipientName;
    @api startDate;
    @api endDate;
    @track parameters = {};
    @track initialDataResponse;
    @track dataClusterToPermLangMap = [];
    @track sectionCollapsed = true;
    cdrLogo = CDR_LOGO;
    svgURLWarning = `${WARNING_LOGO}#warning_logo`;
    @track status;
    @track errorMessage;
    recipientLogo;
    recipientStatus;
    recipientEntityId;
    softwareProductName;
    softwareProductStatus;
    @track isRecordFound;
    legend_color;
    @track revocationDate;
    @track isAccountDataShared;
    @track consentRecordDetails;
    @track consentRecordId;
    @track isEditAccountVisible = true; //INC02104886 - ANCMRG
    @track handledStopSharing = false;
    messageString = '';
    sourceProdNameMap = new Map();
    //receiptUrl; SFP-41107. Receipt generation removed
    //showtoast
    initializeToast = ''; //Added by Johnroy - SFP-43357
    autoCloseTime = 10000; //Added by Johnroy - SFP-43357
    @track isOwnedByLoggedInUser = false;
    customerScope = false;
    @track consentOwner;
    @track consentWithdrawnBy;
    //scopeId;
    loaded = false;
    @track isFAMapEmpty = true; //SFP-71930
    @track showAccounts = true;
    @track financialAccMap = {};
    @track financialAccDetails = [];	
    @track expired;
    @track withdrawn;
    @track active = false;
    onceoff = false;
    @track ownerAccId;
    backBtnRelativeUrl = '';
    @track emptyFA = false;
    labels = {
        CDR_ACCREDITATION_LINK  // added by J.Mendoza (SFP-48940)
    }
    constructor() {
        super();
        Promise.all([
            loadStyle(this, cdrCss)
        ]).then(() => {
            // console.log("Loaded css");
        }).catch(error => {
            console.error('Error loading static resources', error);
        });
    }
    svgURL = `${CHECK_LOGO}#check_logo`;
    connectedCallback(){
        if(this.getOwnerAccId()){
            this.ownerAccId = this.getOwnerAccId();
        }

        console.log('this.parameters' + this.parameters);
        console.log('this.ownerAccId' + this.ownerAccId);

        this.parameters = this.getQueryParameters();
        this.initializeToast = this.hexToString(this.parameters.notif); //Added by Johnroy - SFP-43357

            this.backBtnRelativeUrl = 'management-secondary';
            getConsentArragementRecord({
                queryParameter: this.parameters,
                ownerAccountId: this.ownerAccId
            }).then(data => {
                if(data){
                    this.consentRecordDetails = data;
                    console.log('this.consentRecordDetails: ' + JSON.stringify(this.consentRecordDetails));
                    let consent = JSON.parse(JSON.stringify(this.consentRecordDetails));
                    if(!consent.errorMessage){
                        this.isRecordFound = true;
                        this.consentRecord = consent.consentRecord;
                        this.consentRecordId = (consent.consentRecord).Id;
                        this.isOwnedByLoggedInUser = consent.isOwnedByLoggedInUser;
                        this.consentOwner = consent.consentOwner;
                        //this.scopeId = consent.scopeId;
                        let status = (consent.consentRecord).Status__c;
                        let legend_color = 'status-style_border ';                  
                        switch(status){
                            case 'Active':
                                legend_color += 'status-style_green-color';
                                this.active = true;
                                break;
                            case 'Expired':
                                legend_color += 'status-style_grey-color';
                                this.expired = true;
                                break;
                            case 'Withdrawn':
                                legend_color += 'status-style_grey-color';
                                this.withdrawn = true;
                                break;
                            case 'Once-Off':
                                legend_color += 'status-style_grey-color';
                                this.onceoff = true;
                                break;
                        }
                        this.legend_color = legend_color;
                        this.status = status;
                        let softwareProduct = (consent.consentRecord).Data_Recipient_Software_Product__r;
                        if(softwareProduct){
                            this.softwareProductStatus = (consent.consentRecord).Data_Recipient_Software_Product_Status__c;
                            //SFP-43243 by Heither Ann Ballero
                            //Show only the Edit Accounts button when the arrangement is active and when the software product of the arrangement is active
                            if(this.active && this.softwareProductStatus == 'Active'){
                                this.isEditAccountVisible = true;
                            }
                            if(softwareProduct.Software_Product_Name__c){
                                this.softwareProductName = softwareProduct.Software_Product_Name__c
                            }
                            if(softwareProduct.Data_Recipient_Brand__r && softwareProduct.Data_Recipient_Brand__r.Data_Recipient__r){
                                let recipient = softwareProduct.Data_Recipient_Brand__r.Data_Recipient__r;
                                if(recipient.Legal_Name__c){
                                    this.recipientName = recipient.Legal_Name__c;
                                }
                                if(recipient.Logo_URI__c){
                                    this.recipientLogo = (consent.consentRecord).Data_Recipient_Software_Product__r.Data_Recipient_Brand__r.Data_Recipient__r.Logo_URI__c;
                                }
                                if(recipient.Status__c){
                                    this.recipientStatus = recipient.Status__c;
                                }
                                if(recipient.Legal_Entity_ID__c){
                                    this.recipientEntityId = recipient.Legal_Entity_ID__c;
                                }
                            }
                        }
                        if((consent.consentRecord).Start_Date__c){
                            this.startDate = new Date((consent.consentRecord).Start_Date__c).toLocaleDateString('en-GB', { day: 'numeric', month: 'long', year: 'numeric' }).replace(/ /g, ' ');
                        }
                        if((consent.consentRecord).End_Date__c){
                            this.endDate =  new Date((consent.consentRecord).End_Date__c).toLocaleDateString('en-GB', { day: 'numeric', month: 'long', year: 'numeric' }).replace(/ /g, ' ');
                        }
                        if((consent.consentRecord).Revocation_Date__c){
                            this.revocationDate = new Date((consent.consentRecord).Revocation_Date__c).toLocaleDateString('en-GB', { day: 'numeric', month: 'long', year: 'numeric' }).replace(/ /g, ' ');
                        }
                        let financialAcc = new Map();
                        let financialAccMap = {};
                        let today = new Date();
                        //if((consent.consentRecord).Authorizable_Form_Request_Records__r){ //Joint changes
                        if(consent.loggedInUserAfrrList){    
                            let relevantAFRRs = [];
                            if(status === 'Active'){
                                //relevantAFRRs = (consent.consentRecord).Authorizable_Form_Request_Records__r;  //Joint changes
                                relevantAFRRs = consent.loggedInUserAfrrList;
                                //console.log('relevant afrr : ',relevantAFRRs);
                            } else {
                                //let afrrMap =  (consent.consentRecord).Authorizable_Form_Request_Records__r.reduce((previousValue, currentValue) => {   //Joint changes
                                //console.log('loggedInUserAfrrList: ',consent.loggedInUserAfrrList);
                                let afrrMap =  consent.loggedInUserAfrrList.reduce((previousValue, currentValue) => { 
                                    let endDateCurrent = new Date(currentValue.End_Date__c);                        
                                    let fa = previousValue[currentValue.Financial_Account__c];
                                    if(fa){
                                        let endDatePrevious = new Date(fa.End_Date__c);
                                        if(endDateCurrent > endDatePrevious){
                                            previousValue[currentValue.Financial_Account__c] = currentValue;
                                        }
                                    } else {
                                        previousValue[currentValue.Financial_Account__c] = currentValue;
                                    }
                                    return previousValue;                               
                                }, {});
                              //console.log('Reduced afrr',afrrMap);
                                relevantAFRRs = Object.keys(afrrMap).map((faId) => afrrMap[faId] );
                                //console.log('relevantAFRRs: ',relevantAFRRs);
                            }
                            const objToMap = ((obj) => {
                                return new Map(Object.entries(obj));
                            });
                            this.sourceProdNameMap = objToMap(consent.sourceProdNameMap);
                            //console.log("@@@ sourceProdNameMap: ", this.sourceProdNameMap);
                            // create map of FA with bsb + financial account number as key and name as value
                            for (let i = 0; i < relevantAFRRs.length; i++){
                                let afrr =  relevantAFRRs[i];
                                 if(afrr.Financial_Account__r != null && afrr.Financial_Account__r.Product__r != null){
                                    var sourceProdId = afrr.Financial_Account__r.Product__r.Source_Product_Id__c;
                                 }
                                let endDate = new Date(afrr.End_Date__c);                           
                                //console.log("@@@ sourceProdId: ", sourceProdId);
                                //console.log((this.sourceProdNameMap).get(sourceProdId));    
                                //console.log("AFRR end date", afrr.End_Date__c);
                                //console.log("CAR end date", (consent.consentRecord).End_Date__c);
                                //console.log('car endDate : ',(consent.consentRecord).End_Date__c)
                                if(afrr.Financial_Account__c!=null && (status == 'Active' && endDate >=today || status !='Active' && (consent.consentRecord).End_Date__c === afrr.End_Date__c) ){  //joint change : withdrawal
                                    afrr.StartDate = new Date(afrr.Start_Date__c).toLocaleDateString('en-GB', { day: 'numeric', month: 'short', year: 'numeric' }).replace(/ /g, ' ');
                                    afrr.EndDate = new Date(afrr.End_Date__c).toLocaleDateString('en-GB', { day: 'numeric', month: 'short', year: 'numeric' }).replace(/ /g, ' ');
                                    if(afrr.Financial_Account__r.Bank_BSB__c != null){
                                        afrr.bsbLabel = afrr.Financial_Account__r.Bank_BSB__c.split('').join(' ');
                                    }
                                    if(afrr.Financial_Account__r.FinServ__FinancialAccountNumber__c != null){
                                        afrr.accLabel = afrr.Financial_Account__r.FinServ__FinancialAccountNumber__c.split('').join(' ');
                                    }
                                    if(afrr.Financial_Account__r.Product__r.Source_Product_Id__c != null && (this.sourceProdNameMap).has(sourceProdId)){
                                        financialAcc.set('BSB '+afrr.Financial_Account__r.Bank_BSB__c + ' ACC ' +  afrr.Financial_Account__r.FinServ__FinancialAccountNumber__c, (this.sourceProdNameMap).get(sourceProdId)); 
                                        afrr.prodName = (this.sourceProdNameMap).get(sourceProdId);
                                        this.financialAccDetails.push(afrr);
                                    }else{
                                    financialAcc.set('BSB '+afrr.Financial_Account__r.Bank_BSB__c + ' ACC ' +  afrr.Financial_Account__r.FinServ__FinancialAccountNumber__c , afrr.Financial_Account__r.Name);
                                        this.financialAccDetails.push(afrr);
                                    }
                                }
                            }
                            // convert map to array
                            financialAccMap = Array.from(financialAcc, ([key, value]) => ({ key, value }));
                            this.financialAccMap = financialAccMap;
                        }
                        if(financialAccMap.length>0){
                            this.isFAMapEmpty = false;
                        }
                        if(this.financialAccDetails.length === 0){
                            this.emptyFA = true;
                        }
                        var dataRequest = consent.dataClusterToPermLang;
                        var footnoteHeader = consent.footnoteHeader;
                        var footnoteBody = consent.footnoteBody;
                        // get data cluster details
                        for(let key in consent.dataClusterToPermLang){
                            for(let scopeKey in consent.scopeToAuthSelReqMap){
                                if(scopeKey === key && dataRequest.hasOwnProperty(key)){
                                    this.dataClusterToPermLangMap.push({key: key, value: dataRequest[key], header: footnoteHeader[key], body:footnoteBody[key]});
                                }
                            }
                        }
                        this.isAccountDataShared = consent.isAccountDataShared;
                        //SFP-41107. Receipt generation removed
                        //this.receiptUrl = consent.receiptUrl;
                    } else {
                        this.errorMessage = consent.errorMessage;
                        this.isRecordFound = false;
                    }
                    this.loaded = true;              
                    //Added by Johnroy - SFP-43357
                    if(this.initializeToast !== null ||( typeof this.initializeToast !== 'undefined' )){
                        //strFromHex = this.hexToString(this.initializeToast);
                        if(this.initializeToast === 'Edit'){
                            this.messageString = 'updated the accounts you are sharing on this consent';
                            this.showCustomToast();
                        }else if(this.initializeToast === 'Withdraw'){
                            this.messageString = 'stopped sharing your data';
                            this.showCustomToast();
                        }
                    }
                    console.log('active == ' + this.active);
                    console.log('softwareProductStatus == ' + this.softwareProductStatus);
                }
                else{
                    console.log('Error fetching initial data response: ', JSON.stringify(error));
                    this.loaded = true;
                    this.isRecordFound = false;
                    let event = new ShowToastEvent({
                        title: 'Error!',
                        message: error,
                        variant: 'error',
                        mode: 'dismissable'
                    });
                    this.dispatchEvent(event);
                }
            }).catch(error => {
                console.log('Error fetching initial data response: ', JSON.stringify(error));
                this.loaded = true;
                this.isRecordFound = false;
                let event = new ShowToastEvent({
                    title: 'Error!',
                    message: error,
                    variant: 'error',
                    mode: 'dismissable'
                });
                this.dispatchEvent(event);
            })
        //Make call to server to get our data
        
    }
    getQueryParameters() {
        var params = {};
        var search = location.search.substring(1);
        if (search) {
            params = JSON.parse('{"' + search.replace(/&/g, '","').replace(/=/g, '":"') + '"}', (key, value) => {
                return key === "" ? value : decodeURIComponent(value)
            });
        }
        return params;
    }
    // SFP-33239 by J.Mendoza (Accenture) 08/25/2020
    // navigate to stop sharing page
    handleStopSharing(event) {
        event.preventDefault();
        //Navigate to /data-sharing-arrangement?arrangement=********
            this[NavigationMixin.Navigate]({
                type: 'comm__namedPage',
                attributes: {
                    pageName: 'secondary-user-stop-sharing'
                },
                state: {
                    'arrangement': event.currentTarget.dataset.id,
                    'account': this.ownerAccId
                }
            });
    }
    // SFP-34910 by Heither Ann Ballero (Accenture) 09/07/2020
    // Navigate to edit accounts page
    handleEditAccounts(event){
        event.preventDefault();
        this[NavigationMixin.Navigate]({
            type: 'comm__namedPage',
            attributes: {
                pageName: 'secondary-user-edit-accounts'
            },
            state: {
                'arrangement': event.currentTarget.dataset.id,
                'account': this.ownerAccId
            }
        });
    }
    //show custom Toast Message Added by Johnroy - SFP-43357
    showCustomToast(){
        //console.log("@@@ SHOW TOAST @@@");
        this.handledStopSharing = true;
        //console.log("@@@ SHOW handledStopSharing: " ,this.handledStopSharing);
         //this.delayTimeout = setTimeout(() => {
         //    this.handledStopSharing = false;
         //    console.log("@@@ SHOW handledStopSharing: " ,this.handledStopSharing);
         //}, this.autoCloseTime);
    }
    //Added by Johnroy - SFP-43357
    closeToast() {
        this.handledStopSharing = false;
    }
    //Added by Johnroy - SFP-43357
    hexToString(hexx) {
        if(hexx != null){
            var hex = hexx.toString();//force conversion
            var str = '';
            for (var i = 0; (i < hex.length && hex.substr(i, 2) !== '00'); i += 2)
                str += String.fromCharCode(parseInt(hex.substr(i, 2), 16));
            //console.log("@@@ Hex to String: ", str);
            return str;
        }
        return null;
    }
     //Added by August - SFP-50409
    get showFinAccountInfo(){ 
        return this.isAccountDataShared && !this.isFAMapEmpty; 
     }
    get showEditHeader(){
       if(this.isOwnedByLoggedInUser && this.isAccountDataShared){
          return true;
       } else if(this.isOwnedByLoggedInUser && !this.isAccountDataShared){
          return false;
       } else if(!this.isOwnedByLoggedInUser){
          return this.isAccountDataShared && !this.isFAMapEmpty;
       }
    }
    get showNonInitiatorAccountDisclaimer(){
        if(this.emptyFA){
            return true;
        }else if(!this.isOwnedByLoggedInUser && this.isAccountDataShared && this.isFAMapEmpty){
           return true;
       } else if(!this.isOwnedByLoggedInUser && this.isAccountDataShared && !this.isFAMapEmpty){
           return false;
       } else if(this.isOwnedByLoggedInUser){
           return false;
       }
    }

    getOwnerAccId() {
        const queryString = window.location.search;
        
        const urlParams = new URLSearchParams(queryString);

        const account = urlParams.get('account');

        //console.log('account Id: ' + account);

        return account;
        
    }
    getCARId() {
        const queryString = window.location.search;
        
        const urlParams = new URLSearchParams(queryString);

        const arrangement = urlParams.get('arrangement');

        //console.log('account Id: ' + account);

        return arrangement;
        
    }
 }