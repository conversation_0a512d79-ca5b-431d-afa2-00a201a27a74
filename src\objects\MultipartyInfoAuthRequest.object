<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <compactLayoutAssignment>Multiparty_Information_Authorization_Request_Compact_Layout</compactLayoutAssignment>
    <compactLayouts>
        <fullName>Multiparty_Information_Authorization_Request_Compact_Layout</fullName>
        <fields>Name</fields>
        <fields>DataUsePurposeId</fields>
        <fields>Consent_Arrangement_Record__c</fields>
        <fields>AuthorizationType</fields>
        <fields>ResponseStatus</fields>
        <label>Multiparty Information Authorization Request Compact Layout</label>
    </compactLayouts>
    <enableFeeds>false</enableFeeds>
    <externalSharingModel>Private</externalSharingModel>
    <fields>
        <fullName>AuthorizationType</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>Consent_Arrangement_Record__c</fullName>
        <deleteConstraint>SetNull</deleteConstraint>
        <description>SFP-56510</description>
        <externalId>false</externalId>
        <label>Consent Arrangement Record</label>
        <referenceTo>Consent_Arrangement_Record__c</referenceTo>
        <relationshipLabel>Multiparty Information Authorization Requests</relationshipLabel>
        <relationshipName>Multiparty_Information_Authorization_Req</relationshipName>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>DataUsePurposeId</fullName>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>Name</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>ResponseStatus</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>SourceRecordId</fullName>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>
    <searchLayouts>
        <customTabListAdditionalFields>DataUsePurpose</customTabListAdditionalFields>
        <customTabListAdditionalFields>SourceRecord</customTabListAdditionalFields>
        <customTabListAdditionalFields>AuthorizationType</customTabListAdditionalFields>
        <customTabListAdditionalFields>ResponseStatus</customTabListAdditionalFields>
        <searchResultsAdditionalFields>DataUsePurpose</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>SourceRecord</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>AuthorizationType</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>ResponseStatus</searchResultsAdditionalFields>
    </searchLayouts>
    <sharingModel>Private</sharingModel>
</CustomObject>
