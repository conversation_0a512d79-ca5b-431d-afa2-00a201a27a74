import { LightningElement, track, wire, api } from 'lwc';  
import { loadStyle } from "lightning/platformResourceLoader";
//For navigation
import { NavigationMixin } from 'lightning/navigation';
import { updateRecord, getRecord, getFieldValue  } from 'lightning/uiRecordApi';
import USER_ID from '@salesforce/user/Id';
import USER_MYAMP_REDIRECT_URL from '@salesforce/schema/User.My_AMP_Redirect_URL__c';
import AMP_LOGO from '@salesforce/resourceUrl/AMP_HD_Logo';
import exitHeaderButton from '@salesforce/resourceUrl/exitHeaderButton';
import LOGIN_LOGO from '@salesforce/resourceUrl/login_logo';
//CSS from static resource
import cdrCss from '@salesforce/resourceUrl/CDR_Styles';
//Apex
import getReturnUrl from '@salesforce/apex/DigitalFormSubmissionController.isAuthenticatedUser';//SFP-55305, burhan
import { subscribe, MessageContext } from 'lightning/messageService'; //SFP-57028 Mercado
import DIGITAL_GENERAL_ENQUIRY_CHANNEL from '@salesforce/messageChannel/Digital_Confirmation__c'; //SFP-57028 Mercado

export default class MyAMPHeader extends NavigationMixin(LightningElement) {

    constructor() {
        super();
        Promise.all([
            loadStyle(this, cdrCss)
        ]).then(() => {

        }).catch(error => {
            console.error('Error loading static resources', error);
        });
    }
    
    @track parameters = {};
    @track objUser = {};
    @track isModalOpen = false;
    
    value;
    ampLogo = AMP_LOGO;
    loginLogo = LOGIN_LOGO;
    userMyAmpRedirectURL;
    exitButton = exitHeaderButton;
	returnUrl;//SFP-55305, burhan

	@api isAcknowledgementScreen = false; //SFP-56907, burhan
    @api flowStatus = ''; //SFP-61613, Steph Salanap@

    //SFP-57028 Mercado
    subscription = null;
    @wire(MessageContext)
    messageContext;

    //SFP-57028 Mercado
    subscribeToMessageChannel() {
        this.subscription = subscribe(
            this.messageContext,
            DIGITAL_GENERAL_ENQUIRY_CHANNEL,
            (message) => this.handleMessage(message)
        );
      }

    //SFP-57028 Mercado
    handleMessage(message) {
        if(message.isConfirmationPage == 'true'){
            this.isAcknowledgementScreen = true;
        }
    }

    connectedCallback(){
        
        this.subscribeToMessageChannel(); //SFP-57028 Mercado
        this.parameters = this.getQueryParameters();
        if(this.parameters['backURL']){
            let record = {
                fields: {
                    Id: USER_ID,
                    My_AMP_Redirect_URL__c: this.parameters['backURL']
                },
            };
            updateRecord(record).then((result) => {
                //console.log('User detail is successfully saved');
            }).catch(error => {
                console.log('Error on saving data',error.message.body);
            });
        }

		//SFP-55305, burhan
		getReturnUrl({source: 'Desktop'}).then(result => {
			this.returnUrl = result.redirecturl;

			//SFP-56605, xylene
			if(this.parameters['retAMPURL']){
				//console.log('bn MyAMPHeader connectedCallback retAMPURL', this.parameters['retAMPURL']);
				this.returnUrl = result.redirecturl + this.parameters['retAMPURL'];
			}
		}).catch(error => {
			console.error('bn apex error',error.message.body);
		});
    }

    getQueryParameters() {
        var params = {};
        var search = location.search.substring(1);
        if (search) {
            params = JSON.parse('{"' + search.replace(/&/g, '","').replace(/=/g, '":"') + '"}', (key, value) => {
                return key === "" ? value : decodeURIComponent(value)
            });
        }
        return params;
    }

    @wire(getRecord, {
        recordId: USER_ID,
        fields: [USER_MYAMP_REDIRECT_URL]
    }) userDetails({error,data}) {
        if (data) {
            this.userMyAmpRedirectURL = getFieldValue(data, USER_MYAMP_REDIRECT_URL);
        } else if(error){
            console.log('Error on getting user details ', error);
        }
    }

    handleBack() {
        // this.isModalOpen = true; //SFP-56907, burhan, commented-out

		//SFP-56907, burhan; updated if condition - SFP-61613, Steph Salanap
		if(this.isAcknowledgementScreen || this.flowStatus.toUpperCase() == 'WAITING'){
			this.submitDetails();
		}else{
			this.isModalOpen = true;
		}
    }

    closeModal() {
        this.isModalOpen = false;
    }
    
    submitDetails() {

        // let retR = this.returnUrl;//SFP-55305, bryan
        //window.location.href = '/secur/logout.jsp?retUrl='+retR;//SFP-55305, bryan
		window.location.href = this.returnUrl;//SFP-55305, burhan
        // window.history.back();
        //window.location.href = document.referrer + "?v=" + Math.random();
    }
} 