.h3, .rich-text-editor .ht-heading_3, .rich-text-editor h3, h3 {
    margin-bottom: 3px;
}

p {
	margin-bottom: 0rem;
}

.slds-accordion__summary-heading {
    display: flex;
    flex-grow: 1;
    min-width: 0;
    /* margin-left: -48px;
    margin-top: -28px; */
}

.slds-accordion__summary-action{
	margin-top: -8px;
	margin-left: -10px;
	
}
.slds-accordion__summary{
	margin-top: -8px;
	margin-left: -10px;
	
}

.slds-accordion__summary-content {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal !important;
}

/* For Mobile */
@media screen and (max-width: 540px) {
    button, .slds-combobox__input, textarea {
    font-size: 14px !important;
	}
	.p, p {
    font-size: 14px;
	}
	ul li, a {
    font-size: 14px;
	}
}

/* For Tablets */
@media screen and (min-width: 540px) and (max-width: 780px) {
    button, .slds-combobox__input, textarea {
    font-size: 14px !important;
	}
	
	.p, p {
    font-size: 14px;
	}
	
	ul li, a {
    font-size: 14px;
	}
}

.cDigitalFormSubmission .slds-button {
    line-height: 1.3!important;
}