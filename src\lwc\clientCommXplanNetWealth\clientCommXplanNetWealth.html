<!--
 - Created by jerry.poon on 21/06/2021.
 -->

<!-- Client Community Xplan Net Wealth --> 
<template>
    <!-- Spinner -->
    <template if:false={loaded}>
        <div class="slds-is-relative">
            <lightning-spinner alternative-text="Loading" size="large"></lightning-spinner>
        </div>
    </template>

    <template if:true={loadSuccess}>
        <section class='xplan-details__recordCard slds-grid slds-wrap'>
            <div class="xplan-details__sectionHead slds-col slds-size_1-of-1">
                <div class="slds-media slds-media_center">
                    <!-- Title -->
                    <div class="slds-col">
                        <span class="xplan-details__icon">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path></svg>
                        </span>
                        Net Position
                    </div>
                    <!-- Total -->
                    <div class="slds-no-flex">
                        <div class="xplan-net-position__total">
                            {netPositionCurrency}
                        </div>
                    </div>
                </div>
            </div>
            <!-- Details -->
            <div class="xplan-net-position__body">
                <!--Asset Details-->
                <c-client-comm-xplan-assets xplan-data={assetDetails}></c-client-comm-xplan-assets>
                <!--Liability Details-->
                <c-client-comm-xplan-liabilities xplan-data={liabilityDetails} type-mapping={liability_type} sub-type-mapping={liability_subType}></c-client-comm-xplan-liabilities>
            </div>
        </section>

    </template>
    <!-- Error handling -->
    <template if:true={loadFail}>
        {errorMsg}
    </template>
</template>