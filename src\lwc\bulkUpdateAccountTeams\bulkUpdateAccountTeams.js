import { LightningElement, api, wire } from 'lwc';
import { getConstants } from "c/bulkUpdateAccountTeamsUtil";
import getInitDetails from "@salesforce/apex/BulkUpdateAccountTeamsController.getInitDetails";
import processBulkUpdates from "@salesforce/apex/BulkUpdateAccountTeamsController.processBulkUpdates";
import getAccountsWithTeamMembers from "@salesforce/apex/BulkUpdateAccountTeamsController.getAccountsWithTeamMembers";

const CONSTANTS = getConstants();
 
export default class BulkUpdateAccountTeams extends LightningElement {
    @api selectedRecords;

    _showSpinner;
    _disableSubmitButton = true;

    // Popover attributes
    _mouseClientX;
    _mouseClientY;
    _popOverIcon;
    _popOverTitle;
    _popOverMessage;
    
    // Tree grid attributes
    _hideGridColumns = true;
    _gridColumns = CONSTANTS.COLUMN_SETTINGS;
    _gridData = [];
    _gridExpandedRows = [];
    _gridSelectedRows = [];

    _loaded = true;
    _actionOptions = CONSTANTS.ACTION_OPTIONS;
    _filterOptions = CONSTANTS.FILTER_OPTIONS;
    _defaultFilter = CONSTANTS.FILTER_TYPES.ALL;
    _accountTeamRows;
    _previousGridSelection = [];
    _accountTeamRowData = [];
    _selectedUsers = [];
    _currentUser;
    _teamRoleOptions;
    _selectionCount = "0";
    _showTreeGrid = true;
    _useTransferHeaders = false;

    connectedCallback(){
        this._init();
    }
    _init(){
        this._showSpinner = true;
        Promise.all([
            getInitDetails(),
            getAccountsWithTeamMembers({
                selectedIds: this.selectedRecords
            })
        ])
        .then((result) => {
            console.log(result);
            const initDetails = result[0];
            const accountList = result[1];
            this._setGridData(accountList);
            this._currentUser = initDetails?.currentUser;
            this._teamRoleOptions = initDetails?.teamRoleOptions;
        })
        .catch((error) => {
            console.error(error);
            this._publishToast(
                "Something Went Wrong",
                "Initialization failed.",
                "error"
            );
        })
        .finally(() => {
            this._showSpinner = false;
        })
    }
    _getGridData(filterType){
        this._showSpinner = true;
        getAccountsWithTeamMembers({
            selectedIds: this.selectedRecords,
            filterType: filterType
        })
        .then((response) => {
            this._setGridData(response);
        }).catch((error) => {
            console.error(error);
            this._publishToast(
                "Something Went Wrong",
                "Account and Account Team details could not be retreived.",
                "error"
            );
        })
        .finally(() => {
            this._showSpinner = false;
        })
    }
    get _isGridNotEmpty(){
        return (this._gridData && this._gridData.length > 0);
    }
    get _gridContainerClass(){
        return (this._isGridNotEmpty) ? '' : 'slds-hide'
    }
    /**
     * Parses data into tree grid format and sets it. Also expands all data with team members.
     * @param {*} data Raw data (format from Apex)
     */
    _setGridData(data){
        let expandedRows = [];
        const gridData = data?.map(a =>{
            let gridRow = {
                id: a.Id,
                Name: a.Name,
                accountUrl: '/'+a.Id,
                RecordTypeName: a.RecordType.Name
            };
            if(a.Servicing_Practice__r){
                gridRow.practiceUrl = '/'+a.Servicing_Practice__c;
                gridRow.servicingPracticeName = a.Servicing_Practice__r.Name;
            }
            if(a.AccountTeamMembers?.length){
                gridRow._children = a.AccountTeamMembers.map(atm => {
                    return {
                        id: atm.Id,
                        accountUrl: '/'+atm.UserId,
                        Name: atm.User.Name+' ('+atm.TeamMemberRole+')'
                    }
                });
                // Expand by default
                expandedRows.push(a.Id);
            }
            return gridRow;
        });
        console.log("gridData.length", gridData.length);
        this._gridData = (gridData) ? gridData : [];
        this._gridExpandedRows = expandedRows;
        // Tick all checkboxes
        this._tickAllCheckboxes(gridData);
    }
    _onActionChange(event){
        const action = event.detail.value;
        this._gridSelectedRows = []; // Reset selections
        this._useTransferHeaders = false;
        // Configure _accountTeamRows depending on action selected
        // !! First row Id must always be CONSTANTS.FIRST_INPUT_ROW_ID
        switch(action){
            case CONSTANTS.ACTION_TYPES.ADD:
                this.template.querySelector('.accountsTreeGrid').classList.remove('accountsTreeGrid_hidden');
                this._accountTeamRows = [
                    {
                        id: CONSTANTS.FIRST_INPUT_ROW_ID,
                        defaultTeamMember: this._currentUser
                        // SFP-55221 remove the disableTeamMemberLookup: true and disabled: true
                    }
                ];
                // Add 4 more empty rows
                for(let i= CONSTANTS.FIRST_INPUT_ROW_ID + 1; i < 5; i++){
                    this._accountTeamRows.push({id: i});
                } 

                // Default filter
                this._filterOptions = CONSTANTS.FILTER_OPTIONS;
                this.template.querySelector('.filterCombobox').value = CONSTANTS.FILTER_TYPES.ALL;// SFP-55221 Change ADVISER_NO_AT to ALL
                this._getGridData(CONSTANTS.FILTER_TYPES.ALL); // For some reason the onchange event on lightning-combobox doesn't get triggered. Calling the _getGridData method manually for now.

                break;
            case CONSTANTS.ACTION_TYPES.UPDATE:
                this.template.querySelector('.accountsTreeGrid').classList.remove('accountsTreeGrid_hidden');
                this._accountTeamRows = [
                    {id: CONSTANTS.FIRST_INPUT_ROW_ID}
                ];
                // Default filter
                this._filterOptions = [CONSTANTS.FILTER_OPTIONS[0]];
                this.template.querySelector('.filterCombobox').value = CONSTANTS.FILTER_TYPES.ALL;
                this._getGridData(CONSTANTS.FILTER_TYPES.ALL); // For some reason the onchange event on lightning-combobox doesn't get triggered. Calling the _getGridData method manually for now.

                // Show notification (SFP-49003) / SFP-55221 Changed the Message and mode 
                this._publishToast(
                    "NOTE",
                    "By default, all the Accounts that you have selected from the Accounts list view are included for processing. Please deselect/untick any Account Team Members which you do not want to process.",
                    "info",
                    "sticky"
                );
                
                break;
            case CONSTANTS.ACTION_TYPES.REMOVE:
                // Tree grid visibility will be controlled by the onrowinputchange event after _refreshInputs() is fired.
                this._accountTeamRows = [
                    {id: CONSTANTS.FIRST_INPUT_ROW_ID,
                    actionType: action}
                ];
                // Default filter
                this._filterOptions = [CONSTANTS.FILTER_OPTIONS[0]];
                this.template.querySelector('.filterCombobox').value = CONSTANTS.FILTER_TYPES.ALL;
                this._getGridData(CONSTANTS.FILTER_TYPES.ALL); // For some reason the onchange event on lightning-combobox doesn't get triggered. Calling the _getGridData method manually for now.
                break;
            case CONSTANTS.ACTION_TYPES.TRANSFER:
                this._useTransferHeaders = true;
                this.template.querySelector('.accountsTreeGrid').classList.add('accountsTreeGrid_hidden');
                this._accountTeamRows = [];
                for(let i= CONSTANTS.FIRST_INPUT_ROW_ID; i < 5; i++){
                    this._accountTeamRows.push({id: i, actionType: action});
                }   
                break;
        }
        this._hideGridColumns = false;

        this._enableDisableSubmit(this.template.querySelector('.actionCombobox').value, 
            this.template.querySelector('lightning-tree-grid').getSelectedRows());

        // Refresh input rows
        this._refreshInputs(false);

        // Reset selection count
        this._selectionCount = 0;

    }
    _onGridSelection(){
        const selectRows = this.template.querySelector('lightning-tree-grid').getSelectedRows();
        const action = this.template.querySelector('.actionCombobox').value;
        const newSelection = selectRows.filter(x => !this._previousGridSelection.includes(x));
        this._previousGridSelection = [...selectRows];
        if(selectRows.length > 0){
            // Do validation by action type
            switch(action){
                case CONSTANTS.ACTION_TYPES.ADD:
                    // Only applicable for Account type rows (level = 1)
                    if(selectRows.some(row => row.level != 1)){
                        // Only show validation error message if single selection
                        if(newSelection.length === 1){
                            this._showPopover("Invalid Selection", "Please select Accounts only.", "utility:reply");
                        }

                        // // Reset selected rows to Accounts only
                        // this._gridSelectedRows = selectRows.reduce((accounts, row) => {
                        //     if(row.level === 1){
                        //         accounts.push(row.id);
                        //     }
                        //     return accounts;
                        // },[]);
                        // this._previousGridSelection = selectRows.filter(row => row.level === 1);

                        // Reset selected rows to Team Members only
                        this._resetSelectedRows(selectRows, row => row.level === 1);
                    }
                    break;
                case CONSTANTS.ACTION_TYPES.UPDATE:
                case CONSTANTS.ACTION_TYPES.REMOVE:
                    // Only applicable for Team Member Rows (level = 2)
                    if(selectRows.some(row => row.level != 2)){
                        // Only show validation error message if single selection
                        if(newSelection.length === 1){
                            this._showPopover("Invalid Selection", "Please select Accounts Team members only.", "utility:reply");
                        }

                        // Reset selected rows to Team Members only
                        this._resetSelectedRows(selectRows, row => row.level === 2);
                    }
                    break;
            }
        }
        this._enableDisableSubmit(action, this._previousGridSelection);
        this._selectionCount = this._previousGridSelection.length;
        
    }
    
   /**
    * Unchecks grid selections that don't meet the conditions of the predicate function passed. 
    * @param {object[]} selectRows Array of row selected row data.
    * @param {function} validationFunc predicate function that determinesrow validity. Accepts the row data as parameter.
    */
    _resetSelectedRows(selectRows, validationFunc){
         
         this._gridSelectedRows = selectRows.reduce((accounts, row) => {
            if(validationFunc(row)){
                accounts.push(row.id);
            }
            return accounts;
        },[]);
        this._previousGridSelection = selectRows.filter(row => validationFunc(row));
    }

    _onFilterChange(event){
        this._getGridData(event.detail.value);
    }

    /**
     * Handles the Cancel button
     */
    _onCancel() {
        window.history.back();
    }
    /**
     * Handles the Apply Update button. Validation logic should be here.
     */
    _onSubmit(){
        let isValid = true;
        let errMessage = "";
        
        // Only get rows with valid inputs
        const action = this.template.querySelector('.actionCombobox').value;
        const accountTeamRows = [...this.template.querySelectorAll("c-account-team-member-row")];
        const accountTeamInputs = accountTeamRows.reduce((inputs, row) =>{
            const rowInputs = row.getInputs(); // we can also use this._accountTeamRowData instead.
            switch(action){
                case CONSTANTS.ACTION_TYPES.ADD:
                    // Row has team member
                    if(Array.isArray(rowInputs.user) && rowInputs.user.length > 0){
                        if(rowInputs.role && rowInputs.role !== CONSTANTS.ROLE_NONE_VALUE){
                            // Convert to Sobject format
                            inputs.push({TeamMemberRole: rowInputs.role,
                                UserId: rowInputs.user[0].id});
                        }
                        else{
                            row.setErrors([{inputType: "teamRole", message: CONSTANTS.VALIDATION_ERROR_NO_TEAM_ROLE}]);
                            isValid = false;
                            errMessage = CONSTANTS.VALIDATION_ERROR_INVALID_INPUTS;
                        }
                    // Row has no team member but HAS a team role
                    }else if(rowInputs.role && rowInputs.role !== CONSTANTS.ROLE_NONE_VALUE){
                        row.setErrors([{inputType: "teamMember", message: CONSTANTS.VALIDATION_ERROR_NO_TEAM_MEMBER}]);
                        isValid = false;
                        errMessage = CONSTANTS.VALIDATION_ERROR_INVALID_INPUTS;
                    }
                    break; 
                case CONSTANTS.ACTION_TYPES.UPDATE:
                case CONSTANTS.ACTION_TYPES.REMOVE:
                    inputs.push({
                        TeamMemberRole: (rowInputs.role && rowInputs.role !== CONSTANTS.ROLE_NONE_VALUE) ? rowInputs.role : null,
                        UserId: rowInputs.user?.[0]?.id
                    });
                    break;
                case CONSTANTS.ACTION_TYPES.TRANSFER:
                    inputs.push({
                        fromUser: rowInputs.user?.[0]?.id,
                        toUser: rowInputs.toUser?.[0]?.id,
                    });
                    break;
            }
            return inputs;
        },[]);
        // if(!isValid){
        //     this._publishToast(
        //         "Error",
        //         CONSTANTS.VALIDATION_ERROR_INVALID_INPUTS,
        //         "error"
        //     );
        // }
        // else{
            // this._processUpdates(accountTeamInputs);
        //}

        if(accountTeamInputs.length > 0){
            this._processUpdates(accountTeamInputs);
        }else{
            this._publishToast(
                "Invalid Inputs",
                CONSTANTS.VALIDATION_ERROR_INVALID_INPUTS,
                "error"
            );
        }
        console.log(JSON.parse(JSON.stringify(accountTeamInputs)));
    }

    /**
     * Calls the Apex request for update.
     */
    _processUpdates(accountTeamInputs){
        const action = this.template.querySelector('.actionCombobox').value;
        const selectRows = this.template.querySelector('lightning-tree-grid').getSelectedRows();
        const selectRowIds = selectRows.map(r => r.id);
        let err;
        this._showSpinner = true;
        processBulkUpdates({
            dataJSON: JSON.stringify(accountTeamInputs),
            action: action,
            selectedIds: selectRowIds
        })
        .then((response) => {
            if(response){
                console.log(response);
                let excludeFirstLookupReset = false;
                if(response.isSuccess){
                    const resultMap = response.resultMap;
                    let successMessage;
                    //changes
                    console.log(resultMap.isBatchStarted);
                    //
                    if(resultMap.successRecordCount > 0 || resultMap.isBatchStarted){
                        if(resultMap.isBatchStarted){
                            switch(action){
                                case CONSTANTS.ACTION_TYPES.REMOVE:
                                    successMessage = "The removal process has been initiated and can take some time to complete depending on the volume of records.";
                                    break;
                                case CONSTANTS.ACTION_TYPES.TRANSFER:
                                    successMessage = "The transfer process has been initiated and can take some time to complete depending on the volume of records.";
                                    break;
                            }
                        }
                        else{
                            switch(action){
                            case CONSTANTS.ACTION_TYPES.ADD:
                                successMessage = "Account Members were Added / Updated Successfully!";
                                excludeFirstLookupReset = true;
                                break; 
                            case CONSTANTS.ACTION_TYPES.UPDATE:
                                successMessage = "Account Members Updated Successfully!";
                                break;
                            case CONSTANTS.ACTION_TYPES.DELETE:
                                successMessage = "Account Members Deleted Successfully!";
                                break;
                            //changes 
                            case CONSTANTS.ACTION_TYPES.TRANSFER:
                                successMessage = "Account Members Transfered Successfully!";
                                break;
                            }
                        }
                        if(!resultMap.errorMessages ||  resultMap.errorMessages.length === 0){
                            // No errors
                            this._publishToast(
                                "Bulk Update Successful",
                                successMessage,
                                "success"
                            );
                        }
                        else{
                            console.error(resultMap.failedRecordList);
                            console.error(resultMap.errorMessages);
                            // Partial success. Display warning only
                            this._publishToast(
                                "Bulk Update Successful With Errors",
                                "Errors were encountered and some updates could not be completed.",
                                "warning"
                            );
                        }
                        
                    }else if(resultMap.errorMessages &&  resultMap.errorMessages.length > 0){
                        console.error(resultMap.failedRecordList);
                        console.error(resultMap.errorMessages);
                        // No success. Display error message.
                        this._publishToast(
                            "Bulk Update Failed",
                            "Errors were encountered and no update could be completed.",
                            "error"
                        );
                    }
                    
                    // Refresh data
                    this._getGridData(this.template.querySelector('.filterCombobox').value);

                    // Refresh input rows excluding first lookup on "ADD" action
                    this._refreshInputs(excludeFirstLookupReset);
                    
                }
                else{
                    err = response.errorMessage;
                }
            }
        }).catch((error) => {
            err = error;
        }).finally(() =>{
            if(err){
                console.error(err);
                this._publishToast(
                    "Something Went Wrong",
                    "Could not update the selected records.",
                    "error"
                );

                this._showSpinner = false;
            }
        });
    }

    /**
     * Shows or hides the tree grid based on delete action rule: Only show table when both inputs are populated.
     */
    _showHideTreeGridForDelete(){
        const rowInputs = this._accountTeamRowData[0];
        if(this._noTeamMember(rowInputs) && this._noTeamRole(rowInputs)){
            this.template.querySelector('.accountsTreeGrid').classList.remove('accountsTreeGrid_hidden');
            const selectRows = this.template.querySelector('lightning-tree-grid').getSelectedRows();
            this._selectionCount = selectRows.length;
        }
        else{
            this.template.querySelector('.accountsTreeGrid').classList.add('accountsTreeGrid_hidden');
            this._selectionCount = 0;
        }
    }
    
    /**
     * Enabled or disables the submit button depending on the action, selections, and inputs.
     */
    _enableDisableSubmit(actionSelected, accountsSelected){
        let _disableSubmitButton = false;
        switch(actionSelected){
            case CONSTANTS.ACTION_TYPES.ADD:
                if(accountsSelected.length === 0){
                    _disableSubmitButton = true;
                }
                break;
            case CONSTANTS.ACTION_TYPES.UPDATE:
                if((this._noTeamMember(this._accountTeamRowData[0]) && this._noTeamRole(this._accountTeamRowData[0]))
                        || accountsSelected.length === 0){
                    _disableSubmitButton = true;
                }
                break;
            case CONSTANTS.ACTION_TYPES.REMOVE:
                // If both input are missing, selection is required
                if(this._noTeamMember(this._accountTeamRowData[0]) && this._noTeamRole(this._accountTeamRowData[0])){
                    if(accountsSelected.length === 0){
                        _disableSubmitButton = true;
                    }
                }
                // ...else both inputs are required
                else if(this._noTeamMember(this._accountTeamRowData[0]) || this._noTeamRole(this._accountTeamRowData[0])){
                    _disableSubmitButton = true;
                }
                break;
            case CONSTANTS.ACTION_TYPES.TRANSFER:
                _disableSubmitButton = this._accountTeamRowData.some(r => {
                    if(this._noTeamMember(r) && this._noToTeamMember(r)){
                        // Both lookups are empty so this will be ignored and therefore valid
                        return false;
                    }
                    else{
                        return this._noTeamMember(r) || this._noToTeamMember(r);
                    }
                });
                if(!_disableSubmitButton){
                    // If all inputs are empty then disable
                    _disableSubmitButton = this._accountTeamRowData.every(r => this._noTeamMember(r) && this._noToTeamMember(r));
                }
                break;
            default:
                _disableSubmitButton = true;
                break;
        }
        this._disableSubmitButton = _disableSubmitButton;
    }

    /**
     * Returns true if row input team member is empty.
     */
     _noTeamMember(rowInputs){
        if(!rowInputs || !Array.isArray(rowInputs.user) || rowInputs.user.length === 0){
            return true;
        }
        else{
            return false;
        }
    }
    /**
     * Returns true if row input team role is empty.
     */
     _noTeamRole(rowInputs){
        if(!rowInputs?.role || rowInputs.role === CONSTANTS.ROLE_NONE_VALUE){
            return true;
        }
        else{
            return false;
        }
    }
    /**
     * Returns true if row input "TO" team member is empty.
     */
     _noToTeamMember(rowInputs){
        if(!rowInputs || !Array.isArray(rowInputs.toUser) || rowInputs.toUser.length === 0){
            return true;
        }
        else{
            return false;
        }
    }

    /**
     * Reset inputs and their validation errors.
     */
    _refreshInputs(excludeFirstLookup){
        this._accountTeamRowData = [];
        const accountTeamRows = [...this.template.querySelectorAll("c-account-team-member-row")];
        accountTeamRows.forEach(row => {
            row.resetErrors();
            row.resetInputs(excludeFirstLookup); // This will trigger the rowInputChange event as well
        });
    }

    /**
     * Handles the rowInputChange from accountTeamMemberRow
     */
    _handleRowInputChange(event){
        const data = event.detail;
        console.log(JSON.parse(JSON.stringify(data)));

        // Update _accountTeamRowData
        const existingUserIndex = this._accountTeamRowData.findIndex(atm => atm.rowId === data.rowId);

        if(existingUserIndex > -1){
            this._accountTeamRowData[existingUserIndex] = data;
        }
        else{
            // add new entry
            this._accountTeamRowData.push(data);
        }

        // Update _selectedUsers to prevent users being searched again from lookup
        let selectedIds = this._accountTeamRowData.reduce((ids, atm) => {
            if(Array.isArray(atm.user) && atm.user.length > 0){
                ids.push(atm.user[0].id);
            }
            return ids;
        }, []);
        this._selectedUsers = [...new Set(selectedIds)];

        const action = this.template.querySelector('.actionCombobox').value;
        this._enableDisableSubmit(action, this.template.querySelector('lightning-tree-grid').getSelectedRows());

        // Show/Hide tree grid
        if(action === CONSTANTS.ACTION_TYPES.REMOVE){
            this._showHideTreeGridForDelete();
        }

        console.log(JSON.parse(JSON.stringify(this._selectedUsers)));
    }
    
    _onMouseDown(event){
        this._mouseClientX = event.pageX; 
        this._mouseClientY = event.pageY;
    }

    _publishToast(title, message, variant = "info", mode = "dismissable") {
        const toast = this.template.querySelector('[data-id="toast"]');
        const params = {
            title: title,
                variant: variant,
                message: message,
                mode: mode
        };
        toast.showToast(params);

        if(variant === "error"){
            console.error(title, message);
        }
    }

    _showPopover(title, message, icon){
        const popover = this.template.querySelector('.inlineBubble');
        const rect = popover.getBoundingClientRect();
        this._popOverTitle = title;
        this._popOverMessage = message;
        this._popOverIcon = icon;
        popover.classList.add("inlineBubble_active");
        popover.style.top = (this._mouseClientY - (rect.height / 2)) + "px"; // position to mouseclick minus helf it's height (to center)
        popover.style.left = (this._mouseClientX+50) + "px";

        // Remove after a few seconds
        window.setTimeout(
            () => {
                popover.classList.remove("inlineBubble_active");
            }, 5000
        );
    }
    _handlePopoverClose(){
        const popover = this.template.querySelector('.inlineBubble');
        popover.classList.remove("inlineBubble_active");
    }
    /**
     * Ticks all checkboxes on the treegrid depending on action type
     */
    _tickAllCheckboxes(gridData){
        const action = this.template.querySelector('.actionCombobox').value;
        if(action){
            let allAccounts = gridData.map(d => d.id);
            let allAtm = gridData.reduce((atms, row) => {
                if(row._children?.length > 0){
                    atms = [...atms, ...row._children.map(c => c.id)];
                }
                return atms;
            }, []);
            switch(action){
                case CONSTANTS.ACTION_TYPES.ADD:
                    // Only Accounts
                    this._gridSelectedRows = allAccounts;
                    break;
                case CONSTANTS.ACTION_TYPES.UPDATE:
                case CONSTANTS.ACTION_TYPES.REMOVE:
                    // Only Account Team Members
                    this._gridSelectedRows = allAtm;
                    break;
            }
        }
        // update count
        this._selectionCount = this._gridSelectedRows.length;

        // Update button access
        this._enableDisableSubmit(action, this._gridSelectedRows);
    }
    
}