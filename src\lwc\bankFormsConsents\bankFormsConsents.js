import { LightningElement, track, wire, api } from 'lwc';
import { loadStyle } from "lightning/platformResourceLoader";
import { NavigationMixin } from 'lightning/navigation'; //For Navigation
import { ShowToastEvent } from "lightning/platformShowToastEvent";
import bankFormsCss from '@salesforce/resourceUrl/bankFormsConsentsCSS'; //CSS from static resource


// Apex controller methods
import getFormConsents from '@salesforce/apex/BankFormsConsentController.getFormConsents';
import getRedirectURL from '@salesforce/apex/BankFormsConsentController.getRedirectURL';
import FORM_FACTOR from '@salesforce/client/formFactor';

export default class BankFormsConsents extends NavigationMixin(LightningElement) {

    formName = '';
    @track _source = FORM_FACTOR === 'Large' ? 'Desktop' : 'myAMP App';
    @track pendingConsentList = [];
    @track withPendingConsents = true;
    @track dfrId;
    @track redirectURL;

    @api
	get source(){}

	set source(value){
        console.log('get source: ', value);
		this._source = value;
	}

    selectedConsent = [];
    pageNumber = 1; //default page is first page

    constructor() {
        super();
        Promise.all([
            loadStyle(this, bankFormsCss)
        ]).then(() => {
            // console.log("Loaded css");
        }).catch(error => {
            console.error('Error loading static resources', error);
        });
    }

    connectedCallback() {
        console.log('connectedCallback _source: ', this._source);
        getRedirectURL({ source: this._source })
            .then(data => {
                this.isLoaded = !this.isLoaded;
                if (data) {
                    console.log('data: ', data);
                    this.redirectURL = data;
                }

            }).catch(error => {
                console.error('Error in getRedirectURL: ', error);
                this.hasError = true;
                this.showToast('An unexpected error occurred', 'error', 'Please contact AMP Bank.');
            });

        getFormConsents()
            .then(data => {
                if (data) {
                    for (let consent of data) {
                        //array of pending consent
                        this.pendingConsentList.push(consent);
                    }

                    //if there are no pending consents, we set this variable to false in template
                    if (this.pendingConsentList.length === 0) {
                        this.pendingConsentList = false;
                    }

                } else {
                    this.hasError = true;
                    console.error('Error fetching pending consents: ', 'No data recieved');
                    this.showToast('An unexpected error occurred when retrieving pending consents', 'error', 'Please contact AMP Bank.');
                }

            }).catch(error => {
                console.error('Error fetching pending consents: ', error);
                this.hasError = true;
                this.showToast('An unexpected error occurred when retrieving pending consents', 'error', 'Please contact AMP Bank.');
            });

    }

    get showWithPendingConsents() {
        return this.withPendingConsents;
    }

    handleContinue(event) {
        //move to second page
        event.preventDefault();
        this.dfrId = event.currentTarget.dataset.id;
        this.pageNumber = this.pageNumber + 1;

        //Find the element in the available fa and set it to selected
        for (let i = 0; i < this.pendingConsentList.length; i++) {
            if (this.pendingConsentList[i].dfr.Id === event.currentTarget.dataset.id) {
                this.selectedConsent = this.pendingConsentList[i];
                break;
            }
        }

    }

    handlePages() {
        //handle event to return to first page from second page
        //decrement current page number by 1 (authorisation page)
        this.pageNumber = this.pageNumber - 1;
    }

    //getter to render page by page number
    get renderPageNumber() {
        let renderPage;
        if (this.pageNumber === 1) {
            renderPage = true;
        }
        else {
            renderPage = false;
        }
        return renderPage;
    }

    handleClose(event) {
        console.log('redirectURL: ', this.redirectURL);
        window.location.href = this.redirectURL;
    }

    //handle toast events
    showToast(toastTitle, toastVariant, toastMessage) {
        this.dispatchEvent(
            new ShowToastEvent({
                title: toastTitle,
                variant: toastVariant,
                message: toastMessage
            }),
        );

        //if error occurs, we keep the spinner loading
        if (toastVariant === 'error') {
            //this.loaded = false;
        }
    }

}