<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <compactLayoutAssignment>SYSTEM</compactLayoutAssignment>
    <enableFeeds>false</enableFeeds>
    <externalSharingModel>ControlledByParent</externalSharingModel>
    <fields>
        <fullName>AuthorizationFormId</fullName>
        <trackHistory>false</trackHistory>
        <type>MasterDetail</type>
    </fields>
    <fields>
        <fullName>ContentDocumentId</fullName>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>External_ID__c</fullName>
        <caseSensitive>true</caseSensitive>
        <description>SFP-54367 - Used for mapping on config data load</description>
        <encryptionScheme>None</encryptionScheme>
        <externalId>true</externalId>
        <label>External ID</label>
        <length>255</length>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>true</unique>
    </fields>
    <fields>
        <fullName>FullAuthorizationFormUrl</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>IsActive</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>IsReadOnly</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>LastActivationDateTime</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>Locale</fullName>
    </fields>
    <fields>
        <fullName>Name</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>SummaryAuthFormText</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <listViews>
        <fullName>All_AuthorizationFormText</fullName>
        <filterScope>Everything</filterScope>
        <label>All Authorization Form Text</label>
    </listViews>
    <searchLayouts>
        <customTabListAdditionalFields>AuthorizationForm</customTabListAdditionalFields>
        <customTabListAdditionalFields>SummaryAuthFormText</customTabListAdditionalFields>
        <customTabListAdditionalFields>LocaleSelection</customTabListAdditionalFields>
        <customTabListAdditionalFields>LastModifiedDate</customTabListAdditionalFields>
        <customTabListAdditionalFields>LastModifiedBy</customTabListAdditionalFields>
        <searchResultsAdditionalFields>AuthorizationForm</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>SummaryAuthFormText</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>LocaleSelection</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>LastModifiedDate</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>LastModifiedBy</searchResultsAdditionalFields>
    </searchLayouts>
    <sharingModel>ControlledByParent</sharingModel>
</CustomObject>
