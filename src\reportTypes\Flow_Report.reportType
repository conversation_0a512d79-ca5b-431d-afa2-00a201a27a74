<?xml version="1.0" encoding="UTF-8"?>
<ReportType xmlns="http://soap.sforce.com/2006/04/metadata">
    <baseObject>FlowInterviewLog</baseObject>
    <category>other</category>
    <deployed>true</deployed>
    <description>OOTB Report type that will be use as data source for Flow Reporting</description>
    <join>
        <outerJoin>false</outerJoin>
        <relationship>FlowInterviewLogs</relationship>
    </join>
    <label>Flow Report</label>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedBy</field>
            <table>FlowInterviewLog</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedDate</field>
            <table>FlowInterviewLog</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FlowDeveloperName</field>
            <table>FlowInterviewLog</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FlowInterviewGuid</field>
            <table>FlowInterviewLog</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Id</field>
            <table>FlowInterviewLog</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FlowLabel</field>
            <table>FlowInterviewLog</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FlowNamespace</field>
            <table>FlowInterviewLog</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FlowVersionNumber</field>
            <table>FlowInterviewLog</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>InterviewDurationInMinutes</field>
            <table>FlowInterviewLog</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>InterviewEndTimestamp</field>
            <table>FlowInterviewLog</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>InterviewStartTimestamp</field>
            <table>FlowInterviewLog</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>InterviewStatus</field>
            <table>FlowInterviewLog</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedBy</field>
            <table>FlowInterviewLog</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedDate</field>
            <table>FlowInterviewLog</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedBy.Golden_SCVID__c</field>
            <table>FlowInterviewLog</table>
        </columns>
        <masterLabel>Flow Interview Logs</masterLabel>
    </sections>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedBy</field>
            <table>FlowInterviewLog.FlowInterviewLogs</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedDate</field>
            <table>FlowInterviewLog.FlowInterviewLogs</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DurationSinceStartInMinutes</field>
            <table>FlowInterviewLog.FlowInterviewLogs</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ElementApiName</field>
            <table>FlowInterviewLog.FlowInterviewLogs</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ElementDurationInMinutes</field>
            <table>FlowInterviewLog.FlowInterviewLogs</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ElementLabel</field>
            <table>FlowInterviewLog.FlowInterviewLogs</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Id</field>
            <table>FlowInterviewLog.FlowInterviewLogs</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedBy</field>
            <table>FlowInterviewLog.FlowInterviewLogs</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedDate</field>
            <table>FlowInterviewLog.FlowInterviewLogs</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LogEntryTimestamp</field>
            <table>FlowInterviewLog.FlowInterviewLogs</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LogEntryType</field>
            <table>FlowInterviewLog.FlowInterviewLogs</table>
        </columns>
        <masterLabel>Flow Interview Log Entries</masterLabel>
    </sections>
</ReportType>
