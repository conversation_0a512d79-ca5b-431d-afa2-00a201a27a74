<template> 
  <div class="slds-box slds-theme_default backgroundColor-grey">
    <template if:false={isDeclined}>
      <template if:true={renderPageNumber}>
        <p class="header-style" style="padding-top: 33px;">{formName}</p>
        <p class="subheader-style">Review and authorise changes</p>
        <div class="formContainer">

          <div class="slds-scoped-notification slds-media slds-media_center slds-scoped-notification_light"
            role="status" style="margin-bottom: 24px;">
            <div class="slds-media__figure">
              <lightning-icon icon-name="utility:info" size="small">
              </lightning-icon>
            </div>
            <div class="slds-media__body">
              <lightning-formatted-rich-text class="scoped-notification-style"
                value="AMP Bank may need to contact you to discuss the information in this form. We may need you to call us back to verify the request before it can be fulfilled. To avoid potential delays in the processing of your request you should promptly return our call.">
              </lightning-formatted-rich-text>
            </div>
          </div>

          <lightning-formatted-rich-text disable-linkify value={caseNoteTable}></lightning-formatted-rich-text>

          <div if:true={isPartialDischarge}>
            <div class="slds-scoped-notification slds-media slds-media_center slds-theme_warning"
              role="status" style="margin-bottom: 24px;">
              <div class="slds-media__figure">
                <lightning-icon icon-name="utility:warning" size="small">
                </lightning-icon>
              </div>
              <div class="slds-media__body">
                <lightning-formatted-rich-text class="scoped-notification-style"
                  value="Please ensure the BSB and account number are correct as AMP Bank do not verify the payment details you provide. If these details are incorrect, funds will not be credited to the correct account and we may not be able to recover the funds.">
                </lightning-formatted-rich-text>
              </div>
            </div>

          </div>

          <div class="slds-col slds-p-top_small slds-p-right_small footer-button-style">
            <button type="button" class="close-button-style slds-button slds-button_neutral"
              onclick={openModal}>Decline</button>
            <button type="button" class="close-button-style slds-button slds-button_brand buttonSpace"
              onclick={handleContinue}>Authorize</button>
          </div>

        </div>

        <template if:true={isModalOpen}>
          <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container modalContainerWidthStyle">

              <header class="slds-modal__header">
                <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" title="Close"
                  onclick={closeModal}>
                  <lightning-icon icon-name="utility:close" alternative-text="close" variant="inverse" size="small">
                  </lightning-icon>
                  <span class="slds-assistive-text">Close</span>
                </button>

                <div class="slds-hyphenate modalHeaderStyle">Are you sure?</div>
              </header>

              <div class="modalContentStyle">
                By confirming, you agree that this request won't be submitted and all the information the other account
                holder completed will be lost.
                <br><br>Do you confirm you want to decline this request?
              </div>

              <div class="modalFooterStyle">
                <button class="slds-button slds-button_neutral" onclick={closeModal}
                  style="padding: 10px 24px; border: 1px solid; border-color: #C6C6C8; height: 44px;">No,
                  go back</button>
                <button class="slds-button slds-button_brand buttonSpace" onclick={submitDetails}
                  style="padding: 10px 24px; height: 44px;">Yes, I confirm</button>
              </div>

            </div>

          </section>

          <div class="slds-backdrop slds-backdrop_open"></div>
        </template>

      </template>

      <template if:false={renderPageNumber}>
        <c-bank-forms-impt-info-and-declaration onshowpages={handlePages} selected-consent={selectedConsent} redirect-url={redirectUrl}>
        </c-bank-forms-impt-info-and-declaration>
      </template>
    </template>

    <template if:true={isDeclined}>
      <c-bank-forms-thank-you-screen onshowpages={handlePages} is-declined={isDeclined}
        selected-consent={selectedConsent} redirect-url={redirectUrl}>
      </c-bank-forms-thank-you-screen>
    </template>

  </div>

</template>