<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Interaction_Type__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Previous_Interaction__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Sequential_Completion_Required__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Assign_To__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Assign_To_User__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Assign_All_Task_to_Same_User__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Template_Sharing__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Sync_Accounts_from_XPLAN__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Expose_to_Servicing_Practice__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Status__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Next_Interaction__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Assign_To_Role__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Calculate_Days_From__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>How_many_days_before_this_date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Servicing_Practice__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Unique_per_client__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Unique_per_type__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Original_Global_Interaction_Template__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Available_to_Apply_Interaction__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <relatedLists>
        <fields>No__c</fields>
        <fields>NAME</fields>
        <fields>Due_Date__c</fields>
        <fields>Method__c</fields>
        <fields>Priority__c</fields>
        <fields>Is_Required__c</fields>
        <relatedList>Action_Plan_Template_Item__c.Action_Plan_Template__c</relatedList>
        <sortField>No__c</sortField>
        <sortOrder>Asc</sortOrder>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Ongoing_Services__c</fields>
        <fields>Servicing_Practice__c</fields>
        <fields>Initiation_Date__c</fields>
        <fields>Last_Due_Date__c</fields>
        <fields>OWNER.ALIAS</fields>
        <relatedList>Action_Plan__c.Action_Plan_Template__c</relatedList>
        <sortField>Servicing_Practice__c</sortField>
        <sortOrder>Asc</sortOrder>
    </relatedLists>
    <relatedLists>
        <fields>OPPORTUNITY.NAME</fields>
        <fields>ACCOUNT.NAME</fields>
        <fields>OPPORTUNITY.STAGE_NAME</fields>
        <fields>OPPORTUNITY.PROBABILITY</fields>
        <fields>Products_API__c</fields>
        <fields>Products_AUM__c</fields>
        <fields>OPPORTUNITY.RECORDTYPE</fields>
        <fields>OPPORTUNITY.CLOSE_DATE</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <relatedList>Opportunity.Action_Plan_Template__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Action_Plan_Template__c.Next_Interaction__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Action_Plan_Template__c.Previous_Interaction__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>CREATEDBY_USER</fields>
        <fields>UPDATEDBY_USER</fields>
        <fields>LAST_UPDATE</fields>
        <relatedList>Interaction_Template_History__c.Interaction_Template__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Job_Interaction__c.InteractionTemplateId__c</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00h5P000000ciRl</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
