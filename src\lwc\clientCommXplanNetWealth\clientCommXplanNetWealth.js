/**
 * Created by jerry.poon on 21/06/2021.
 */ 

import {LightningElement, api} from 'lwc';
import getAssets from '@salesforce/apex/ClientCommXplanService.getXplanAsset';
import getLiabilities from '@salesforce/apex/ClientCommXplanService.getXplanLiability';
import {loadStyle} from "lightning/platformResourceLoader";
import css from '@salesforce/resourceUrl/clientCommXplanStyle';

export default class ClientCommXplanNetWealth extends LightningElement {
    assetDetails;
    liabilityDetails;
    assetLoaded = false;
    liabilityLoaded = false;
    loaded = false;
    netPosition = null;
    netPositionCurrency = 0.00;

    @api
    get loadSuccess() {
        return this.loaded && this.assetDetails && this.liabilityDetails;
    }
    @api
    get loadFail() {
        return this.loaded && (!this.assetDetails || !this.liabilityDetails);
    }

    //Load css
    constructor() {
        super();
        Promise.all([
            loadStyle(this, css)
        ]).then(() => {
            console.log("Styling loaded");
        }).catch(error => {
            console.error('Error loading static resources', error);
        });
    }

    //get xplan data
    connectedCallback() {
        Promise.all([getAssets(), getLiabilities()])
            .then((values) => {
                //return is null if xplan id is null
                if(values[0] != null && values[1] != null) {
                    //get assets
                    this.processAssets(JSON.parse(values[0]));
                    //get liabilities
                    this.processLiabilities(JSON.parse(values[1]));
                    this.loaded = true;
                } else {
                    this.assetLoaded = true;
                    this.liabilityLoaded = true;
                }
            })
            .catch(error => {
                this.errorMsg = 'Error: ' + error.message;
                this.assetLoaded = true;
                this.liabilityLoaded = true;
        });
    }

    processAssets(response) {
        if(response && response.isSuccess) {
            //loop through and sum the value
            this.assetDetails = JSON.parse(response.result);
            this.assetDetails.forEach((asset) => {
                if(asset.Current_Value__c) {
                    this.netPosition = this.netPosition == null ? asset.Current_Value__c : (this.netPosition + asset.Current_Value__c);
                }
            });
        } else {
            this.errorMsg = response.errorMessage;
        }
        this.netPositionCurrency = this.formatter.format(this.netPosition);
        this.assetLoaded = true;
    }
    processLiabilities(response) {
        if(response && response.isSuccess) {
            this.liabilityDetails = JSON.parse(response.result);
            //Subtract from net wealth position
            this.liabilityDetails.forEach((liability) => {
                if(liability.Current_Value__c) {
                    this.netPosition = this.netPosition == null ? -(liability.Current_Value__c) : (this.netPosition - liability.Current_Value__c);
                }
            });
        }
        this.netPositionCurrency = this.formatter.format(this.netPosition);
        this.liabilityLoaded = true;
    }
    formatter = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'AUD',
    });
}