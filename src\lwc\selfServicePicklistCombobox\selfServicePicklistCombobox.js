import { LightningElement, api, wire } from 'lwc';
import getPicklistOptions from '@salesforce/apex/SelfServicePicklistDataService.getPicklistOptions';

export default class SelfServicePicklistCombobox extends LightningElement {
    @api recordId;
    @api fieldName;
    @api objectName;
    @api fieldLabel;
    @api picklistRT;
    @api picklistObject;
    @api picklistField;
    @api showNoneOption;

    _readOnly = false;
    _placeholderText;
    _picklistOptions = [];
    _initialPicklistValue;
    _picklistValue;
    _picklistRecordMap = {}; // Picklist value to Self_Service_Picklist__c record map.

    /** API methods */
    @api
    get initialPicklistValue(){
        return this._initialPicklistValue;
    }
    set initialPicklistValue(_initialPicklistValue){
        this._picklistValue = this._initialPicklistValue = _initialPicklistValue;

        if(this._initialPicklistValue){
            // If initial value is not a valid option add it as an option and re-sort
            let isInitialValueValid = this._picklistOptions.find(o => this._initialPicklistValue === o.value);
            if(!isInitialValueValid){
                this._picklistOptions.push({label : this._initialPicklistValue, value: this._initialPicklistValue});
                this._sortOptions();
            }

            // Trigger value change event
            if(Object.keys(this._picklistRecordMap).length){
                this._dispatchRecordValueChangeEvent(this._picklistRecordMap[this._initialPicklistValue], false);
            }
        }
    }

    @api 
    get readOnly(){
        return this._readOnly;
    }
    set readOnly(_readOnly){
        this._readOnly = _readOnly;
    }

    @api 
    get placeholderText(){
        return this._placeholderText;
    }
    set placeholderText(_placeholderText){
        this._placeholderText =(_placeholderText) ? _placeholderText : "Select a "+this.fieldLabel;
    }

    @api
    reset(){
        this._picklistValue = this._initialPicklistValue;
    }

    @api
    clear(){
        this._picklistValue = null;
    }

    /**
     * @returns String : The current selected value (Name field from Self_Service_Picklist__c)
     */
    @api
    getValue(){
        return this._picklistValue;
    }

    /**
     * @returns Object : The current selected value in Self_Service_Picklist__c record form.
     */
    @api 
    getValueRecord(){
        return this._picklistRecordMap[this._picklistValue];
    }

    @wire(getPicklistOptions, { fieldName: '$fieldName', objectName: '$objectName' })
    handleGetPicklistOptions({ error, data }) {
        if (data) {
            let isInitialValueValid = !this._initialPicklistValue;
            if(Array.isArray(data)){
                this._picklistOptions =  data.map( o => {
                    if(!isInitialValueValid && this._initialPicklistValue === o.Name){
                        isInitialValueValid = true;
                    }
                    return {label : o.Name, value : o.Name};
                });
                // If initial value is not a valid option add it as an option and re-sort
                if(!isInitialValueValid){
                    this._picklistOptions.push({label : this._initialPicklistValue, value: this._initialPicklistValue});
                    this._sortOptions();
                }
                // Populate map
                this._picklistRecordMap = data.reduce((optionMap, o) => {
                    optionMap[o.Name] = o;
                    return optionMap
                }, {});
                // Trigger value change event
                if(this._initialPicklistValue){
                    this._dispatchRecordValueChangeEvent(this._picklistRecordMap[this._initialPicklistValue], false);
                }
            }
            if(this.showNoneOption){
                this._picklistOptions.unshift({label : "--None--", value: ''});
            }
            
        } else if (error) {
            console.error(error);
        }
    }

    /** Handlers */
    _handleChange(event){
        this._picklistValue = event.detail.value;
        this._dispatchRecordValueChangeEvent(this._picklistRecordMap[event.detail.value], true);
    }

    _dispatchRecordValueChangeEvent(picklistValueRecord, clear){
        this.dispatchEvent(new CustomEvent("controllingrecordvaluechange", {
            detail : {
                    "controllingRecord" : picklistValueRecord,
                    "clearValue" : clear
            }
        }));
    }

    _sortOptions(){
        this._picklistOptions.sort((a,b) => {
            const nameA = a.label.toUpperCase(); // ignore upper and lowercase
            const nameB = b.label.toUpperCase(); // ignore upper and lowercase
            if (nameA < nameB) {
                return -1;
            }
            if (nameA > nameB) {
                return 1;
            }

            // names must be equal
            return 0;
        });
    }
}