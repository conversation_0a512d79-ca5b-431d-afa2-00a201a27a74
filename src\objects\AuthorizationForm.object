<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <compactLayoutAssignment>SYSTEM</compactLayoutAssignment>
    <enableFeeds>false</enableFeeds>
    <externalSharingModel>Private</externalSharingModel>
    <fields>
        <fullName>AssociatedRegulation</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>ConsentDurationDayCount</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>DefaultAuthFormTextId</fullName>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>EffectiveFromDate</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>EffectiveToDate</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>External_ID__c</fullName>
        <caseSensitive>true</caseSensitive>
        <description>SFP-54367 - Used for mapping on config data load</description>
        <encryptionScheme>None</encryptionScheme>
        <externalId>true</externalId>
        <label>External ID</label>
        <length>255</length>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>true</unique>
    </fields>
    <fields>
        <fullName>IsSignatureRequired</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>Name</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>RevisionNumber</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <listViews>
        <fullName>All_AuthorizationForms</fullName>
        <filterScope>Everything</filterScope>
        <label>All Authorization Forms</label>
    </listViews>
    <searchLayouts>
        <customTabListAdditionalFields>RevisionNumber</customTabListAdditionalFields>
        <customTabListAdditionalFields>EffectiveFromDate</customTabListAdditionalFields>
        <customTabListAdditionalFields>EffectiveToDate</customTabListAdditionalFields>
        <customTabListAdditionalFields>LastModifiedDate</customTabListAdditionalFields>
        <customTabListAdditionalFields>LastModifiedBy</customTabListAdditionalFields>
        <searchResultsAdditionalFields>RevisionNumber</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>EffectiveFromDate</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>EffectiveToDate</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>LastModifiedDate</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>LastModifiedBy</searchResultsAdditionalFields>
    </searchLayouts>
    <sharingModel>Private</sharingModel>
</CustomObject>
