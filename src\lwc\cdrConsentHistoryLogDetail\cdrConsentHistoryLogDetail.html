<!--   
- Author        : <PERSON><PERSON><PERSON> (Accenture) SFP-32835
- Date          : September 26, 2024
- Description   : Added as part of consent History requriments. SFP-78754.
-->
<template> 
    <div class="content__body">
        <br/>
        <!-- loading -->
        <template if:false={loaded}>
            <lightning-spinner class="loading slds-is-relative" alternative-text="Loading" size="large" style="position: fixed;"></lightning-spinner>
        </template>
        <template if:true={loaded}>
            <c-cdr-header back-enabled="true"></c-cdr-header>
            <template if:true={isRecordFound}>
                <div>
                    <br/>
                    <h2 class="slds-text-align_left header-spacing font-style_light-bold" style="position: relative;">Consent History Details</h2>
                    <h4 class="slds-text-align_left header-spacing font-style_light-bold" style="position: relative;">{currentScopeRecord.startDate}</h4>
                </div>
                <!-- Data recipient information -->
                <fieldset class="content__body-main-dashboard">
                    <!--<legend class={currentScopeRecord.consent.legend_color}><p>{currentScopeRecord.consent.status}</p></legend> -->
                        <div class="slds-media slds-var-p-left_large slds-var-p-right_large slds-var-p-bottom_large slds-var-p-top_small">
                            <div class="slds-media__figure slds-media__figure_fixed-width slds-align_absolute-center slds-var-p-top_xx-small">
                                <div class="">
                                    <div class="slds-welcome-mat__tile-icon-container">
                                        <span class="slds-icon_container">
                                            <div class="slds-icon slds-icon-text-default">
                                                <img src={currentScopeRecord.consent.recipientLogo} class="arrangement-image" alt="company logo">
                                            </div>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="slds-media__body slds-grid slds-grid_vertical-align-center slds-var-p-top_xx-small">
                                <div class="slds-col">
                                    <h4 class="inline-text">{currentScopeRecord.consent.recipientName}</h4>
                                    <p class="slds-text-align_left text-gray consent-owner-text"> 
                                        Consent granted by <span class="consent-owner">{currentScopeRecord.consent.consentOwner}</span>:&nbsp;{currentScopeRecord.consent.startDate}
                                    </p>
                                </div>
                            </div>
                        </div>
                </fieldset>
                <!-- Data sharing information -->
                <template lwc:if={active}>
                    <h3 class="slds-text-align_left header-spacing font-style_light-bold data-shared-header">What you've shared
                        <template lwc:if={isAmended}>
                            <div class="amended-badge-wrap">
                                <span class="slds-badge slds-theme_inverse lsds-var-m-right_small status-amended">Amended</span>
                            </div>
                        </template>
                    </h3>
                </template>
                <template lwc:else>
                    <h3 class="slds-text-align_left header-spacing font-style_light-bold data-shared-header">Data we've shared
                    <template lwc:if={isAmended}>
                        <div class="amended-badge-wrap">
                            <span class="slds-badge slds-theme_inverse lsds-var-m-right_small status-amended">Amended</span>
                        </div>
                    </template>
                   </h3>
                </template>

                <div class="">
                    <template for:each={dataClusterToPermLangMap} for:item="mapKey">
                            <ul class="" key={mapKey.key}>
                                <li class="slds-var-p-bottom_x-small">
                                    <c-cdr-data-shared-section key={mapKey.key} map-key={mapKey.key} value={mapKey.value} header={mapKey.header} 
                                            body={mapKey.body} consent-start={currentScopeRecord.startDate} consent-end={currentScopeRecord.endDate} recipient-name={currentScopeRecord.consent.recipientName} 
                                            status={onceoff} ></c-cdr-data-shared-section>
                                </li>
                            </ul>
                    </template>
                </div>
                <br/>
               
                <!-- Key dates information -->
                <div class="content__body-main-dashboard-shadow slds-var-p-left_large slds-var-p-right_large slds-var-p-top_large slds-var-p-bottom_large slds-border_bottom slds-border_right slds-border_left">
                    <h4 class="inline-text">Key dates </h4>
                        <template lwc:if={isKeyDatesAmended}>
                            <div class="amended-badge-wrap">
                                <span class="slds-badge slds-theme_inverse lsds-var-m-right_small status-amended">Amended</span>
                            </div>
                        </template>
                    <br>
                    <div>
                        <template if:true={onceoff}>
                            <div class="slds-grid slds-grid_vertical-align-center slds-var-p-top_medium">
                                <div class="slds-p-right_small slds-size_1-of-1 slds-medium-size_1-of-3 slds-large-size_1-of-3">
                                    <p class="text-gray">Consent given</p> <!-- SFP-62336 -->
                                </div>
                                <div class="slds-size_1-of-1 slds-medium-size_2-of-3 slds-large-size_2-of-3">
                                    <h6 class="slds-text-align_left slds-var-p-left_medium">{currentScopeRecord.consent.startDate}</h6>        
                                </div>
                            </div>
                            <div class="slds-grid slds-grid_vertical-align-center slds-var-p-top_medium">
                                <div class="slds-p-right_small slds-size_1-of-1 slds-medium-size_1-of-3 slds-large-size_1-of-3">
                                    <p class="text-gray">Consent expires</p>   <!-- SFP-62336 -->
                                </div>
                                <div class="slds-size_1-of-1 slds-medium-size_2-of-3 slds-large-size_2-of-3">
                                    <h6 class="slds-text-align_left slds-var-p-left_medium">{currentScopeRecord.originalEndDate}</h6>        
                                </div>
                            </div>
                            <div class="slds-grid slds-grid_vertical-align-center slds-var-p-top_medium">
                                <div class="slds-p-right_small slds-size_1-of-1 slds-medium-size_1-of-3 slds-large-size_1-of-3">
                                    <p class="text-gray">Sharing Period</p>
                                </div>
                                <div class="slds-size_1-of-1 slds-medium-size_2-of-3 slds-large-size_2-of-3">
                                    <h6 class="slds-text-align_left slds-var-p-left_medium">Your data will be shared once  </h6>        
                                </div>
                            </div>
                        </template>                      
                        <template if:false={onceoff}>
                            <div class="slds-col">
                                <div class="slds-grid slds-wrap slds-grid_vertical-align-center slds-var-p-top_medium">
                                    <div class="slds-p-right_small slds-size_1-of-1 slds-medium-size_1-of-3 slds-large-size_1-of-3">
                                        <p class="text-gray float_none">Consent given</p>  <!-- SFP-62336 -->
                                    </div>
                                    <div class="slds-size_1-of-1 slds-medium-size_2-of-3 slds-large-size_2-of-3">
                                        <h4 class="slds-text-align_left font-style_light-bold ds-text-font-style">{currentScopeRecord.consent.startDate}</h4>
                                    </div>
                                </div>
                            </div>
                            <template if:true={active}>
                                <div class="slds-col">
                                    <div class="slds-grid slds-wrap slds-grid_vertical-align-center slds-var-p-top_medium">
                                        <div class="slds-p-right_small slds-size_1-of-1 slds-medium-size_1-of-3 slds-large-size_1-of-3 slds-var-p-right_large">
                                            <p class="text-gray float_none">Consent expires</p>   <!-- SFP-62336 -->
                                        </div>
                                        <div class="slds-size_1-of-1 slds-medium-size_2-of-3 slds-large-size_2-of-3">
                                            <h4 class="slds-text-align_left font-style_light-bold ds-text-font-style">{currentScopeRecord.originalEndDate}</h4>
                                        </div>
                                    </div>
                                </div>
                            </template>
                            <template if:true={expired}>
                                <div class="slds-col">
                                    <div class="slds-grid slds-wrap slds-grid_vertical-align-center slds-var-p-top_medium">
                                        <div class="slds-p-right_small slds-size_1-of-1 slds-medium-size_1-of-3 slds-large-size_1-of-3">
                                            <p class="text-gray float_none">Consent expires</p>   <!-- SFP-62336 -->
                                        </div>
                                        <div class="slds-size_1-of-1 slds-medium-size_2-of-3 slds-large-size_2-of-3">
                                            <h4 class="slds-text-align_left font-style_light-bold ds-text-font-style">{currentScopeRecord.consent.endDate}</h4>
                                        </div>
                                    </div>
                                </div>
                            </template>
                            <template if:true={withdrawn}>
                                <div class="slds-col">
                                    <div class="slds-grid slds-wrap slds-grid_vertical-align-center slds-var-p-top_medium">
                                        <div class="slds-p-right_small slds-size_1-of-1 slds-medium-size_1-of-3 slds-large-size_1-of-3">
                                            <p class="text-gray float_none">When you withdrew your consent</p>
                                        </div>
                                        <div class="slds-size_1-of-1 slds-medium-size_2-of-3 slds-large-size_2-of-3">
                                            <h4 class="slds-text-align_left font-style_light-bold ds-text-font-style">{currentScopeRecord.consent.revocationDate}</h4>
                                        </div>
                                    </div>
                                </div>
                            </template>   
                            <div class="slds-grid slds-wrap slds-grid_vertical-align-center slds-var-p-top_medium">
                                <div class="slds-p-right_small slds-size_1-of-1 slds-medium-size_1-of-3 slds-large-size_1-of-3">
                                    <p class="text-gray float_none">Sharing period</p>
                                </div>
                                <div class="slds-size_1-of-1 slds-medium-size_2-of-3 slds-large-size_2-of-3">
                                    <template if:true={active}>
                                        <h4 class="slds-text-align_left font-style_light-bold ds-text-font-style">{currentScopeRecord.consent.startDate} - {currentScopeRecord.originalEndDate}</h4>
                                    </template>
                                    <template if:true={expired}>
                                        <h4 class="slds-text-align_left font-style_light-bold ds-text-font-style">{currentScopeRecord.consent.startDate} - {currentScopeRecord.originalEndDate}</h4>
                                    </template>
                                    <template if:true={withdrawn}>
                                        <h4 class="slds-text-align_left font-style_light-bold ds-text-font-style">{currentScopeRecord.consent.startDate} - {currentScopeRecord.originalEndDate}</h4>
                                    </template>                                
                                </div>
                            </div>                           
                        </template>
                    </div>
                </div>
            </template>
            <template if:false={isRecordFound}>
                <h2 class="slds-text-align_left header-spacing font-style_light-bold">{errorMessage}</h2>
            </template>
        </template>
    </div>
</template>