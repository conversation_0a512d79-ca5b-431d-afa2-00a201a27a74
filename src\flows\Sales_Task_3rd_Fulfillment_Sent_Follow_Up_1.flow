<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <decisions>
        <name>Check_Task_1</name>
        <label>Check Task 1st</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Check_Task_2</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>FALSE_Check Task 1st</defaultConnectorLabel>
        <rules>
            <name>TRUE_myRule_1</name>
            <conditionLogic>7 AND (1 OR 2) AND 3 AND 4 AND 5 AND 6</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Direction__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Inbound</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Direction__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Outbound</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Completed</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Attempt_Counts__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>1</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Related_Opportunity__r.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>AMP_Assist_Sales</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Related_Opportunity__r.StageName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Fulfilment Sent</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.WhatId</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>006</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Opp_1</targetReference>
            </connector>
            <label>TRUE_Check Task 1st</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Task_2</name>
        <label>Check Task 2nd</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Check_Task_3</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>FALSE_Check Task 2nd</defaultConnectorLabel>
        <rules>
            <name>TRUE_myRule_4</name>
            <conditionLogic>8 AND (1 OR 2) AND 3 AND 4 AND 5 AND 6 AND 7</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Direction__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Inbound</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Direction__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Outbound</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Completed</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Related_Opportunity_Stage__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Fulfillment Sent</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Attempt_Counts__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>2</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Related_Opportunity__r.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>AMP_Assist_Sales</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Related_Opportunity__r.StageName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Fulfilment Sent</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.WhatId</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>006</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Opportunity_Task_2</targetReference>
            </connector>
            <label>TRUE_Check Task 2nd</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Task_3</name>
        <label>Check Task 3rd</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>FALSE_Check Task 3rd</defaultConnectorLabel>
        <rules>
            <name>TRUE_myRule_8</name>
            <conditionLogic>8 AND (1 OR 2) AND 3 AND 4 AND 5 AND 6 AND 7</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Direction__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Inbound</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Direction__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Outbound</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Completed</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Related_Opportunity_Stage__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Fulfillment Sent</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Attempt_Counts__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>3</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Related_Opportunity__r.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>AMP_Assist_Sales</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Related_Opportunity__r.StageName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Fulfilment Sent</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.WhatId</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>006</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Opportunity_Task_3</targetReference>
            </connector>
            <label>TRUE_Check Task 3rd</label>
        </rules>
    </decisions>
    <description>Migrated from the Sales - Task 3rd Fulfillment Sent Follow Up process using multiple criteria.
Creates a tasks (3rd Fulfillment Send Follow Up) when Sales opportunity is moved to Fulfillment Sent</description>
    <environments>Default</environments>
    <formulas>
        <name>formula_5_myRule_1_A2_9016628346</name>
        <dataType>Date</dataType>
        <expression>TODAY() + 2</expression>
    </formulas>
    <formulas>
        <name>formula_6_myRule_4_A2_9924874520</name>
        <dataType>Date</dataType>
        <expression>TODAY() + 2</expression>
    </formulas>
    <formulas>
        <name>formula_7_myRule_4_A2_0825906100</name>
        <dataType>String</dataType>
        <expression>{!$Record.WhatId}</expression>
    </formulas>
    <formulas>
        <name>formula_8_myRule_8_A2_6221862050</name>
        <dataType>Date</dataType>
        <expression>TODAY() + 2</expression>
    </formulas>
    <label>Sales - Task 3rd Fulfillment Sent Follow Up_1</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <name>Create_Fullfillment_Sent_FollowUp_Task</name>
        <label>Create Fulfillment Sent Follow-Up Task</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>formula_5_myRule_1_A2_9016628346</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Attempt_Counts__c</field>
            <value>
                <stringValue>2</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Direction__c</field>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.Owner:User.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <stringValue>Normal</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>01290000000nX2nAAE</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Related_Opportunity_Stage__c</field>
            <value>
                <stringValue>Fulfillment Sent</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Not Started</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>2nd follow up on Fulfillment</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.WhatId</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_Fullfillment_Sent_FollowUp_Task2</name>
        <label>Create Fulfillment Sent Follow-Up</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>formula_6_myRule_4_A2_9924874520</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Attempt_Counts__c</field>
            <value>
                <stringValue>3</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Direction__c</field>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.Owner:User.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <stringValue>Normal</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>01290000000nX2nAAE</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Related_Opportunity_Stage__c</field>
            <value>
                <stringValue>Fulfillment Sent</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Not Started</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>3rd follow up on Fulfillment</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>formula_7_myRule_4_A2_0825906100</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_Fullfillment_Sent_FollowUp_Task3</name>
        <label>Create Fulfillment Sent Follow-Up</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>formula_8_myRule_8_A2_6221862050</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Attempt_Counts__c</field>
            <value>
                <stringValue>4</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Direction__c</field>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.Owner:User.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <stringValue>Normal</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>01290000000nX2nAAE</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Related_Opportunity_Stage__c</field>
            <value>
                <stringValue>Fulfillment Sent</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Not Started</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>4th follow up on Fulfillment</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.WhatId</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordUpdates>
        <name>Update_Opp_1</name>
        <label>Update Opportunity Task - 1st</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Create_Fullfillment_Sent_FollowUp_Task</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Attempt_Count__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>1</stringValue>
            </value>
        </filters>
        <filters>
            <field>StageName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Fulfilment Sent</stringValue>
            </value>
        </filters>
        <filters>
            <field>Task_Type__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Sales</stringValue>
            </value>
        </filters>
        <inputAssignments>
            <field>Attempt_Count__c</field>
            <value>
                <stringValue>2</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record.Related_Opportunity__r</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Opportunity_Task_2</name>
        <label>Update Opportunity Task - 2nd</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Create_Fullfillment_Sent_FollowUp_Task2</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Attempt_Count__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>2</stringValue>
            </value>
        </filters>
        <filters>
            <field>StageName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Fulfilment Sent</stringValue>
            </value>
        </filters>
        <filters>
            <field>Task_Type__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Sales</stringValue>
            </value>
        </filters>
        <inputAssignments>
            <field>Attempt_Count__c</field>
            <value>
                <stringValue>3</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record.Related_Opportunity__r</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Opportunity_Task_3</name>
        <label>Update Opportunity Task - 3rd</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Create_Fullfillment_Sent_FollowUp_Task3</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Attempt_Count__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>3</stringValue>
            </value>
        </filters>
        <filters>
            <field>StageName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Fulfilment Sent</stringValue>
            </value>
        </filters>
        <filters>
            <field>Task_Type__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Sales</stringValue>
            </value>
        </filters>
        <inputAssignments>
            <field>Attempt_Count__c</field>
            <value>
                <stringValue>4</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record.Related_Opportunity__r</inputReference>
    </recordUpdates>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Check_Task_1</targetReference>
        </connector>
        <object>Task</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
