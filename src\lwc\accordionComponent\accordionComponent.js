import { api, LightningElement } from 'lwc';
import accordionEnablerCSS from '@salesforce/resourceUrl/accordionEnablerCSS';
import { loadStyle } from 'lightning/platformResourceLoader';

export default class accordionComponent extends LightningElement {

    connectedCallback() {
        loadStyle(this, accordionEnablerCSS);
    }

    activeSections = ['A'];
    @api isCardVisible;
    @api sectionHeading1;
    @api sectionText1;
    @api sectionHeading2;
    @api sectionText2;
    @api sectionHeading3;
    @api sectionText3;
    @api sectionHeading4;
    @api sectionText4;
    @api sectionHeading5;
    @api sectionText5;
    @api sectionHeading6;
    @api sectionText6;
    @api sectionHeading7;
    @api sectionText7;
    @api sectionHeading8;
    @api sectionText8;
    @api sectionHeading9;
    @api sectionText9;
    @api sectionHeading10;
    @api sectionText10;
    @api sectionHeading11;
    @api sectionText11;
    @api allowMultipleSectionsOpen;

}