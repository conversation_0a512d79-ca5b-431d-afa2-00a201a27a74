<?xml version="1.0" encoding="UTF-8"?> 
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>DataDotComClean</excludeButtons>
    <excludeButtons>RequestUseSfdc</excludeButtons>
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Contact Details</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FinServ__PreferredName__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FinServ__Gender__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Birthdate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Best_Address__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Email</field>
            </layoutItems>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Alternate_Email__c</field>
            </layoutItems>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>HomePhone</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MobilePhone</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Phone</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Fax</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>AccountId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Unclaimed_Money__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Excluded_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Deceased__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Centrelink_Number_DVA__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FinServ__Age__c</field>
            </layoutItems>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Email_Invalid__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Email_Address_In_Invalid_Format__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Alternate_Email_Invalid__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Alternate_Email_Address_In_InvalidFormat__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Home_Phone_Invalid__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Mobile_Invalid__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Phone_Invalid__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Fax_Invalid__c</field>
            </layoutItems>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Permanently_Excluded_from_payouts_to_ATO__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Excluded_By__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Date_of_Death__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Description Information</label>
        <layoutColumns/>
        <style>OneColumn</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
        <platformActionListItems>
            <actionName>FinServ__NewEvent</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>0</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__NewTaskClientAssociate</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>1</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Contact.Call_log</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>2</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Sales_Comms_Log</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>3</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Contact.New_Case</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>4</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewOpportunity</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>5</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>EnableCustomerPortal</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>6</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>DisableCustomerPortal</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>7</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>LoginToNetworkAsUser</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>8</sortOrder>
        </platformActionListItems>
    </platformActionList>
    <relatedLists>
        <relatedList>RelatedEntityHistoryList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>CAMPAIGN.NAME</fields>
        <fields>CAMPAIGN.START_DATE</fields>
        <fields>CAMPAIGN.CAMPAIGN_TYPE</fields>
        <fields>CM.STATUS</fields>
        <fields>CM.RESPONDED</fields>
        <fields>CM.LAST_UPDATE</fields>
        <relatedList>RelatedCampaignList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>STATUS</fields>
        <fields>COMPANY</fields>
        <fields>TITLE</fields>
        <fields>MEMBER_TYPE</fields>
        <fields>LAST_UPDATE</fields>
        <relatedList>CampaignMember.Adviser_Name__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>FinServ__ContactContactRelation__c.FinServ__Contact__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Preference_Center__c.Contact_v2__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Notification_of_Death__c.Account__c</relatedList>
    </relatedLists>
    <relatedObjects>AccountId</relatedObjects>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00h8r000003qUl3</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
