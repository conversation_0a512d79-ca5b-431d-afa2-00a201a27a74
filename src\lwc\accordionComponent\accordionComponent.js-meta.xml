<?xml version="1.0" encoding="UTF-8"?>
<LightningComponentBundle xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>52.0</apiVersion>
    <isExposed>true</isExposed>
    <targets>
        <target>lightningCommunity__Page</target>
        <target>lightningCommunity__Default</target>
        <target>lightning__RecordPage</target>
        <target>lightning__FlowScreen</target>
        <target>lightning__HomePage</target>
        <target>lightning__AppPage</target>
    </targets>
    <targetConfigs>
        <targetConfig targets="lightning__RecordPage,lightning__AppPage,lightning__HomePage,lightning__FlowScreen">
            <property name="allowMultipleSectionsOpen" type="Boolean" default="false" label="Allow Multiple Sections Open" />
            <property name="isCardVisible" type="Boolean" default="true" label="Toggle Card Visibility" />
            <property name="sectionHeading1" type="String" default="" label="Section 1 Heading" />
            <property name="sectionText1" type="String" default="" label="Section 1 Text" />
            <property name="sectionHeading2" type="String" default="" label="Section 2 Heading"/>
            <property name="sectionText2" type="String" default="" label="Section 2 Text"/>
            <property name="sectionHeading3" type="String" default="" label="Section 3 Heading" />
            <property name="sectionText3" type="String" default="" label="Section 3 Text"/>
            <property name="sectionHeading4" type="String" default="" label="Section 4 Heading"/>
            <property name="sectionText4" type="String" default="" label="Section 4 Text"/>
            <property name="sectionHeading5" type="String" default="" label="Section 5 Heading"/>
            <property name="sectionText5" type="String" default="" label="Section 5 Text"/>
            <property name="sectionHeading6" type="String" default="" label="Section 6 Heading"/>
            <property name="sectionText6" type="String" default="" label="Section 6 Text"/>
            <property name="sectionHeading7" type="String" default="" label="Section 7 Heading"/>
            <property name="sectionText7" type="String" default="" label="Section 7 Text"/>
            <property name="sectionHeading8" type="String" default="" label="Section 8 Heading"/>
            <property name="sectionText8" type="String" default="" label="Section 8 Text"/>
            <property name="sectionHeading9" type="String" default="" label="Section 9 Heading"/>
            <property name="sectionText9" type="String" default="" label="Section 9 Text"/>
            <property name="sectionHeading10" type="String" default="" label="Section 10 Heading"/>
            <property name="sectionText10" type="String" default="" label="Section 10 Text"/>
            <property name="sectionHeading11" type="String" default="" label="Section 11 Heading"/>
            <property name="sectionText11" type="String" default="" label="Section 11 Text"/>
        </targetConfig>
    </targetConfigs>
</LightningComponentBundle>