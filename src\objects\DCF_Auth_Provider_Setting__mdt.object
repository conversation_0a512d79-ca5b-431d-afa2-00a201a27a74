<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <fields>
        <fullName>Callback_Url__c</fullName>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>Callback Url</label>
        <length>255</length>
        <required>false</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Certificate_Unique_Name__c</fullName>
        <description>Certificate used in this process</description>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>Certificate Unique Name</label>
        <length>255</length>
        <required>false</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Client_Id__c</fullName>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>Client Id</label>
        <length>255</length>
        <required>false</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Encoded_Thumbprint__c</fullName>
        <description>Base 64 encodedThumbprint can be obtained using following command (run this command in terminal):

echo $(openssl x509 -in Sharepoint_DCF_Cert.crt -fingerprint -noout) | sed &apos;s/SHA1 Fingerprint=//g&apos; | sed &apos;s/://g&apos; | xxd -r -ps | base64

Note: Sharepoint_DCF_Cert.crt is used example for the self-signed certificate name created and downloaded from salesforce.</description>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>Encoded Thumbprint</label>
        <length>255</length>
        <required>false</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Tenant_Id__c</fullName>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>Tenant Id</label>
        <length>255</length>
        <required>false</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Token_Valid_For_Seconds__c</fullName>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <inlineHelpText>Value in Seconds</inlineHelpText>
        <label>Token Valid For Seconds</label>
        <length>20</length>
        <required>false</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <label>DCF Auth Provider Setting</label>
    <pluralLabel>DCF Auth Provider Settings</pluralLabel>
    <visibility>Public</visibility>
</CustomObject>
