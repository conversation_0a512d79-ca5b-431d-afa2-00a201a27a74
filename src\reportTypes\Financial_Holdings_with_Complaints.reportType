<?xml version="1.0" encoding="UTF-8"?>
<ReportType xmlns="http://soap.sforce.com/2006/04/metadata">
    <baseObject>FinServ__FinancialHolding__c</baseObject>
    <category>other</category>
    <deployed>true</deployed>
    <description>Financial Holdings with Complaints</description>
    <join>
        <outerJoin>false</outerJoin>
        <relationship>Complaints__r</relationship>
    </join>
    <label>Financial Holdings with Complaints</label>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Id</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>true</checkedByDefault>
            <field>Name</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>RecordType</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedDate</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedBy</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedDate</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedBy</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastActivityDate</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__FinancialAccount__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__AssetCategoryName__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__AssetCategory__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__AssetClass__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__GainLoss__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Household__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__LastUpdated__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__MarketValue__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__PercentChange__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Price__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__PrimaryOwner__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__PurchasePrice__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Securities__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Shares__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__SourceSystemId__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Symbol__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>APIR_Code__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Created_By_Internal__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Holding_Percent__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Initial_Investment_Amount__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Initial_Unit_Price__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Initial_Units__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Investment_Amount__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Pending_Amount__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Pie_Tax_Amount__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Restricted_AMP__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Restricted_Practice__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Servicing_Practice__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SourceContractIdentifier__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SourceInvestmentIdentifier__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Staff_Super__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Superannuation_Sub_Account__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Unique_Holding__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Balance_Calculation__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>IsDeleted__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Australian_shares_Funds__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Cash_Funds__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Interest_Rate__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>International_shares_Funds__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>APIR_Code_Del__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Effective_Date__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Gross_Investment_amount__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Initial_Price__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Initial_Unit_Amount__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Investment_Unit_Amount__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Morning_Star_Report__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Net_Amount__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Pie_Tax_Units__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Reinvest_Ind__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Security_Name__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Type__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Unit_Price__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Value__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Last_Effective_Date__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Managed_Portfolio_ID__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <masterLabel>Financial Holdings</masterLabel>
    </sections>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Id</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Owner</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>true</checkedByDefault>
            <field>Name</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>RecordType</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedDate</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedBy</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedDate</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedBy</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastActivityDate</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case_Number__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>AFCA_Rules_Extension__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint_Insurance_Dispute_Number__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint_Insurance_Dispute_Reason__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint_Insurance_Dispute_Type__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Created_By_Team__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>EDR_Closed_Status__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>EDR_Outcome_Type__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>EDR_Outcome__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>EDR_Potential_Systemic_Issue__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>EDR_Scheme_Reference__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>EDR_Scheme__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Account_Name__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Insurance_Benefit_Type__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Insurance_Dispute_Outcome_Date__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Insurance_Dispute_Outcome__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Insurance_Dispute_Status__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Insurance_Frequency_of_Sum_Insured__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Insurance_Party_In_Dispute__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Insurance_Reinsurance__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Insurance_Reinsurer__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Insurance_Sum_Insured__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Insurance_Unresolved_Dispute_Reason__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Litigation_AMP_Legal_Reference_Number__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Litigation_Dispute_Outcome_Date__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Litigation_Dispute_Outcome__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Litigation_Dispute_Status__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Litigation_Dispute_Type__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Amount_Recouped_From_Adviser__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Amount_Recouped_from_PI_Insurer__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Amount_in_Dispute__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Business_Area__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Business_Sub_Area__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Caller_Type__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case_Owner_Team__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case_Owner__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Contract__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Cause__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Class_of_Business__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Classification__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Company__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Compensation__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint_AFCA_IDR_Referral__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint_Category__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint_Compensation_Requested__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint_Contact_Details__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint_Discretionary_Payment_Amount__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint_Entity__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint_External_Product__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint_Level__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint_Other_External_Reference_Numbe__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint_Outcome_Requested_by_Customer__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint_Outcome__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint_Potential_Breach_Item__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint_Potential_Vulnerability_Type__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint_Product_Type__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint_Received_Date__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint_SpecialAssistance_CareNeed__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint_SpecialAssistance_CareNeeded__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint_Sub_Category__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint_Trust_or_Platform__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint_Type__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Cost_To_Licensee__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Description__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Diarised_Until__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Financial_Account__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>First_Name__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ID_Check_Result__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>IsEscalated__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Last_Name__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Link_to_PRM_Adviser__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Link_to_PRM_Practice__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>My_AMP__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Non_product_related_Cannot_find_Contract__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Origin__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Other_Impacted_Areas__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Outcome_Description__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Parent_Case__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Potential_Adviser_Exposure__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Potential_Licensee_Exposure__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Potential_PI_Exposure__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Priority__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Product_Sub_Type__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Product_Team__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>REM_AE_Adviser__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>REM_Licensee__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Raised_By__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Related_To__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Root_Cause__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Servicing_Practice__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Settlement_Amount__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Status__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Subject__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Yes_Plan_Employed__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Status_Transition_Date__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case_Age_Reopen_Duration__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case_Age__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case_Owner_Complaint__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case_Owner_Team_Complaint__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Check_fields_on_close__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>APRA_Reporting_Section_FinAcc__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Advice_Related_Information__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Benefit_Name__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ComplaintIs_AFCA_IDR_Referral_And_Closed__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>EDR_Complaint_Closed__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinAcc_Blank__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Litigation_Complaint_Closed__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Outcome_Description_Length__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Previous_Status__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ProductSubType__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SimpleIDR_for_Legal_and_Cust_Advocate__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Contact_Preference__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Status_Transition_Comments__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>The_Complaint_is_about__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>AMP_Bank_Role__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>AMP_Responsibility_Blank_Check__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>AMP_Responsibility_Fully_Check__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>AMP_Responsibility_Not_Responsible_Check__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>AMP_Responsibility_Partially_Check__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>AMP_Responsibility__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Adviser_AMC_Name__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Adviser_AMC_Not_Found__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Adviser_Not_Found__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>BCCC_Age_Bracket__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>BCCC_Cause__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>BCCC_Cycle_Time__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>BCCC_Issue__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>BCCC_Regulatory_Reporting_Check__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>COBisBankingProduct__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Credit_Bureau__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Credit_Provider__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Date_Complaint_Received_By_3rd_Party__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Days_In_Between_Closed_Status__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Direction__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Historic_Adviser_Check__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Historic_Adviser__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Legacy_External_Reference_ID__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Legacy_System__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Privacy_Related_Complaint__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Sync_with_PRM__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>X3rd_Party_s_Complaint_Response_Due_Date__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Affects_more_than_1_Customer_PSI__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Affects_more_than_1_customer_check__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CCR_Complaint_Level_IDR_C_Check__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Investment_Details_check__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Other_Impacted_Entities__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Driver_Cause_Blank_Check__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Projects_Events__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Adviser_AMC_Removed__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Commercial_Settlement__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint_Uplift_Date__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>EDR_QP_Vetting_Section_Conditional__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>IDRReviewOutcomeCheck__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>IDR_Review_Outcome__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ID_Check_Result_New__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Policy_Cancelled__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Processed_By__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Related_To_IDR__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Status_Reason__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Transaction_Approved_By__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Vetting_Type_Question_Conditional__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint_Milestone_Criteria__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint_Regulatory_Condition__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Delay_Other__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Delay_Reasons__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Delay_Scenario__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>GRC_Reference__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Submitted_Online__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ComplaintChecklist__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Total_no_of_Investments__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Date_of_Resolution__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Days_to_Resolution__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Additional_PSI_information__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Has_Potential_Incident__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Unknown_Potential_Incident__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Outcome_Offered__c</field>
            <table>FinServ__FinancialHolding__c.Complaints__r</table>
        </columns>
        <masterLabel>Complaints</masterLabel>
    </sections>
</ReportType>
