<template> 
    <lightning-layout multiple-rows>
        <lightning-layout-item size="5">
            <div class="slds-p-around_xx-small">
                <c-lookup
                    class="fromLookup"
                    selection={_initialSelection}
                    errors={_lookupErrors}
                    onsearch={handleLookupSearch}
                    onselectionchange={handleLookupSelectionChange}
                    placeholder="Search User"
                    disabled={disableTeamMemberLookup}
                >
                </c-lookup>
            </div>
        </lightning-layout-item>
        <lightning-layout-item size="7">
            <div class="slds-p-around_xx-small">
                <template if:false={_isActionTransfer}>
                    <lightning-combobox
                        placeholder="Select a Team Role"
                        options={_teamRoleOptions}
                        onchange={_handleRoleChange} 
                        variant="label-hidden"></lightning-combobox>
                </template>
                
                <template if:true={_isActionTransfer}>
                    <c-lookup
                        class="toLookup"
                        selection={_toLookupSelection}
                        errors={_toLookupErrors}
                        onsearch={handleToLookupSearch}
                        onselectionchange={handleLookupSelectionChange}
                        placeholder="Search User"
                    >
                    </c-lookup>
                </template>
            </div>
        </lightning-layout-item>
    </lightning-layout>
</template>