<?xml version="1.0" encoding="UTF-8"?>
<ReportType xmlns="http://soap.sforce.com/2006/04/metadata">
    <baseObject>FinServ__FinancialHolding__c</baseObject>
    <category>other</category>
    <deployed>true</deployed>
    <description>This report type will extract the financial holdings, financial accounts, investment details cases and complaints for securities</description>
    <join>
        <outerJoin>false</outerJoin>
        <relationship>Investment_Details__r</relationship>
    </join>
    <label>Cases and Complaints with Investments</label>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>APIR_Code__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>APIR_Code_Del__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__AssetCategory__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__AssetCategoryName__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__AssetClass__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Australian_shares_Funds__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Balance_Calculation__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Cash_Funds__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedBy</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Created_By_Internal__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedDate</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Effective_Date__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Last_Effective_Date__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__FinancialAccount__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Id</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>true</checkedByDefault>
            <field>Name</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__GainLoss__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Gross_Investment_amount__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Holding_Percent__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Household__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Initial_Investment_Amount__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Initial_Unit_Amount__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Initial_Unit_Price__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Initial_Price__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Initial_Units__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Interest_Rate__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>International_shares_Funds__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Investment_Amount__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastActivityDate</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedBy</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedDate</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__LastUpdated__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__MarketValue__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Morning_Star_Report__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Security_Name__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Net_Amount__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Pending_Amount__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__PercentChange__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Pie_Tax_Amount__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Pie_Tax_Units__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Price__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__PrimaryOwner__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__PurchasePrice__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>RecordType</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Reinvest_Ind__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>IsDeleted__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Restricted_AMP__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Restricted_Practice__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Securities__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Servicing_Practice__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__SourceSystemId__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Staff_Super__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Superannuation_Sub_Account__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Symbol__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Type__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Unique_Holding__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SourceContractIdentifier__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SourceInvestmentIdentifier__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Investment_Unit_Amount__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Unit_Price__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Shares__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Value__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Securities__c.FinServ__SecuritiesName__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Securities__c.Security_Name__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <masterLabel>Financial Holdings</masterLabel>
    </sections>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedBy</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedDate</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Current_or_Previous_Investment__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Id</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>true</checkedByDefault>
            <field>Name</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Investment_Name_U2__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedBy</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedDate</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Product_APIR_Code_AMP_Capital__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Product_Name_AMP_Capital__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Product_Option_Code__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SMA_code__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Investment__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Outcome_Offered__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <masterLabel>Investment Details</masterLabel>
    </sections>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Declaration__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.X24_Hours_Case_SLA__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Account_Balance__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Account</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Account_Name__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.PARTY_Account_Name__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.AccountJoined__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Account_Name_Text__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Acknowledged__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Acquiring_Practice_ID__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_AE_ACR_Number__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.ActiveOrderCheck__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_AE_Is_The_Adviser_Being_Terminated__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Additional_Information__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Additional_Information_Other__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Additional_notes__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Admin_Specialist__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Intrafund_Advice_Topics__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Rem_Advice_Category__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.ServiceSelectionQ4__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Advice_conversation_and_lead_generated__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Advice_Fee_Compensated__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Advice_Role__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Advice_Role_FirstName__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Advice_Role_LastName__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Advice_Type__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Adviser__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_AE_Adviser__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Adviser__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Adviser_Adviser_Group__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Adviser_Email__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Practice_Adviser_Contact_ID__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Adviser_Phone__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Adviser_protocol_conducted__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Adviser_Review_Status__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_ART_Adviser_Selected__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Adviser_State__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Complaint_AFCA_IDR_Referral__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_AE_AFS_FAR_AR_No__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Servicing_Adviser__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Servicing_Practice__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Allocated_Practice_Reporting_Channel__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Work_Volumes__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.AMP_Bank_Home_Loan_conversation__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.ServiceSelectionQ2__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.AMP_Product__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.AMP_Source__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Any_other_form_enquiries_received__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Application_Number__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Apply_Online_Offered__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Area_Product__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.FeeSelection1__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.FeeSelection3__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.ARM_Team_Leader__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Assessment_Outcome__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Assessment_Peer_Review_Outcome__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Assessment_Peer_Review_Status__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Assessment_Peer_Review_User__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Assessment_Priority__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Assessment_Quality_Assurance__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Assessment_Quality_Assurance_Status__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Assessment_Quality_Assurance_User__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Assessment_Quality_Check__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Assessment_QC_Status__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Assessment_Quality_Check_User__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Assessment_Status__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Asset</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Assigned_to_TCS__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_State__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Batch_Job_Id__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Beneficiaries_form_provided__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Best_time_to_call_customer_From__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Best_time_to_call_customer_To__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Better_account_conversation__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Billing_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.BOLR_Assist_Owner_changed__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.BA_BOLR_Case_Number__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.BOLR_eForm_ID__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.BOLR_Email_Reference_Id__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.BOLR_Send_Rejected_Email__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.BA_BOLR_Status__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.BOLR_Sub_Phase_Assessment__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.BOLR_Sub_Phase_Post_Settlement__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.BOLR_Sub_Phase_Pre_Settlement__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.BOLR_Sub_Phase_Settlement__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.BOLR_Sub_Phase_Strategy__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.BPMS_Mini_Enrichment_Failed__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.BPMS_Owner__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Business_Area__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Business_Channel_Servicing_Practice__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Bussiness_Email__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.BusinessHours</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Business_Sub_Area__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Calculated_Owner_ID__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Calculated_Payee_ID__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Calculation_Complexity__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Calculation_Grading__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Calculations_High_priority_Reason__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Calculations_Priority__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Calculation_QA_Remaining_Days__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Calculations_QA_1_Manager__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Calculations_QA_2_Manager__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Calculations_QA_3_Manager__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Calculation_Quality_Assurance__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Calculation_QA_Outcome__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Calculation_Quality_Assurance_Status__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Calculation_Quality_Assurance_User__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Calculation_Quality_Check_Status__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Calculation_Remaining_Days__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Calculation_Request__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Calculation_Request_Received__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Calculations_Specialist__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Calculation_Status__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Caller_Name__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Caller_Type__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Call_Recording_Id__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Cancelled_Reason__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Case_Activity__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Case_Age__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Case_Age_Business_Hours__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Case_Allocation_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_AE_Case_Closure_Comments__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.BOLR_Case_Closure_Reason__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Case_Complexity__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Id</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Case_Integration_Status__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Case_is_On_Hold__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.CaseNumber</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Case_Off_Hold_Comments__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Case_On_Hold_Comments__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_On_Hold_Reason__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Origin</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Owner</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Case_Owner__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Case_Owner_LAN_ID__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.CaseOwnerName__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Case_Owner_Team__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Case_Progression_Percent__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Reason</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.RecordType</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Case_Support_Reason__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Case_Support_SLA__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Case_Support_SLA_Met__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Case_Support_Type__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Case_Team__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Category__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Cause__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Channel__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Choice_Conversation__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Claim_Number__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Classification__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Class_of_Business__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.ServiceSelectionQ5__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Client__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Client_Engagement__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Client_FAs__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Client_Testimony_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Client_Transfer__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.IsClosed</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Closed_Before_Due__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.IsClosedOnCreate</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.COB_Classification__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.FeePermission__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_AE_Commencement_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_AE_Commencement_Date_AMP__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Commission_Split_Update_Completed__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Company__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Company_Practice__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Complaint_Compensation_Requested__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Complaint__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.ComplaintAU_Alert_Message__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Complaint_Category__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Complaint_Level__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Complaint_Stage__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Complaint_Sub_Category__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Complaint_Type__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Completion_Time__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Completion_Time_TCS__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Compliance_Category__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Consolidation__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Contact_Address_1__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Contact_Address_2__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Complaint_Contact_Details__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Contact</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Contact_Name__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.PARTY_Contact_Name__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Contact_Phone__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Contact_Role_Picklist__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Contact_Type__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Contract__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Contribution_increase_conversation__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Converted_from_Service_Request__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Case_Count__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Country_of_Origin__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.CreatedBy</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Created_By__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Created_By_First_Name__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Created_By_Team__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Created_From_Email__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Credit_Card__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.CS_SLA_Met_is_FALSE__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.CS_SLA_Met_is_TRUE__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.ClosedDate</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.CreatedDate</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Date_Time_Opened__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Date_allocated__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Date_Allocated_To_User__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Date_Case_Joined_Current_Sub_Stage__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Date_Complaint_Closed__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Date_of_Equifax_request__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Date_of_Transfer__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Date_Outcome_Letter_Sent__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Date_Release_Agreement_Sent__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Days_Allocated_To_User__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_AE_Days_in_Adviser_Engagement__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Days_in_Current_Stage__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Days_In_Sub_Stage__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Days_With_File_Build__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Days_With_File_Retrieval__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.DeclineReason__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Description</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Diarised_Status__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Diarised_Until__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Differing_Client_Name__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Differing_Client_Name_Comment__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Digital_Form_Record__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Direct_Financial_Loss__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.ProductPlatform1__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Complaint_Discretionary_Payment_Amount__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Disposing_Practice__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Practice_Distribution_Channel__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Existing_Adviser__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Do_Not_Pursue_Reason__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Do_services_need_to_be_switched_off__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Root_Cause__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Due_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Due_Status__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Duplicate_Case_Required__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Duplicate_Reason__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Duration__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_AE_Ecosystem__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Electronic_Consents__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Employer_Scheme__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Engage_Client__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Entitlement</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.SlaExitDate</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.SlaStartDate</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Complaint_Entity__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Equifax_Request_Submitted__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.IsEscalated</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Referral_Case_Escalation__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Estimated_Completion_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.ProductPlatform2__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.ProductPlatform3__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Exit_Pathway_Number__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.FeeSelection5__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Complaint_External_Product__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Fee_Dial_Down_Completed__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.FeeSelection4__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_File_Build_Commencement_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.File_Build_Quality_Check__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_File_Build_Quality_Check_Status__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.File_Build_Quality_Check_User__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.File_Build_Status__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_File_Build_Status_Change_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_File_Build_Status_End_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.File_Build_User__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_File_Retrieval_Commencement_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.File_Retrieval_Status__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_File_Retrieval_Status_Change_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_File_Retrieval_Status_End_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.File_Retrieval_User__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.FinServ__FinancialAccount__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Financial_Account__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Account_Number__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Financial_Account_Primary_Owner_Name_Txt__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Financial_Account_Product__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Financial_Account_Product_Family__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.First_Name__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Follow_up_actions__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.FSG_Provided__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.General_advice_warning_provided__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Goals_360_base_advise_conversation__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Grading__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Complaint_Group__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Group__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Group_Account_Contacts__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Handoff_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Handoff_Note__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Handoff_Reason__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_AE_Has_The_Case_Been_Put_On_Hold__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_High_Priority_Comments__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_High_Priority_Reason__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Home_Phone__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.FinServ__Household__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.ID_Check_Result__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Inappropriate_Gearing_Compensated__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Inappropriate_Ins_Prem_Compensated__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Inappropriate_TTR_Compensated__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Initial_Assessment_QA_Received_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Initial_Calculation_QA_Received_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Initial_File_Build_QC_Received_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Initial_Letter_Send_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Insurance_Advice_Remediation__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Insurance_Claimed_to_be_Paid_Out__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Insurance_increase_conversation__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Insurance_Policy_Issued_Amount__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Insurance_Required__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Integration_Acknowledged_Count__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Investment_Choice_conversation__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Is_Staff_Super_Account__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Incident_Report__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_AE_IsTheAdviserLookingToSellPractice__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.IsToBeDownloaded__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Key_Risk_Indicator__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Key_Volume_Indicator__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_AE_Known_Adviser_Themes__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.No_of_Clients_In_Scope__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.KVI_Description__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Language</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.LastModifiedBy</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.LastModifiedDate</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Last_Name__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Letter_Options__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Licensee__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.LifeInsured1__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Loan_Purpose__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Location__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Location_of_Original_Files__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Manager_of_Adviser_Eng__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Maturities_Contract_Number__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.ServiceSelectionQ1__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Member_Security_Check__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Milestone_Criteria__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Misappropriation_Funds_Compensated__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.MOS_Due_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Multi_Application_Id__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.My_AMP__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.ProductPlatform5__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Due_Date_MOS__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.New_member__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.ProductPlatform4__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Non_KRI_Scope__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Non_product_related_Cannot_find_Contract__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_No_of_Calculation_QA_Staff__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.CaseClosureComments__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Not_Moving_all_of_Client_s_FAs__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Number_of_Assessment_QA_Recheck__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Number_of_Assessment_Recheck__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Number_of_Assessment_Rework__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Number_of_Clients_Triaged__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Number_of_Open_Legal_Review_Requests__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Num_of_Submitted_Transfer_Records_v2__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Number_of_times_diarised__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Offer_Status__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.OGS_Fee_Refund_Date_From__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.OGS_Fee_Refund_Date_To__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.OMS_Scope__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.FeeCommission2__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Online_Registration__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Opportunity__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Other__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Complaint_Other_External_Reference_Numbe__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Other_Impacted_Areas__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Complaint_Outcome__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Outcome__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Outcome_Description__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Outcome_Letter__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Tech_Outcome_letter_Ready_to_send__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Outcome_Letter_Review_Status__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Complaint_Outcome_Requested_by_Customer__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Paraplanner_Name__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Paraplanning_Billing_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Parent</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Parent_Case__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Parent_Request_ID2__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_AE_Partnership_Manager__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.FeeSelection2__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Payment_System__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Peer_Reviewer__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Performance_Amount__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Performed_Using_Calculations_Model__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.PSOutstanding__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.BOLR_Phase__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Financial_Account_Plan_Name__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Planner__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Allocated_Adviser_Primary_Adviser_ID__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Policy_Number__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.PolicyOwner2__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.ProductPlatform6__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Postal_Address__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Complaint_Potential_Breach_Item__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Complaint_Potential_Vulnerability_Type__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.PP_Case_Owner_Name__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Practice__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Practice_Access_Revocation_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Adviser_Contact_Practice_ID__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_AE_Practice_Principal__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Pre_Transfer_Checks_Completed__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Preventative_Action__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Tech_CalculationsQA1_Outcome__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.ServiceSelectionQ3__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Campaign__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Prioritise_this_case__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Priority</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Product</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Product_NZ__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Product_Category__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Product_Code__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Product_Owner__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Product_Reporting_NZ__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Product_Sub_Type__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Product_Team__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Complaint_Product_Type__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Product_Worktype_Map__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Full_Review_QA_Team_Leader__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Quality_Assurance_Outcome__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Raised_By__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Reason__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Reason_for_closed_or_reopened_case__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Reason_for_closure__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Reason_for_the_prioritised_case__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Reassignment_in_Progress__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Complaint_Received_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Complaint_Received_Date_Time__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Record_Type__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.RecordTypeChange__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Release_Rectification_Status__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REMRectificationAdvice__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_AE_Referral_Source__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Referral_Type__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Compensation__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Re_indexed__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Re_indexed_Cancelled_Comments__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Rejected__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Rejected_By__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Reject_Reason__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Related_To__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Release_Agreement_Cover_Letter__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Complaint_35_Email_Sent__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Remediation_Manager__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Template_Group__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Reporting_Status__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Request_Approval_From__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Alternative_Contact__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Request_ID__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Request_Id_for_Claim_Case__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Requesting_Adviser__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Requesting_Practice__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Request_MDC__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Requestor__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Response_Instruction__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Response_Task__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Restricted_Financial_Account__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Returned_to_File_Build__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Returned_to_File_Build_Reason__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Returned_to_File_Retrieval__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Returned_to_File_Retrieval_Reason__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Re_Work__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Reworked_Request__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Salary_Sacrifice_conversation__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Sales__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Salesforce_Ref_ID_One__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Salesforce_Ref_ID_Two__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Sales_ID__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Scope_Date_From__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Scope_Date_To__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.SCVID_Formula__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Second_Policy_Owner__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Adviser_Practice_Segment__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.BOLR_PracticeName__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Sent_Outcome_Letter__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.ServiceContract</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Service_fees_still_being_charged__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Service_Request_Category__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.ServiceSelectionL1__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.ServiceSelectionL2__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.ServiceSelectionL3__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Servicing_Adviser_ID__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Servicing_Practice_Party_Access__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Session_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Session_Type__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Set_Case_to_Read_Only__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.SLA__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.SLA_Met__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_SMSF_Compensated__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Source__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Source_of_Files__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Lack_of_Confidence_Source__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.BPMS_Created_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Source_System_ID__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Complaint_SpecialAssistance_CareNeed__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Complaint_SpecialAssistance_CareNeeded__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Special_conditions__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Specify_the_reason_below_OTHER_ONLY__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Spouse_Family_account_conversation__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Stage__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.State__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Status</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_AE_Status_Authority__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Status_Change_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.IsStopped</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.StopStartDate</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Stopping_OSF_confirmation_date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Sub_Category__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Subject</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Sub_Stage__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Sub_Status__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.SubStatus__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Sub_Type__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Sub_Worktype__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Super_Option_Compensated__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Super_Search_Requested__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Suppress_Integration_Message__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Task_Identifier__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.TCS_SLA_Tier_1__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.TCS_SLA_Tier_1_Met__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.TCS_SLA_Tier_2__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.TCS_SLA_Tier_2_Met__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Team__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_TECH_MRQ_SoftClose__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_TECH_OutcomeAppropriate_Next_Version__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_TECH_Outcome_Appropriate_Version__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_TECH_Outcome_Letter_Next_Version__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_TECH_Outcome_Letter_Version__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_TECH_Outcome_Soft_Close_Next_Version__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_TECH_Outcome_Soft_Close_Version__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_TECH_Release_Agreement_Next_Version__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_TECH_Release_Agreement_Version__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_TECH_Short_Release_Agreement_Version__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_TECH_Short_Release_Next_Version__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.TECH_Admin_Specialist_Prior_Value__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_TECH_Closed_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_TECH_Days_in_Full_Review__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_TECH_Days_in_Implementation__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_TECH_Full_Review_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_TECH_Implementation_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Tech_Outcome_Letter__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_TECH_Softclose_Status_Capture__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_TECH_Total_days_in_Assessment__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Tech_TPP_Doc_Sent_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_AE_Termination_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.TFN_disclosure_provided__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Third_Party_Name__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_ART_Third_Party_Interaction__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Total_Num_of_Transfer_Records_v2__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Total_Compensation_Amount__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Total_ex_Gratia_Amount__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Total_No_of_Clients_In_Book__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Total_Remediation_Paid__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Total_Remediation_Payment_Amount__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Total_Tax_Amount__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Tracking__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Tracking_ID__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Transfer__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Transfer_Adviser_Client_Base__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Transferring_Adviser__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Transfer_Successful__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Transfer_Type__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Triage_Status_Completion__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Complaint_Trust_or_Platform__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Type</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Type_of_Calculation__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Type_of_Calculation_Other__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.UWFlag__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_AE_UniqueCase__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.ProductPlatform7__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Update_Practice__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.FeeCommission1__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.User_Role__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Variance_Dollar__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Variance_Percent__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.ProductPlatform8__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.SuppliedCompany</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.SuppliedEmail</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.SuppliedName</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.SuppliedPhone</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Which_Letter_To_Send__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Which_Letter_to_Send__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_Which_Outcome_letter__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Won_Lost_Reason_Opportunity__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_AE_Worked_From__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_AE_Worked_To__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Work_Phone__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.REM_AE_Workstream__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Type__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Worktype__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Product_Worktype_Code__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Worktype_Flag__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Case__c.Yes_Plan_Employed__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <masterLabel>Case Lookup</masterLabel>
    </sections>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.X3rd_Party_s_Complaint_Response_Due_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Account_Name__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Advice_Related_Information__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.REM_AE_Adviser__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Adviser_AMC_Name__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Adviser_AMC_Not_Found__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Adviser_AMC_Removed__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Adviser_Not_Found__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Complaint_AFCA_IDR_Referral__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.AFCA_Rules_Extension__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Affects_more_than_1_Customer_PSI__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Affects_more_than_1_customer_check__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Servicing_Practice__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Amount_in_Dispute__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Amount_Recouped_From_Adviser__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Amount_Recouped_from_PI_Insurer__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.AMP_Bank_Role__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Litigation_AMP_Legal_Reference_Number__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.AMP_Responsibility__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.AMP_Responsibility_Blank_Check__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.AMP_Responsibility_Fully_Check__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.AMP_Responsibility_Not_Responsible_Check__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.AMP_Responsibility_Partially_Check__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.APRA_Reporting_Section_FinAcc__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.BCCC_Age_Bracket__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.BCCC_Cause__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.BCCC_Cycle_Time__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.BCCC_Issue__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.BCCC_Regulatory_Reporting_Check__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Benefit_Name__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Insurance_Benefit_Type__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Business_Area__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Business_Sub_Area__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Caller_Type__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Case_Age__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Case_Age_Reopen_Duration__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Case_Number__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Case_Owner__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Case_Owner_Complaint__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Case_Owner_Team__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Case_Owner_Team_Complaint__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Cause__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.CCR_Complaint_Level_IDR_C_Check__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Check_fields_on_close__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Classification__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Class_of_Business__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.COBisBankingProduct__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Commercial_Settlement__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Company__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Complaint_Compensation_Requested__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Complaint_Category__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.ComplaintChecklist__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Id</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.ComplaintIs_AFCA_IDR_Referral_And_Closed__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Complaint_Level__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Name</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Complaint_Regulatory_Condition__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Complaint_Sub_Category__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Complaint_Type__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Complaint_Uplift_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Complaint_Contact_Details__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Contact_Preference__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Contract__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Cost_To_Licensee__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.CreatedBy</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Created_By_Team__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.CreatedDate</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Credit_Bureau__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Credit_Provider__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Date_Complaint_Received_By_3rd_Party__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Days_In_Between_Closed_Status__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Description__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Diarised_Until__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Direction__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Complaint_Discretionary_Payment_Amount__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Complaint_Insurance_Dispute_Number__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Insurance_Dispute_Outcome__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Litigation_Dispute_Outcome__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Insurance_Dispute_Outcome_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Litigation_Dispute_Outcome_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Complaint_Insurance_Dispute_Reason__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Insurance_Dispute_Status__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Litigation_Dispute_Status__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Complaint_Insurance_Dispute_Type__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Litigation_Dispute_Type__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Root_Cause__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Driver_Cause_Blank_Check__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.EDR_Closed_Status__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.EDR_Complaint_Closed__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.EDR_Outcome__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.EDR_Outcome_Type__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.EDR_Potential_Systemic_Issue__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.EDR_QP_Vetting_Section_Conditional__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.EDR_Scheme__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.EDR_Scheme_Reference__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Complaint_Entity__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.IsEscalated__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Complaint_External_Product__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.FinAcc_Blank__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Financial_Account__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.First_Name__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Insurance_Frequency_of_Sum_Insured__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.GRC_Reference__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Historic_Adviser__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Historic_Adviser_Check__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.ID_Check_Result_New__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.ID_Check_Result__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.IDR_Review_Outcome__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.IDRReviewOutcomeCheck__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Investment_Details__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Investment_Details_check__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.LastActivityDate</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.LastModifiedBy</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.LastModifiedDate</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Last_Name__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Legacy_External_Reference_ID__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Legacy_System__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.REM_Licensee__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Link_to_PRM_Adviser__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Link_to_PRM_Practice__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Litigation_Complaint_Closed__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Origin__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Complaint_Milestone_Criteria__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.My_AMP__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Non_product_related_Cannot_find_Contract__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Complaint_Other_External_Reference_Numbe__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Other_Impacted_Areas__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Other_Impacted_Entities__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Complaint_Outcome__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Outcome_Description__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Outcome_Description_Length__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Complaint_Outcome_Requested_by_Customer__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Owner</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Parent_Case__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Insurance_Party_In_Dispute__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Policy_Cancelled__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Potential_Adviser_Exposure__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Complaint_Potential_Breach_Item__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Potential_Licensee_Exposure__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Potential_PI_Exposure__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Complaint_Potential_Vulnerability_Type__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Previous_Status__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Priority__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Privacy_Related_Complaint__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Processed_By__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.ProductSubType__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Product_Sub_Type__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Product_Team__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Complaint_Product_Type__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Projects_Events__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Raised_By__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Delay_Reasons__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Complaint_Received_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.RecordType</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Compensation__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Insurance_Reinsurance__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Insurance_Reinsurer__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Related_To__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Related_To_IDR__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Delay_Scenario__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Delay_Other__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Settlement_Amount__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.SimpleIDR_for_Legal_and_Cust_Advocate__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Complaint_SpecialAssistance_CareNeed__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Complaint_SpecialAssistance_CareNeeded__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Status__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Status_Reason__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Status_Transition_Comments__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Status_Transition_Date__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Subject__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Submitted_Online__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Insurance_Sum_Insured__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Sync_with_PRM__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.The_Complaint_is_about__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Total_no_of_Investments__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Transaction_Approved_By__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Complaint_Trust_or_Platform__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Insurance_Unresolved_Dispute_Reason__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Vetting_Type_Question_Conditional__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Complaint__c.Yes_Plan_Employed__c</field>
            <table>FinServ__FinancialHolding__c.Investment_Details__r</table>
        </columns>
        <masterLabel>Complaints Lookup</masterLabel>
    </sections>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__FinancialAccount__c.Product_System_Code__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__FinancialAccount__c.Plan_Name__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__FinancialAccount__c.Product_Code__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__FinancialAccount__c.USI_Code__c</field>
            <table>FinServ__FinancialHolding__c</table>
        </columns>
        <masterLabel>Financial Account Lookup</masterLabel>
    </sections>
</ReportType>
