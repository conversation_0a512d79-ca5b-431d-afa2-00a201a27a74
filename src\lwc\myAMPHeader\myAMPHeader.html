<template>   
    <div class="content__body-header">
        <div slot="title" class="slds-grid slds-grid_vertical-align-center c-title-header-confirmation header-margin-bottom"> 
            <div class="slds-col"> 
                <img src={ampLogo} class="ampLogo slds-button slds-float_left" aria-label="AMP Logo" alt="AMP Logo">
            </div>
            <div class="slds-col">
                <button type="button" class="slds-button slds-float_right slds-var-p-right_large" title="Back to My AMP" onclick={handleBack}>
                    <!--<lightning-icon icon-name="utility:close" size="x-small" class="slds-current-color slds-float_right exit" ></lightning-icon-->
                    <img src={exitButton} class="exit" aria-label="Close" alt="Close">
                    <div class="close">Close</div>
                </button> 
            </div>
        </div>
    </div>

    <template if:true={isModalOpen}>
        <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container modalWhole">
                <header class="slds-modal__header slds-modal__header_empty">
                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" title="Close" onclick={closeModal}>
                        <lightning-icon icon-name="utility:close"
                            alternative-text="close"
                            variant="inverse"
                            size="small" ></lightning-icon>
                        <span class="slds-assistive-text">Close</span>
                    </button>
                </header>
                <div class="slds-modal__content modalPadding" id="modal-content-id-1">
                    <div>
                        <p class="modalHead">Are you sure you want to leave?
                        </p>
                    </div>
                    <div>
                    <p class="modalBody">The details you've entered will not be saved. If you leave now, you'll have to start again.
                    </p>
                    </div>
                    <div class="slds-float_right">
                        <button class="slds-button slds-button_neutral modalYes" onclick={submitDetails} title="Yes">Yes, leave form</button>
                        <button class="slds-button slds-button_brand modalNo" onclick={closeModal} title="No">No</button>
                        <!--div class="modalYesBox">
                            <button class="slds-button slds-button_neutral modalYes" onclick={submitDetails} title="Yes">Yes, leave form</button>
                        </div>
                        <div class="modalNoBox">
                            <button class="slds-button slds-button_brand modalNo" onclick={closeModal} title="Cancel">Cancel</button>
                        </div--> 
                    </div>
                </div>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>
</template> 