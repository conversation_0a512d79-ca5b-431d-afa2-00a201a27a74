<?xml version="1.0" encoding="UTF-8"?>
<Report xmlns="http://soap.sforce.com/2006/04/metadata">
    <columns>
        <field>Account$FinServ__PrimaryContact__c.Salutation</field>
    </columns>
    <columns>
        <field>Account$FinServ__PrimaryContact__c.FirstName</field>
    </columns>
    <columns>
        <field>Account$FinServ__PrimaryContact__c.LastName</field>
    </columns>
    <columns>
        <aggregateTypes>Sum</aggregateTypes>
        <field>Account$Days_to_Next_Review__c</field>
    </columns>
    <columns>
        <field>Account$FinServ__ReviewFrequency__c</field>
    </columns>
    <columns>
        <field>Account$Primary_Contact_Email__c</field>
    </columns>
    <columns>
        <field>Account$Primary_Contact_Alternate_Email__c</field>
    </columns>
    <columns>
        <aggregateTypes>Sum</aggregateTypes>
        <field>Account$FinServ__PrimaryContact__c.E_Signature__c</field>
    </columns>
    <columns>
        <field>Account$FinServ__PrimaryContact__c.MobilePhone</field>
    </columns>
    <columns>
        <field>Account$FinServ__PrimaryContact__c.OtherStreet</field>
    </columns>
    <columns>
        <field>Account$FinServ__PrimaryContact__c.Residential_Suburb__c</field>
    </columns>
    <columns>
        <field>Account$FinServ__PrimaryContact__c.OtherState</field>
    </columns>
    <columns>
        <field>Account$FinServ__PrimaryContact__c.OtherPostalCode</field>
    </columns>
    <columns>
        <field>Account$FinServ__PrimaryContact__c.OtherCountry</field>
    </columns>
    <description>Updated Report for new report type (Accounts with Contacts)</description>
    <filter>
        <criteriaItems>
            <column>Account$Name</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>notEqual</operator>
            <value>Fifteen Hundred Financial Strategists</value>
        </criteriaItems>
        <criteriaItems>
            <column>Account$FinServ__ClientCategory__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>Silver,Gold,Platinum</value>
        </criteriaItems>
        <criteriaItems>
            <column>Account$FinServ__NextReview__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>lessThan</operator>
            <value>TODAY</value>
        </criteriaItems>
        <language>en_US</language>
    </filter>
    <format>Summary</format>
    <groupingsDown>
        <dateGranularity>Day</dateGranularity>
        <field>Account$FinServ__ClientCategory__c</field>
        <sortOrder>Asc</sortOrder>
    </groupingsDown>
    <groupingsDown>
        <field>Account$FinServ__NextReview__c</field>
        <sortOrder>Asc</sortOrder>
    </groupingsDown>
    <name>Review Dates in the Past</name>
    <params>
        <name>co</name>
        <value>1</value>
    </params>
    <reportType>Accounts_with_Contacts__c</reportType>
    <scope>organization</scope>
    <showDetails>true</showDetails>
    <showGrandTotal>true</showGrandTotal>
    <showSubTotals>true</showSubTotals>
    <timeFrameFilter>
        <dateColumn>Account$CreatedDate</dateColumn>
        <interval>INTERVAL_CUSTOM</interval>
    </timeFrameFilter>
</Report>
