/*----------------------------------------------------------------
@Description:   Trigger for the AuthFormRequestRecord object
@testclass :    AuthFormRequestRecordTrigger_Test
------------------------------------------------------------------*/
trigger AuthFormRequestRecordTrigger on AuthFormRequestRecord (before insert, before update, before delete, after insert, after update, after delete) {
   
    UtilityClass.debug(string.valueOf(Trigger.operationType) + ' AuthFormRequestRecord Trigger Start: ' + system.now());
	TriggerFactory.createTriggerDispatcher(AuthFormRequestRecord.SObjectType);
    UtilityClass.debug(string.valueOf(Trigger.operationType) + ' AuthFormRequestRecord Trigger End: ' + system.now());
}