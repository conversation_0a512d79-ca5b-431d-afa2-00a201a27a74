<?xml version="1.0" encoding="UTF-8"?>
<Report xmlns="http://soap.sforce.com/2006/04/metadata">
    <columns>
        <field>FinServ__Alert__c$Id</field>
    </columns>
    <columns>
        <aggregateTypes>Sum</aggregateTypes>
        <field>FinServ__Alert__c$Applies_to_all_Financial_Accounts__c</field>
    </columns>
    <columns>
        <field>FinServ__Alert__c$FinServ__FinancialAccount__c</field>
    </columns>
    <columns>
        <field>FinServ__Alert__c$FinServ__Account__c</field>
    </columns>
    <columns>
        <field>FinServ__Alert__c$FinServ__Message__c</field>
    </columns>
    <columns>
        <field>FinServ__Alert__c$FinServ__FinancialAccount__c.Product_System_Code__c</field>
    </columns>
    <filter>
        <booleanFilter>1 AND 2 AND 3 AND (4 OR 5) AND 6 AND 7 AND 8 AND 9</booleanFilter>
        <criteriaItems>
            <column>FinServ__Alert__c$FinServ__Account__c.Has_AMP_Product__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>1</value>
        </criteriaItems>
        <criteriaItems>
            <column>FinServ__Alert__c$FinServ__Account__c.Has_AMP_Life_Product__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>1</value>
        </criteriaItems>
        <criteriaItems>
            <column>FinServ__Alert__c$Applies_to_all_Financial_Accounts__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>0</value>
        </criteriaItems>
        <criteriaItems>
            <column>FinServ__Alert__c$End_Date__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value></value>
        </criteriaItems>
        <criteriaItems>
            <column>FinServ__Alert__c$End_Date__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>greaterThan</operator>
            <value>2/1/2022</value>
        </criteriaItems>
        <criteriaItems>
            <column>FinServ__Alert__c$FinServ__Active__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>1</value>
        </criteriaItems>
        <criteriaItems>
            <column>FinServ__Alert__c$FinServ__FinancialAccount__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>notContain</operator>
            <value>AMP Flexible Super - Super,Flexible Lifetime - Super,SignatureSuper,CustomSuper,SuperLeader,Metcash,AMP Flexible Super - Retirement,Flexible Lifetime - Allocated Pension,SignatureSuper Allocated Pension,Flexible Lifetime - Term Pension,SignatureSuper - Term Pension,SignatureSuper - Allocated Pension,AMP Capital Investment Funds</value>
        </criteriaItems>
        <criteriaItems>
            <column>FinServ__Alert__c$FinServ__FinancialAccount__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>notEqual</operator>
            <value></value>
        </criteriaItems>
        <criteriaItems>
            <column>FinServ__Alert__c$FinServ__FinancialAccount__c.Product_System_Code__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>notEqual</operator>
            <value>CM,CH,L7,L8,BK,BD</value>
        </criteriaItems>
        <language>en_US</language>
    </filter>
    <format>Tabular</format>
    <name>Alerts with Account and FA</name>
    <params>
        <name>co</name>
        <value>1</value>
    </params>
    <reportType>Alerts_with_Account_and_Financial_Account__c</reportType>
    <scope>organization</scope>
    <showDetails>true</showDetails>
    <showGrandTotal>true</showGrandTotal>
    <showSubTotals>true</showSubTotals>
    <timeFrameFilter>
        <dateColumn>FinServ__Alert__c$CreatedDate</dateColumn>
        <interval>INTERVAL_CUSTOM</interval>
    </timeFrameFilter>
</Report>
