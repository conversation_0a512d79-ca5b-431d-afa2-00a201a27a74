<!--
 - Created by jerry.poon on 30/06/2021.
 -->

<!-- Client Comm Xplan Goal Row --> 
<template>
    <section class={_mainClass}>
        <div class="xplan-details__sectionHead slds-col slds-size_1-of-1">
            <div class="slds-media slds-media_center">
                <!-- Title -->
                <div class="slds-col">
                    <span class="xplan-details__icon">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z" clip-rule="evenodd"></path></svg>
                    </span>
                    {_title}
                </div>
                <!-- Chevron -->
                <div class="slds-no-flex">
                    <div class="xplan-details__headerButton" onclick={handleToggleCollapse}>
                        <template if:true={_showDownChevron}>
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>
                        </template>
                        <template if:true={_showUpChevron}>
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path></svg>
                        </template>
                    </div>
                </div>
            </div>
        </div>
        <!-- Details -->
        <div class="xplan-details__sectionBody slds-col slds-grid slds-wrap slds-size_1-of-1 slds-order_2 slds-medium-order_1">
            <template for:each={_details} for:item="detail">
                <div class="xplan-details__sectionField slds-col slds-size_1-of-1 slds-medium-size_6-of-12 slds-large-size_3-of-12"
                     key={detail.External_Id__c}>
                    <c-client-comm-record-field field={detail}></c-client-comm-record-field>
                </div>
            </template>
        </div>
    </section>
</template>