// SFP-51661, burhan 

import { LightningElement, api } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { FlowNavigationNextEvent } from 'lightning/flowSupport';
import getEmailWebServiceInterface from '@salesforce/apex/ContactDetailsSectionController.getEmailWebServiceInterface';

const STR_SHOW = 'SHOW';
const STR_STARTED = 'STARTED';
const STR_FINISHED = 'FINISHED';
const STR_YES = 'YES';

export default class FormsDigiExperianEmailInput extends LightningElement {

	isWarning = false;
	isError = false;
	globalSwitch = false;
	isDisabled = false;
	invalidInput = false;//SFP-62636, burhan
	runOnce = false;//SFP-62636, burhan
	timeout = 10000;
	numberOfWarnings = 0;
	authToken = '';
	url = '';
	emailAddressClean;
	sessionStorageCheckInterval;
	experianMessage;
	@api successful;
	@api isRequired = false;
	@api validationStatus;
	@api validationMessage = 'Please enter your email address in the format: <EMAIL>';
	@api emailAddress;
	@api errorMessage;
	@api warningMessage = 'Please check that your email is correct before continuing.';

	connectedCallback(){
		// console.log('bn FormsDigiExperianEmailInput connectedCallback 1');
		// console.log('bn FormsDigiExperianEmailInput connectedCallback experianCheck',sessionStorage.getItem('formsDigiExperianEmailInput.experianCheck'));

		this.emailAddressClean = this.emailAddress;
		this.invalidInput = sessionStorage.getItem('formsDigiExperianEmailInput.invalidInput') == STR_YES;//SFP-62636, burhan
		//console.log('bn formsDigiExperianEmailInput connectedCallback invalidInput',this.invalidInput);
		
		getEmailWebServiceInterface().then(result => {
			this.globalSwitch = result.globalSwitch;
			this.url = result.url;
			this.authToken = result.authToken;
			this.timeout = result.timeout;
		}).catch(error => {
			console.error('bn connectedCallback getEmailWebServiceInterface error', error);
			let event = new ShowToastEvent({
				title: 'Error!',
				variant: 'error',
				mode: 'dismissable'
			});
			this.dispatchEvent(event);
		});

		if(sessionStorage.getItem('formsDigiExperianEmailInput.experianCheck') == STR_STARTED){
			this.sessionStorageCheckInterval = setInterval(() => {
				this.checkSessionStorage();
			}, 500);
		}
	}

	//SFP-62636, burhan
	renderedCallback(){
		if(this.invalidInput && !this.runOnce){
			this.runOnce = true;
			const inputValidation = this.template.querySelector('lightning-input');
			inputValidation.reportValidity();
			//console.log('bn formsDigiExperianEmailInput renderedCallback reportValidity');
		}
	}

	checkSessionStorage(){
		try {
			// console.log('bn FormsDigiExperianEmailInput checkSessionStorage');
			let experianCheck = sessionStorage.getItem('formsDigiExperianEmailInput.experianCheck');
			// console.log('bn FormsDigiExperianEmailInput checkSessionStorage experianCheck',experianCheck);
			
			if(experianCheck == STR_FINISHED){
				// console.log('bn FormsDigiExperianEmailInput clearInterval');
				clearInterval(this.sessionStorageCheckInterval);

				this.isDisabled = sessionStorage.getItem('formsDigiExperianEmailInput.isDisabled') == STR_YES;
				this.isWarning = sessionStorage.getItem('formsDigiExperianEmailInput.warningSection') == STR_SHOW;
				let mobileIsWarning = sessionStorage.getItem('formsDigiExperianPhoneInput.warningSection') == STR_SHOW;
				let mobileExperianCheck = sessionStorage.getItem('formsDigiExperianPhoneInput.experianCheck') == STR_FINISHED;
				
				// console.log('bn FormsDigiExperianEmailInput checkSessionStorage isWarning',this.isWarning);
				// console.log('bn FormsDigiExperianEmailInput checkSessionStorage mobileIsWarning',mobileIsWarning);
				// console.log('bn FormsDigiExperianEmailInput checkSessionStorage mobileExperianCheck',mobileExperianCheck);
				// console.log('bn FormsDigiExperianEmailInput checkSessionStorage isDisabled',this.isDisabled);

				if(!this.isWarning && (!mobileIsWarning || (mobileIsWarning && mobileExperianCheck))){
					this.handleGoNext();
				}
			}
		} catch (error) {
			console.error('bn checkSessionStorage error',error);
		}
	}

	checkInput(e){
		try{
			let newVal = e.target.value;
			// console.log('bn FormsDigiExperianEmailInput checkInput newVal',newVal);
			// console.log('bn FormsDigiExperianEmailInput checkInput emailAddress',this.emailAddress);
			
			if(newVal && (!this.emailAddress || newVal.toUpperCase() != this.emailAddress.toUpperCase())){
				// console.log('bn FormsDigiExperianEmailInput checkInput 2');
				this.emailAddress = newVal;
				this.isWarning = false;
				sessionStorage.setItem('formsDigiExperianEmailInput.warningSection', '');

				//validate using SF
				const inputValidation = this.template.querySelector('lightning-input')
				inputValidation.reportValidity();
				let valid = inputValidation.checkValidity();
				// console.log('bn FormsDigiExperianEmailInput checkInput valid1',valid);

				let tempArr = newVal.split('@');
				// console.log('bn FormsDigiExperianEmailInput checkInput tempArr',tempArr);
				let startingIndex = tempArr[1] ? tempArr[1].indexOf('(') : -1;
				let lastIndex = tempArr[1] ? tempArr[1].lastIndexOf(')') : -1;
				// console.log('bn FormsDigiExperianEmailInput checkInput startingIndex',startingIndex);
				// console.log('bn FormsDigiExperianEmailInput checkInput lastIndex',lastIndex);
				if(startingIndex != -1 && lastIndex != -1){
					let tempSubString = tempArr[1].substring(startingIndex, lastIndex + 1)
					// console.log('bn FormsDigiExperianEmailInput checkInput tempArr[1]2',tempSubString);
					let a = tempArr[1].replace(tempSubString, '');
					// console.log('bn FormsDigiExperianEmailInput checkInput a',a);
					this.emailAddressClean = tempArr[0] + '@' + a;
					// console.log('bn FormsDigiExperianEmailInput checkInput emailAddressClean',this.emailAddressClean);
				}else{
					this.emailAddressClean = newVal;
				}
				// console.log('bn FormsDigiExperianEmailInput checkInput valid2',valid);
				
				//validate using API callout
				if(valid){
					this.doExperianCheck();
				}
				//SFP-62636, burhan
				else{
					//prevent duplicate error msg
					this.invalidInput = true;
				}
			}else{
				// console.log('bn FormsDigiExperianEmailInput checkInput emailAddressClean',this.emailAddressClean);
				this.emailAddressClean = newVal;
			}
		}catch(error){
			console.error('bn error', error);
		}
	}
	
	@api
	validate(){
		try{
			// console.log('bn FormsDigiExperianEmailInput validate');
			const inputValidation = this.template.querySelector('lightning-input')
			inputValidation.reportValidity();
			let valid = inputValidation.checkValidity();
			
			// console.log('bn FormsDigiExperianEmailInput validate valid',valid);
			
			if(valid){
				let experianCheck = sessionStorage.getItem('formsDigiExperianEmailInput.experianCheck');
				let mobileExperianCheck = sessionStorage.getItem('formsDigiExperianPhoneInput.experianCheck');
				// console.log('bn FormsDigiExperianEmailInput validate experianCheck',experianCheck);
				
				if(experianCheck != STR_STARTED && mobileExperianCheck != STR_STARTED){
					// console.log('bn FormsDigiExperianEmailInput validate = true');
					this.successful = true; 
					return {isValid : true};
				}

				return {isValid : false, errorMessage: ''};//no error msg since this will arrive late, fetch the error msg in init instead from session storage
			}
		}catch(error){
			console.error('bn validate error',error);
		}

		//SFP-62636, burhan
		if(this.invalidInput){
			sessionStorage.setItem('formsDigiExperianEmailInput.invalidInput', STR_YES);
		}

		//console.log('bn formsDigiExperianEmailInput validate invalidInput',this.invalidInput);
		
		// return {isValid : false, errorMessage: this.validationMessage};//SFP-62636, burhan, commented-out
		return {isValid : false, errorMessage: this.invalidInput ? '' : this.validationMessage};//SFP-62636, burhan
	}

	doExperianCheck(){
		// console.log('bn FormsDigiExperianEmailInput doExperianCheck');
		sessionStorage.setItem('formsDigiExperianEmailInput.experianCheck', STR_STARTED);
		sessionStorage.setItem('formsDigiExperianEmailInput.isDisabled', STR_YES);

		this.isDisabled = true;

		// check if global switch isn't disabled
		if(!this.globalSwitch){
			let params = {
				method: 'POST',
				headers: {
				"Content-Type" : "application/json",
				"Auth-Token": this.authToken
				},
				body: '{"email":"'+ this.emailAddressClean+'", "timeout":'+this.timeout+'}'
			};

			fetch(this.url, params).then(response => {
				this.statusCode = response.status;
				// console.log('bn FormsDigiExperianEmailInput doExperianCheck fetch statusCode', this.statusCode);
				return response.json(); // returning the response in the form of JSON
			}).then(jsonResponse => {
				// console.log('bn FormsDigiExperianEmailInput doExperianCheck fetch jsonResponse',jsonResponse);

				let objData = {
					resultConfidence : '',
				};

				this.successful = false;
				if(this.statusCode == 200 && jsonResponse.result){
					let exchangeData = jsonResponse.result;
		
					// adding data object
					objData.resultConfidence = exchangeData.confidence;
	
					if(objData.resultConfidence){
						//get response message and corresponding icons, styling and status
						this.isWarning = false;
						switch(objData.resultConfidence){
							case 'verified':
								this.successful = true;
								this.validationStatus = "Verified";
								this.experianMessage = 'Successfully verified as deliverable';
								break;
							case 'unreachable':
								this.validationStatus = 'Unreachable';
								this.experianMessage = 'Email is undeliverable';
								this.isWarning = true;
								break;
							case 'illegitimate':
								this.validationStatus = 'Illegitimate';
								this.experianMessage = 'Email is undeliverable';
								this.isWarning = true;
								break;
							case 'undeliverable':
								this.validationStatus = 'Undeliverable';
								this.experianMessage = 'Emails are unlikely to be delivered';
								this.isWarning = true;
								break;
							case 'disposable':
								this.validationStatus = 'Disposable';
								this.experianMessage = 'Email Address may not be in permanent use';
								this.isWarning = true;
								break;
							case 'unknown':
								this.validationStatus = 'Unknown';
								this.experianMessage = 'Email not completely verified, but it may be correct';
								this.isWarning = true;
								break;
						}
					}
				} else {
					//when server is down or response is unsuccessful
					// console.log('Response error with status code: ', this.statusCode);
					this.experianMessage = 'Verification Service unavailable';
					this.validationStatus = 'Unavailable';
					this.isWarning = true;
				}

				// console.log('bn FormsDigiExperianEmailInput doExperianCheck message',this.experianMessage)
				// console.log('bn FormsDigiExperianEmailInput doExperianCheck status',this.status)
				// console.log('bn FormsDigiExperianEmailInput doExperianCheck isWarning',this.isWarning)

				sessionStorage.setItem('formsDigiExperianEmailInput.experianCheck', STR_FINISHED);
				sessionStorage.setItem('formsDigiExperianEmailInput.isDisabled', '');

				if(this.isWarning){
					sessionStorage.setItem('formsDigiExperianEmailInput.warningSection', STR_SHOW);
				}else{
					sessionStorage.setItem('formsDigiExperianEmailInput.warningSection', '');
				}

				this.isDisabled = false;
			})
			.catch(error => {
				console.error('bn error',error);
			})
		}
		// else global switch is disabled
		else {
			this.experianMessage = 'Verification Service is switched off';
			this.validationStatus = 'Switched off';
		}
	}

	handleGoNext() {
		// console.log('bn FormsDigiExperianEmailInput handleGoNext');
		sessionStorage.setItem('formsDigiExperianEmailInput.experianCheck','');
		sessionStorage.setItem('formsDigiExperianEmailInput.warningSection', '');

		this.dispatchEvent(new FlowNavigationNextEvent());
    }
}