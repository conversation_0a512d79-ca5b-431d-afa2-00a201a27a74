<template>  
    <div class="slds-box slds-theme_default backgroundColor-grey">
        <template if:false={isPrevious}>
            <template if:true={renderPageNumber}>
                <p class="header-style" style="padding-top: 33px;">{formName}</p>
                <p class="subheader-style">Important information and declaration</p>
                <div class="formContainer">
                    <div if:true={isLoaded}>
                        <p class="heading-style" style="padding-bottom:24px;">Important information</p>

                        <template if:true={genTextList}>
                            <div class="slds-card accordion-container">
                                <lightning-accordion allow-multiple-sections-open>

                                    <template iterator:it={genTextList}>

                                        <lightning-accordion-section
                                            label={it.value.Form_Generic_Text__r.Important_information_label__c}
                                            key={it.value.Id}>
                                            <lightning-formatted-rich-text
                                                value={it.value.Form_Generic_Text__r.Current_Display_Text__c}>
                                            </lightning-formatted-rich-text>
                                        </lightning-accordion-section>

                                    </template>


                                </lightning-accordion>
                            </div>
                        </template>



                        <div if:true={isPartialDischarge}>
                            <lightning-input type="checkbox"
                                label="I have read, understood and accepted the important information." class="validateCheckbox"
                                required
                                message-when-value-missing="Please acknowledge that you have read and understood the important information before proceeding">
                            </lightning-input>

                            <p class="heading-style" style="padding-bottom:24px; padding-top:24px;">Acknowledgement</p>

                            <lightning-input type="checkbox" label={pdCheckboxLabel} class="validateCheckbox" required
                                message-when-value-missing="Please acknowledge that you have electronically signed this acknowledgement before proceeding"
                                style="padding-bottom:24px;">
                            </lightning-input>
                        </div>

                        <div if:false={isPartialDischarge}>
                            <lightning-input type="checkbox"
                                label="I have read and understood the Important information." class="validateCheckbox"
                                required
                                message-when-value-missing="Please acknowledge that you have read and understood the important information before proceeding">
                            </lightning-input>

                            <p class="heading-style" style="padding-bottom:24px; padding-top:24px;">Declaration</p>

                            <lightning-formatted-rich-text value={declaration} class="pbody-style">
                            </lightning-formatted-rich-text>

                            <lightning-input type="checkbox"
                                label="I have read and electronically signed this Declaration." class="validateCheckbox"
                                required
                                message-when-value-missing="Please declare that you have electronically signed the declaration"
                                style="padding-bottom:24px; padding-top:24px;">
                            </lightning-input>

                        </div>

                        <div class="slds-col slds-p-top_small slds-p-right_small footer-button-style">
                            <button type="button" class="close-button-style slds-button slds-button_neutral"
                                onclick={handleBack}>Previous</button>
                            <button type="button" class="close-button-style slds-button slds-button_brand buttonSpace"
                                onclick={handleContinue}>Finish</button>
                        </div>
                    </div>
                    <div if:false={isLoaded} class="slds-is-relative">
                        <lightning-spinner alternative-text="Loading...">
                        </lightning-spinner>
                    </div>
                </div>
            </template>


            <template if:false={renderPageNumber}>
                <c-bank-forms-thank-you-screen onshowpages={handlePages} selected-consent={selectedConsent}
                    redirect-url={redirectUrl}>
                </c-bank-forms-thank-you-screen>
            </template>
        </template>

        <template if:true={isPrevious}>
            <c-bank-forms-consent-details onshowpages={handlePages} dfr-id={dfrId} selected-consent={selectedConsent}
                redirect-url={redirectUrl}>
            </c-bank-forms-consent-details>
        </template>
    </div>
</template>