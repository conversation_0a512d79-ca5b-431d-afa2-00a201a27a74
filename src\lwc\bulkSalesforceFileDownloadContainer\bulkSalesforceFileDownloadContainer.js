import { LightningElement, wire, api } from 'lwc';
import hasIFABulkDownloadPermission from '@salesforce/customPermission/IFA_Bulk_Download';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
//import hasDCFBulkDownloadPermission from '@salesforce/customPermission/DCF_Bulk_Download';
//import isDCFAvailableForObject from '@salesforce/apex/RemoteFileUploadCtrl.isDCFAvailableForObject';


import {
    subscribe,
    unsubscribe,
    APPLICATION_SCOPE,
    MessageContext,
    publish
} from 'lightning/messageService';

import bulkDocumentDownloadMessage from '@salesforce/messageChannel/bulkDocumentDownloadMessageChannel__c';
import dataListViewSubscribe from '@salesforce/messageChannel/dataListViewSubscribeMessage__c';

export default class BulkSalesforceFileDownloadContainer extends LightningElement { 
    
    hasPermissionToBulkDownload = hasIFABulkDownloadPermission;

    @api className;
    @api recordId;
    @api objectApiName;

    dcfAvailableForObject = false;

    subscription = null;

    salesforceFiles = [];
    //sharepointFiles = [];
    onDemandFiles = [];

    connectedCallback() {
        this.subscribeToMessageChannel();
        //this.checkIfDCFAvailableForObject();
    }

    disconnectedCallback() {
        this.unsubscribeToMessageChannel();
    }

    /*async checkIfDCFAvailableForObject() {
        try {
            this.dcfAvailableForObject = await isDCFAvailableForObject({recordId: this.recordId});
        }
        catch(ex) {
            console.error('Exception: ', ex);
        }
    }*/
    

    @wire(MessageContext) 
    messageContext;

    get isDocumentationPage() {
        return this.objectApiName == 'Documentation__c';
    }
    
    subscribeToMessageChannel() {
        if (!this.subscription) {
            this.subscription = subscribe(
                this.messageContext,
                bulkDocumentDownloadMessage,
                (message) => this.handleMessage(message),
                { scope: APPLICATION_SCOPE }
            );
        }
    }

    unsubscribeToMessageChannel() {
        unsubscribe(this.subscription);
        this.subscription = null;
    }

    handleMessage(message) {
        if(this.recordId != message.recordId) return;
        
        if(!message.records || message.records.length == 0) {
            this.showToast(
                'error',
                'Error',
                'Please select documents before downloading.'
            );
            return;
        }

        this.salesforceFiles = [];
        //this.sharepointFiles = [];
        this.onDemandFiles = [];
        for(let record of message.records) {
            /*if(record.isDocumentation) {
                this.sharepointFiles.push(record);
            }
            else*/
            if(record.onDemand) {
                this.onDemandFiles.push(record);
            }
            else if(record.docType != 'Email Message') {
                this.salesforceFiles.push(record);
            }
        }

        this.startDownload();
    }

    startDownload() {
        //let fileSize = this.sharepointFiles.length + this.salesforceFiles.length;
        let fileSize = this.salesforceFiles.length;
        if(fileSize > 1) {
            this.showToast(
                'info',
                'You are about to download ' + fileSize + ' files',
                'Documents may be split into multiple Zip files based on size and location.'
            );
        }

        /*if(this.sharepointFiles && this.sharepointFiles.length > 0) {
           this.downloadSharepointFiles();
        }
        else {
            this.downloadSalesforceFiles();
        }*/
        this.downloadSalesforceFiles();
    }

    /*downloadSharepointFiles() {
        let remoteFileDownloadComp = this.template.querySelector('c-remote-file-download');
        remoteFileDownloadComp.downloadFiles(this.sharepointFiles);
    }*/

    downloadSalesforceFiles() {
        if(this.salesforceFiles && this.salesforceFiles.length > 0) {
            let urlIdArrays = [];
            let maxUrlIdArrayLength = 100;

            let salesforceFileIds = this.salesforceFiles.map(item => item.id);

            for(let i = 0; i < salesforceFileIds.length; i = i + maxUrlIdArrayLength) {
                let partialArray = salesforceFileIds.slice(i, i + maxUrlIdArrayLength);
                urlIdArrays.push(partialArray);
            }

            for(let urlIdArray of urlIdArrays) {
                let a = document.createElement("a");
                a.href = '/sfc/servlet.shepherd/version/download/' + urlIdArray.join('/');
                a.style = "display: none";
                a.target = '_blank';
                document.body.appendChild(a);
                a.click();
                a.remove();
            }
        }
    }

    // Respond to UI event by publishing message
    startDownloadingFiles(event) {
      publish(this.messageContext, dataListViewSubscribe, {recordId: this.recordId});
    }


    handleRemoteFileDownloadCompletion() {
        this.downloadSalesforceFiles();
    }

    showToast(variant, title, msg) {
        let eventToast = new ShowToastEvent({
            title: title,
            variant: variant,
            message: msg
        });

        this.dispatchEvent(eventToast);
    }
}