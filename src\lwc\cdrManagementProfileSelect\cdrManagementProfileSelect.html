<template>
    <div class="content__body-dashboard">
        <h2 class="slds-text-align_left hurme-text header-spacing font-style_light-bold">Select a profile</h2>
        <p class="slds-text-align_left header-spacing_margin">Open Banking lets you control how your banking data is
            shared with trusted third parties, including other banks.</p>       
        <div class="auth-page-intermediary">   
            <div class="content__body-main-dashboard-shadow">
                <!-- <div class="slds-media__body slds-grid slds-grid_vertical-align-center">
                    <div class="slds-grid slds-wrap">
                        <lightning-combobox name="progress" label="Select a profile" value={value}
                            placeholder="Please select" options={options} onchange={handleChange} required>
                        </lightning-combobox>
                    </div>
                </div> -->

                <div class="slds-media__body slds-grid slds-grid_vertical slds-p-around_small">
                    <div class="slds-col">
                        <span>
                            <lightning-combobox name="progress" label="Select a profile" value={value}
                                placeholder="Please select" options={options} onchange={handleChange} required>
                            </lightning-combobox>
                        </span>
                    </div>
                    <div class="slds-col slds-p-top_small slds-p-right_small">
                        <span>
                            <div class="slds-grid">
                                <div class="slds-col slds-size_2-of-3">
                                    <span> </span>
                                </div>
                                <div class="slds-col slds-size_1-of-3">
                                    <span>
                                        <h5 class="hurme-text">
                                            <button type="button"
                                                class="slds-button slds-button_brand button__medium button-width_medium ss-button-cont continueDisabled"
                                                disabled={disableContinue} onclick={handleClick}>Next</button>
                                        </h5>
                                    </span>
                                </div>
                            </div>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>