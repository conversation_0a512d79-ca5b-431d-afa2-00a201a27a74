<template>
    <lightning-spinner if:true={loading} alternative-text="Uploading..." size="small"></lightning-spinner>
    <lightning-layout multiple-rows="true">
        <lightning-layout-item size="12" if:true={bottom}>
            <lightning-file-upload
                if:false={external}
                label={label}
                name="uploadFile"
                accept={acceptedFormats}
                file-field-name='Guest_Record_fileupload__c'
                file-field-value={value}
                onuploadfinished={handleUpload_lightningFile}
                multiple={allowMultiple}
                disabled={disabled}>
            </lightning-file-upload>
            <lightning-input
                if:true={external}
                type="file"
                label={label}
                accept={acceptedFormats}
                onchange={handleUpload_lightningInput}
                multiple={allowMultiple}
                disabled={disabled}>
            </lightning-input>
        </lightning-layout-item>
        <lightning-layout-item class="slds-form-element__label" size="12">
            {uploadedlabel}
        </lightning-layout-item>
        <lightning-layout-item size="12">
            <lightning-layout multiple-rows="true">
                <template for:each={objFiles} for:item="objFile">
                    <div key={objFile} class="slds-size_12-of-12 slds-small-size_12-of-12 slds-medium-size_12-of-12 slds-large-size_12-of-12 slds-var-p-around_xx-small" style="padding-top: 16px;">
                        <lightning-layout class="slds-grid_vertical-align-center slds-var-p-top_xx-small slds-var-p-bottom_xx-small slds-var-p-left_xx-small slds-var-p-right_small" style="border: 1px solid #d8dde6; border-radius:4px; background-color:#F3F2F2; height:44px;">
                            <lightning-layout-item size="11">
                                <div class="slds-media slds-media_center slds-has-flexi-truncate">
                                    <div class="slds-media__figure">
                                        <span class="slds-avatar slds-avatar_medium">
                                            <lightning-icon 
                                                icon-name={objFile.filetype}
                                                size="medium"
                                                title="Icon">
                                            </lightning-icon>
                                        </span>
                                    </div>
                                    <lightning-tile
                                        label={objFile.name}
                                        onclick={openFile}
                                        data-docid={objFile.documentId}
                                        class="slds-media__body">
                                    </lightning-tile>
                                </div>
                            </lightning-layout-item>
                            <lightning-layout-item size="1" class="slds-text-align_right">
                                <!-- sldsValidatorIgnoreNextLine -->
                                <lightning-button-icon
                                    icon-name="utility:close"
                                    title="Delete File"
                                    aria-hidden="true"
                                    onclick={deleteDocument}
                                    data-documentid={objFile.documentId}
                                    data-contentversionid={objFile.contentVersionId}
                                    variant="bare">
                                </lightning-button-icon>
                            </lightning-layout-item>
                        </lightning-layout>
                    </div>
                </template>
            </lightning-layout>
        </lightning-layout-item>
        <lightning-layout-item size="12" if:false={bottom}>
            <lightning-file-upload
                if:false={external}
                label={label}
                name="uploadFile"
                accept={acceptedFormats}
                file-field-name='Guest_Record_fileupload__c'
                file-field-value={value}
                onuploadfinished={handleUpload_lightningFile}
                multiple={allowMultiple}
                disabled={disabled}>
            </lightning-file-upload>
            <lightning-input
                if:true={external}
                type="file"
                label={label}
                accept={acceptedFormats}
                onchange={handleUpload_lightningInput}
                multiple={allowMultiple}
                disabled={disabled}>
            </lightning-input>
        </lightning-layout-item>
    </lightning-layout>
</template>