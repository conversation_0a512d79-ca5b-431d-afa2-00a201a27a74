import { LightningElement, track, wire, api } from 'lwc'; 
import getSecondaryUserDetailsForIndividualTrust from '@salesforce/apex/CdrSecondaryUserManagement.getSecondaryUserDetailsForIndividualTrust';
import getSecondaryUserDetails from '@salesforce/apex/CdrSecondaryUserManagement.getSecondaryUserDetails';
import getSecondaryDataPermissionName from '@salesforce/apex/CdrSecondaryUserManagement.getSecondaryDataPermissionName';
import getFullAuthorityDetails from '@salesforce/apex/CdrGetAccountsWS02.getFullAuthorityDetails';
import fetchCDRSetting from '@salesforce/apex/CdrLandingDashboardController.fetchCDRSetting'; 
//Toast events
import {ShowToastEvent} from "lightning/platformShowToastEvent" 
//For navigation
import { NavigationMixin } from 'lightning/navigation'; 
//CSS from static resource
import { loadStyle } from 'lightning/platformResourceLoader';
import cdrCss from '@salesforce/resourceUrl/CDR_Styles'; 
import LOGIN_LOGO from '@salesforce/resourceUrl/login_logo';
import CDR_LOGO from '@salesforce/resourceUrl/CDR_Logo';
import ERROR_LOGO from '@salesforce/resourceUrl/cdr_authorisation_error';
// Custom labels
import CDR_Secondary_Data_Sharing from '@salesforce/label/c.CDR_Secondary_Data_Sharing';
import CDR_Secondary_Data_Sharing_1 from '@salesforce/label/c.CDR_Secondary_Data_Sharing_1';
import CDR_Secondary_Data_Sharing_K from '@salesforce/label/c.CDR_Secondary_Data_Sharing_K';
import CDR_Secondary_Data_Sharing_2 from '@salesforce/label/c.CDR_Secondary_Data_Sharing_2';
import CDR_Secondary_Data_Sharing_3 from '@salesforce/label/c.CDR_Secondary_Data_Sharing_3';
import CDR_Secondary_Data_Sharing_4 from '@salesforce/label/c.CDR_Secondary_Data_Sharing_4';
import CDR_Secondary_Data_Sharing_5 from '@salesforce/label/c.CDR_Secondary_Data_Sharing_5';




export default class CdrDashboardLandingPage extends NavigationMixin(LightningElement) { 
    

    @track activeSecondary = [];  
    @track sharingDisabledFinAccts = []; 
    @track cdrFAQLink = '';
    @track cdrJointAcctSharing = false; 

    
    
    error; 
    loginLogo = LOGIN_LOGO; 
    cdrLogo=  CDR_LOGO;
    @track editMode = true;
    @track fromConfirmation = false;
    @track showErrorScreen = false;
    @track enableContinue;
    @track showPartialAccountsFromList;
   
    @track deselectedAllAcc = false;
    @track financialAccounts = [];
    @track activeFinancialAccounts = [];
    @track inactiveFinancialAccounts = [];
    @track withdrawnFinancialAccounts = [];
    @track unavailableFinancialAccounts = [];
    @track availableFinancialAccounts = [];
    @track partialAccounts = [];
    @track availableFinancialAccountsFromList = [];
    @track partialAccountsFromList = [];
    @track newAccountsFromList = [];
    @track excludedFinancialAccounts = [];
    @track selectedFaSet = [];
    @track filteredSelectedFaSet = [];
    @track filteredAvailableFinancialAccounts = [];
    @track deselectedFaSet = [];
    @track unavailableCollapsed = false;
    @track loaded = false; ;
    @track loadedFromList;
    @track selectedFinancialAccounts = [];
    
    legend_color=  'status-style_border status-style_green-color';
    legend_color2=  'status-style_border status-style_grey-color';

    numberOfSelectedFas = 0;
    faDataMap = new Map();
   
    secondaryUser;
    @api secondaryUserId;
    ownerSCVID;
    secondaryUserDetails;
    @track showFinAccountInfo = true;
    @track faDataArray;
    indivTrustAccId;
    isIndivTrust = false;
    
    labels = {                 
        CDR_Secondary_Data_Sharing_1,
        CDR_Secondary_Data_Sharing,
        CDR_Secondary_Data_Sharing_K,
        CDR_Secondary_Data_Sharing_2,
        CDR_Secondary_Data_Sharing_3,
        CDR_Secondary_Data_Sharing_4,
        CDR_Secondary_Data_Sharing_5
    };

    svgURL = `${ERROR_LOGO}#error_logo`

    constructor() { 
        super(); 
        Promise.all([ 
            loadStyle(this, cdrCss) 
        ]).then(() => { 
            // console.log("Loaded css"); 
        }).catch(error => { 
            console.error('Error loading static resources', error); 
        }); 
    }

   
    connectedCallback(){

        if(this.getTrustAccId()){
            this.indivTrustAccId = this.getTrustAccId();
            this.isIndivTrust = true;
        }

        if(this.fromConfirmationScreen()){
            this.fromConfirmation = true;
            this.editMode = false;
        }

        this.parameters = this.getAccId();

        fetchCDRSetting({ 
            cdrRecord: 'CDR Management Settings'
        }).then(data => {
            if(data){
                let cdrMngtSettings = JSON.parse(data);
                this.cdrFAQLink = cdrMngtSettings.CDR_FAQ__c;
            }      
            else{
                console.log('Error fetching CDR Management Settings: ', error);
            }
        }).catch(error => {
            console.log('Error fetching CDR Management Settings: ', error);
        });

        getSecondaryDataPermissionName({

            myID: this.parameters,
            indivOwnerAccId: this.indivTrustAccId

        })
        .then(data => {
            if(data){
                this.secondaryUserDetails = data;
                let Permission = JSON.parse(JSON.stringify(this.secondaryUserDetails));

                if(!Permission.errorMessage){
                    this.secondaryUser = Permission.secondaryName;
                    this.secondaryUserId = Permission.secondaryNameId;
                    this.ownerSCVID = Permission.secondarySCVID;
                    
                }
            }else{
                this.error = error;
                console.log('Error fetching Data Sharing Arrangements: ', JSON.stringify(error));
                this.showToast('An unexpected error occurred when retrieving data sharing arrangements', 'error', 'Please contact your system administrator.');
            }
        }).catch(error => {
            this.error = error;
            console.log('Error fetching Data Sharing Arrangements: ', JSON.stringify(error));
            this.showToast('An unexpected error occurred when retrieving data sharing arrangements', 'error', 'Please contact your system administrator.');
        })

        if(this.isIndivTrust){
            getSecondaryUserDetailsForIndividualTrust({
                secondaryUserAccId: this.parameters,
                indivTrustAccId: this.indivTrustAccId
            })
            .then(data => {
                if(data){
                    this.financialAccounts = data;
                    const faDataArray = [];
                    for (let faData of data) {
                      const key = faData.finAccId;
                      const value = faData;
                      if (!faDataArray.some(([existingKey, existingValue]) => existingKey === key)) {
                        faDataArray.push([key, value]);
                        //this.availableFinancialAccounts.push(faData);                      
                      }
                    }
                    this.faDataArray = faDataArray;
                    for(let faData of data){
                        if(!this.faDataMap.has(faData.finAccId)){
                            this.faDataMap.set(faData.finAccId, faData);
                        }
                    }
                    this.groupFinancialAccounts();                
                }


                 
                if(this.availableFinancialAccounts.length > 0){
               
                    let newFaSet = this.availableFinancialAccounts;
                     for(let fa of newFaSet) {
                    if(fa.isSecondaryUserEnabled == true){
                        this.selectedFinancialAccounts.push(fa);
                       }
                    }
                     this.availableFinancialAccounts = newFaSet;
                     this.selectedFaSet = this.selectedFinancialAccounts;
                     this.numberOfSelectedFas = this.selectedFaSet.length;
               }


               if(this.selectedFaSet.length === 0){
                this.deselectedAllAcc = false;
                }
                  else{
                       this.deselectedAllAcc = true;
                }

    
                this.loaded = true;      
            }).catch(error => {
                this.error = error;
                console.log('Error fetching Data Sharing Arrangements: ', error);
                this.showToast('An unexpected error occurred when retrieving secondary user details', 'error', 'Please contact your system administrator.');
            })
        }else{
            getSecondaryUserDetails({
                secondaryUserAccId: this.parameters
            })
            .then(data => {
                if(data){
                    this.financialAccounts = data;
                    const faDataArray = [];
                    for (let faData of data) {
                      const key = faData.finAccId;
                      const value = faData;
                      if (!faDataArray.some(([existingKey, existingValue]) => existingKey === key)) {
                        faDataArray.push([key, value]);
                        
                        //this.availableFinancialAccounts.push(faData);
                    

                      }
                    }
                    this.faDataArray = faDataArray;
          
                    
                    
                                       
                     for(let faData of data){
                        if(!this.faDataMap.has(faData.finAccId)){
                            this.faDataMap.set(faData.finAccId, faData);
                        }
                    } 
                    this.groupFinancialAccounts();
                }

               
                if(this.availableFinancialAccounts.length > 0){
               
                    let newFaSet = this.availableFinancialAccounts;
                     for(let fa of newFaSet) {
                    if(fa.isSecondaryUserEnabled == true){
                        this.selectedFinancialAccounts.push(fa);
                       }
                    }
                     this.availableFinancialAccounts = newFaSet;
                     this.selectedFaSet = this.selectedFinancialAccounts;
                     this.numberOfSelectedFas = this.selectedFaSet.length;
               }


               if(this.selectedFaSet.length === 0){
                this.deselectedAllAcc = false;
                }
                  else{
                       this.deselectedAllAcc = true;
                }





    
                this.loaded = true;      
            }).catch(error => {
                this.error = error;
                console.log('Error fetching Data Sharing Arrangements: ', error);
                this.showToast('An unexpected error occurred when retrieving secondary user details', 'error', 'Please contact your system administrator.');
            })
        }
      
    }

    renderedCallback(){
       
       
        //we persists the toggle on the buttons when we move from 2nd page to first page
        for(let selectedFA of this.selectedFaSet){
            for(let btn of this.template.querySelectorAll('input.toggleSelection')){
                
                //we check if the button value == FA number and we indicate the button is checked
                if(btn.value === selectedFA.finAccId){
                    btn.checked = true;
                }
            }
        }
    }

    handleSelectAll() {
       
       
        //Select all
        if(this.deselectedAllAcc) {
            //If deselection, we make checked to false
            for(let toggle of this.template.querySelectorAll('input.toggleSelection')) {
                toggle.checked = false;
            }
           
            
           
            //Clear the selected Fa
            this.selectedFaSet = [];
           
            
            //change the elements in available set to all deselected
            //clone the list so we can set the property
           let newFaSet = this.availableFinancialAccounts;
     
           
            
            for(let fa of newFaSet) {
                fa.isSecondaryUserEnabled = false;
            }
            this.availableFinancialAccounts = newFaSet;
            
            
        } else {
            //If select all, we add the 'checked' attribute to the toggle selections
            for(let toggle of this.template.querySelectorAll('input.toggleSelection')) {
                toggle.checked = true;
            }
            //Add all available fa to selected set
            this.selectedFaSet = [];
            for (let faData of this.availableFinancialAccounts) {
                this.selectedFaSet.push(faData);
                console.log("faData_s",faData);

            }

          
           
          
            //this.availableFinancialAccounts = faData;

            //change the elements in available set to all selected
            //clone the list so we can set the property
            let newFaSet = this.availableFinancialAccounts;
           

           
            for(let fa of newFaSet) {
                fa.isSecondaryUserEnabled = true;
            }
            this.availableFinancialAccounts = newFaSet;
           

        }

        this.deselectedAllAcc = !this.deselectedAllAcc;
        this.numberOfSelectedFas = this.selectedFaSet.length;
    }

   
   
    handleSelect(event){
       
        console.log("availableFinancialAccounts",this.availableFinancialAccounts);
 
         if(event.target.checked){
             //if checked, we check through the FA map if the key is present and push the entire value(faData) in the array
             if(!this.selectedFaSet.includes(this.faDataMap.get(event.target.value))){
                
                console.log("event.target.value_ren",event.target.value);
                
                 this.selectedFaSet.push(this.faDataMap.get(event.target.value));
                 console.log("electedFaSettttttww",this.selectedFaSet);
                 //Find the element in the available fa and set it to selected
                 for(let i = 0; i < this.availableFinancialAccounts.length; i++) {
                     if(this.availableFinancialAccounts[i].finAccId === event.target.value) {
                         //clone the list so we can set the property
                         let newFaSet = this.availableFinancialAccounts;
                         newFaSet[i].isSecondaryUserEnabled = true;
                         this.availableFinancialAccounts = newFaSet;
                        
                         break;
                     }
                }
                 
 
         }

         
 
        }
         else{
             
            console.log("event.target.value_ren2",event.target.value);
            
            //we remove the element when it is in the selectedFaSet array
             for(let i = 0; i < this.selectedFaSet.length; i++) {
                 if(this.selectedFaSet[i].finAccId === event.target.value) {
                     this.selectedFaSet.splice(i, 1);
                     break;
                 }
             }
             //Find the element in the available fa and set it to deselected
             for(let i = 0; i < this.availableFinancialAccounts.length; i++) {
                 if(this.availableFinancialAccounts[i].finAccId === event.target.value) {
                     //clone the list so we can set the property
                     let newFaSet = this.availableFinancialAccounts;
                     newFaSet[i].isSecondaryUserEnabled = false;
                     this.availableFinancialAccounts = newFaSet;
                     break;
                 }
             }
        }
 
         //we set deselectedAllAcc flag to render either Select All/Deselect All label 
         if(this.selectedFaSet.length === 0){
             this.deselectedAllAcc = false;
        }
         else{
             this.deselectedAllAcc = true;
        }
         this.numberOfSelectedFas = this.selectedFaSet.length;
    }


    handleUnavailableCollapse(){
        //allow collapse of unavailable FAs
        this.unavailableCollapsed = !this.unavailableCollapsed;
    }

    handleEditAccounts(event){
        event.preventDefault();
        this.editMode = true;
        this.fromConfirmation = false;
    }

    handleCancel(event){
        if(this.isIndivTrust){
                        
            event.preventDefault();
            this[NavigationMixin.Navigate]({
                type: 'comm__namedPage',
                attributes: {
                    pageName: 'secondary-user'
                },
                state: {
                    'businessAccId': this.indivTrustAccId
                }
            });
        }else{
            
            event.preventDefault();
            this[NavigationMixin.Navigate]({
                type: 'comm__namedPage',
                attributes: {
                    pageName: 'secondary-user'
                }
            });
        }
    }

    groupFinancialAccounts(){



        this.financialAccounts.forEach(finAcct => {
            if(!finAcct.masterDisclosureSetting){
                this.unavailableFinancialAccounts.push(finAcct);
            }else if(!finAcct.fullAuthority){
                this.partialAccounts.push(finAcct);
                this.availableFinancialAccounts.push(finAcct);
            }else if(finAcct.fullAuthority && !finAcct.isSecondaryUserEnabled && finAcct.isWithdrawn){
                    this.withdrawnFinancialAccounts.push(finAcct);
                    this.availableFinancialAccounts.push(finAcct);

            }else if(finAcct.fullAuthority && finAcct.isSecondaryUserEnabled){
                    this.activeFinancialAccounts.push(finAcct);
                    this.availableFinancialAccounts.push(finAcct);
            }else{
                this.inactiveFinancialAccounts.push(finAcct);
                this.availableFinancialAccounts.push(finAcct);
            }
        });

        
        if(this.unavailableFinancialAccounts.length === 0){ 
            this.unavailableFinancialAccounts = false;
        }

        if(this.activeFinancialAccounts.length === 0){
            this.activeFinancialAccounts = false;
        }

        if(this.inactiveFinancialAccounts.length === 0){
            this.inactiveFinancialAccounts = false;
        }

        if(this.withdrawnFinancialAccounts.length === 0){
            this.withdrawnFinancialAccounts = false;
        }

        if(this.partialAccounts.length === 0){
            this.partialAccounts = false;
        }

        if(!this.activeFinancialAccounts && !this.inactiveFinancialAccounts && !this.withdrawnFinancialAccounts && !this.partialAccounts){
            this.availableFinancialAccounts = false;
        }

        console.log("faDataArray_Ren2",JSON.stringify(this.availableFinancialAccounts));

    }


    getQueryParameters() {
        var params = {};
        var search = location.search.substring(1);
        if (search) {
            params = JSON.parse('{"' + search.replace(/&/g, '","').replace(/=/g, '":"') + '"}', (key, value) => {
                return key === "" ? value : decodeURIComponent(value)
            });
        }
        return params;
    }

    getAccId() {
        const queryString = window.location.search;
        
        const urlParams = new URLSearchParams(queryString);

        const account = urlParams.get('account');

        //console.log('account Id: ' + account);

        return account;
        
    }

    getTrustAccId() {
        const queryString = window.location.search;
        
        const urlParams = new URLSearchParams(queryString);

        const trustAcc = urlParams.get('trustAcc');

        //console.log('trustAcc Id: ' + trustAcc);

        return trustAcc;
        
    }

    
    fromConfirmationScreen() {
        const queryString = window.location.search;
        
        const urlParams = new URLSearchParams(queryString);

        const fromConfirmation = urlParams.get('fromConfirmation');

        //console.log('trustAcc Id: ' + trustAcc);

        return fromConfirmation;
        
    }


        //handle ENTER key press on LWC checkbox
        changeToggleValue(event){
            if(event.which == 13){  
                //alert("Selected"+event.target.checked);
                event.target.checked = !event.target.checked;
                this.handleSelect(event);
            }
        }
   
    //handle toast events
    showToast(toastTitle, toastVariant, toastMessage){
        this.dispatchEvent(
            new ShowToastEvent({
                title: toastTitle,
                variant: toastVariant,
                message: toastMessage
            }),
        );
    }

    handleContinue(event){
        this.loadedFromList = false;
        this.enableContinue = true;
        this.showPartialAccountsFromList = false;
        let accNumberList = this.availableFinancialAccounts.map(account => account.accNumber);
        console.log('accNumberList: ' + accNumberList);
        try {
            getFullAuthorityDetails({ ownerSCVID: this.ownerSCVID, accountId: this.parameters, accountNumbersList: accNumberList})
            .then((result) => {
                // Process the response (result is the map returned from Apex)
                console.log('Response:', JSON.stringify(result));
                if(result){
                    const fullAuthMap = result.reduce((acc, accountInfo) => {
                        acc[accountInfo.accNumber] = accountInfo.accessRole;
                        return acc;
                    }, {});
    
                    // Filter accounts based on accessRole
                    this.filteredSelectedFaSet = this.selectedFaSet.filter(item => {
                        const finAccId = item.finAccId;
                        const accNumber = item.accNumber;
                        return accNumber in fullAuthMap && fullAuthMap[accNumber] === 'CanCreditAndDebit';
                    });
    
                    this.filteredAvailableFinancialAccounts = this.availableFinancialAccounts.filter(item => {
                        const finAccId = item.finAccId;
                        const accNumber = item.accNumber;
                        return accNumber in fullAuthMap && fullAuthMap[accNumber] === 'CanCreditAndDebit';
                    });
    
                    this.excludedFinancialAccounts = this.availableFinancialAccounts.filter(item => {
                        const accNumber = item.accNumber;
                        return !(accNumber in fullAuthMap && fullAuthMap[accNumber] === 'CanCreditAndDebit');
                    });

                    if(this.isIndivTrust){
                        getSecondaryUserDetailsForIndividualTrust({
                            secondaryUserAccId: this.parameters,
                            indivTrustAccId: this.indivTrustAccId
                        })
                        .then(data => {
                            if(data){
                                this.financialAccounts = data;
                                const faDataArrayFromList = [];
                                for (let faData of data) {
                                  const key = faData.finAccId;
                                  const value = faData;
                                  if (!faDataArrayFromList.some(([existingKey, existingValue]) => existingKey === key)) {
                                    faDataArrayFromList.push([key, value]);
                                    
                                    this.newAccountsFromList.push(faData);

                                  }
                                }
                            }

                                let newFaSet = this.newAccountsFromList;
                                 for(let fa of newFaSet) {
                                if(fa.fullAuthority == true){
                                    this.availableFinancialAccountsFromList.push(fa);
                                    this.enableContinue = false;
                                    this.loadedFromList = true;
                                   }else{
                                    this.partialAccountsFromList.push(fa);
                                    this.showPartialAccountsFromList = true;
                                    this.loadedFromList = true;
                                   }
                                }
                        }).catch(error => {
                            this.error = error;
                            console.log('Error fetching Data Sharing Arrangements: ', error);
                            this.showToast('An unexpected error occurred when retrieving secondary user details', 'error', 'Please contact your system administrator.');
                        })
                    }else{
                        getSecondaryUserDetails({
                            secondaryUserAccId: this.parameters
                        })
                        .then(data => {
                            if(data){
                                this.financialAccounts = data;
                                const faDataArrayFromList = [];
                                for (let faData of data) {
                                  const key = faData.finAccId;
                                  const value = faData;
                                  if (!faDataArrayFromList.some(([existingKey, existingValue]) => existingKey === key)) {
                                    faDataArrayFromList.push([key, value]);
                                    
                                    this.newAccountsFromList.push(faData);

                                  }
                                }
                            }

                                let newFaSet = this.newAccountsFromList;
                                 for(let fa of newFaSet) {
                                    console.log('fa: ' + JSON.stringify(fa));
                                if(fa.fullAuthority == true){
                                    this.availableFinancialAccountsFromList.push(fa);
                                    this.enableContinue = false;
                                    this.loadedFromList = true;
                                   }else{
                                    this.partialAccountsFromList.push(fa);
                                    this.showPartialAccountsFromList = true;
                                    this.loadedFromList = true;
                                   }
                                }
                        }).catch(error => {
                            this.error = error;
                            console.log('Error fetching Data Sharing Arrangements: ', error);
                            this.showToast('An unexpected error occurred when retrieving secondary user details', 'error', 'Please contact your system administrator.');
                        })
                    }
    
                    console.log('unfiltered:', JSON.stringify(this.selectedFaSet));
                    console.log('filtered:', JSON.stringify(this.filteredSelectedFaSet));
                    console.log('filteredAvailable:', JSON.stringify(this.filteredAvailableFinancialAccounts));
                    console.log('excluded:', JSON.stringify(this.excludedFinancialAccounts));

                } else {
                    this.showErrorScreen = true;
                }
                
            })
            .catch((error) => {
                // Handle any error that occurred during the Apex callout
                this.showErrorScreen = true;
                console.error('Error:', JSON.stringify(error));
            });
            

            this.editMode = false;
            
        } catch (error) {
            console.log('Error: ', error);
        }
        
    }
    

    handleContinueFromList(event){
        if(this.isIndivTrust){
                        
            event.preventDefault();
            this[NavigationMixin.Navigate]({
                type: 'comm__namedPage',
                attributes: {
                    pageName: 'secondary-confirmation'
                },
                state: {
                    'account': this.parameters,
                    'trustAcc': this.indivTrustAccId,
                    'selectedFaSet': JSON.stringify(this.filteredSelectedFaSet),
                    'availableFinancialAccounts' : JSON.stringify(this.filteredAvailableFinancialAccounts),
                    'partialAccessAccounts' : JSON.stringify(this.excludedFinancialAccounts)
                }
            });
        }else{
            
            event.preventDefault();
            this[NavigationMixin.Navigate]({
                type: 'comm__namedPage',
                attributes: {
                    pageName: 'secondary-confirmation'
                },
                state: {
                    'account': this.parameters,
                    'selectedFaSet': JSON.stringify(this.filteredSelectedFaSet),
                    'availableFinancialAccounts' : JSON.stringify(this.filteredAvailableFinancialAccounts),
                    'partialAccessAccounts' : JSON.stringify(this.excludedFinancialAccounts)
                }
            });
        }
    }

    getIsFullAccessListEmpty() {
        if(this.availableFinancialAccountsFromList.length > 0){
            this.enableContinue = false;
        }
     }

}