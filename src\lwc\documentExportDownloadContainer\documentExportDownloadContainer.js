import { LightningElement, wire, api, track} from 'lwc';
import { subscribe, unsubscribe, APPLICATION_SCOPE, MessageContext } from 'lightning/messageService';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { reduceErrors } from 'c/ldsUtils';
import { loadScript } from 'lightning/platformResourceLoader';
import jszip from '@salesforce/resourceUrl/jszip';
 
export default class DocumentExportDownloadContainer extends LightningElement {
    @api recordId
    @wire(MessageContext)
    messageContext;

    @track failedFiles = [];

    totalFileSize = 0;
    files = [];
    isModalOpen = false;
    driveId;
    downloadedFileSize = 0;

    zipProgressPercent;
    zipProgressFileName;
 
    downloadFileName;
    isCanceled = false;

    isFechingInfoStep;
    isProgressStep;
    isWarningStep;
    isZippingStep;
    isSuccessStep;
    sharepointId;

    fileIdVsAccId = new Map();
    accIdVsFolderName = new Map();

    get downloadPercent() {
        return Math.round((this.downloadedFileSize/this.totalFileSize) * 100);
    }

    showToast(variant, title, msg) {
        let eventToast = new ShowToastEvent({
            title: title,
            variant: variant,
            message: msg
        });

        this.dispatchEvent(eventToast);
    }

    // Encapsulate logic for Lightning message service subscribe and unsubsubscribe
    subscribeToMessageChannel() {
        if (!this.subscription) {
            this.subscription = subscribe(
                this.messageContext,
                (message) => this.handleMessage(message)
            );
        }
    }

    unsubscribeToMessageChannel() {
        unsubscribe(this.subscription);
        this.subscription = null;
    }

    // Handler for message received by component
    handleMessage(message) {
        console.log('message.recordId: ', message.recordId);
        console.log('recordId: ', this.recordId);
        if(message.recordId != this.recordId) return;
        this.getFileInfos();        
    }

    // Standard lifecycle hooks used to subscribe and unsubsubscribe to the message channel
    connectedCallback() {
        this.subscribeToMessageChannel();
    }

    disconnectedCallback() {
        this.unsubscribeToMessageChannel();
    }

    prepareFolderNames(accountDetails) {
        //sorting by practice id
        accountDetails.sort(
            (a, b) => a.servicingPracticeId == b.servicingPracticeId ? 0 : (a.servicingPracticeId > b.servicingPracticeId ? 1 : -1)
        );

        let practiceIdVsName = new Map();
        let practiceIdVsAdviserDetail = new Map();
        let practiceId_adviserIdVsAccDetail = new Map();

        for(let acc of accountDetails) {
            //underscores are used to handle the scenario if id/name is blank, this will create a folder with name _
            
            //Preparing unique practice name
            let practiceName = acc.servicePracticeName || '_';
            let practiceId = acc.servicingPracticeId || '_';

            if(!practiceIdVsName.has(practiceId)) {
                let usedPracticeNames = new Set(Array.from(practiceIdVsName.values()));
                let newPracticeName = this.getNewFileOrFolderName(practiceName, 0, usedPracticeNames);
                practiceIdVsName.set(practiceId, newPracticeName);
            }

            //Preparing unique adviser name for a practice

            if(!practiceIdVsAdviserDetail.has(practiceId)) {
                practiceIdVsAdviserDetail.set(practiceId, new Map());
            }

            let adviserName = acc.adviserName || '_';
            let adviserId = acc.adviserId || '_';

            if(!practiceIdVsAdviserDetail.get(practiceId).has(adviserId)) {
                let usedAdviserNames = new Set(Array.from(practiceIdVsAdviserDetail.get(practiceId).values()));
                let newAdviserName = this.getNewFileOrFolderName(adviserName, 0, usedAdviserNames);
                practiceIdVsAdviserDetail.get(practiceId).set(adviserId, newAdviserName);
            }

            //Preparing unique acc name for a adviser under a practice

            let keyForAccName = practiceId + '_' + adviserId;
            if(!practiceId_adviserIdVsAccDetail.has(keyForAccName)) {
                practiceId_adviserIdVsAccDetail.set(keyForAccName, new Map());
            }

            let accName = acc.accountName;
            let accId = acc.accountId;

            if(!practiceId_adviserIdVsAccDetail.get(keyForAccName).has(accId)) {
                let usedAccNames = new Set(Array.from(practiceId_adviserIdVsAccDetail.get(keyForAccName).values()));
                let newAccName = this.getNewFileOrFolderName(accName, 0, usedAccNames);
                practiceId_adviserIdVsAccDetail.get(keyForAccName).set(accId, newAccName);
            }

            let folderName = practiceIdVsName.get(practiceId) + '/'
                + practiceIdVsAdviserDetail.get(practiceId).get(adviserId) + '/'
                + practiceId_adviserIdVsAccDetail.get(keyForAccName).get(accId);

            this.accIdVsFolderName.set(acc.accountId, folderName);
        }

    }

    getNewFileOrFolderName(name, index, usedNames) {
        let newName = name + (index > 0 ? '(' + index + ')' : '');
        if(!usedNames.has(newName)) {
            usedNames.add(newName);
            return newName;
        }
        else {
            return this.getNewFileOrFolderName(name, index + 1, usedNames);
        }
    }

    async getFileInfos() {
        try{
            this.isCanceled = false;
            this.isFechingInfoStep = true;
            this.isModalOpen = true;
            if(this.isCanceled) return;
            
            this.isFechingInfoStep = false;

            this.files = sharepointFileInfos.map(file => ({
                id: file.documentId,
                blob: null,
                fileSize: file.fileSize,
                fileName: file.fileName,
                downloadSize: 0,
                isSuccess: false,
                sharepointId: file.sharepointId,
                type: 'Sharepoint'
            }));

            let bigSalesforceFilesIds = [];
            let sizeLimit = 23068672; //22 MB

            for(let salesforceFile of salesforceFileInfos) {
                if(salesforceFile.ContentSize < sizeLimit) {
                    this.files.push({
                        id: salesforceFile.Id,
                        blob: null,
                        fileSize: salesforceFile.ContentSize,
                        fileName: salesforceFile.PathOnClient,
                        downloadSize: 0,
                        isSuccess: false,
                        type: 'Salesforce'
                    });
                }
                else {
                    bigSalesforceFilesIds.push(salesforceFile.Id);
                }
            }

            if(bigSalesforceFilesIds.length > 0) {
                this.downloadSalesforceFiles(bigSalesforceFilesIds);
            }

            if(this.files.length == 0 && bigSalesforceFilesIds.length == 0) {
                this.showToast('info', 'Info', 'No document is available to download.');
            }

            if(this.files.length > 0) {
                this.totalFileSize = this.files.reduce((prevValue, currentValue) => prevValue + currentValue.fileSize, 0);
                this.isProgressStep = true;
                this.checkFileTypeAndDownload(0);
            }
            else {
                this.isModalOpen = false;
            }
        }
        catch(ex) {
            this.isFechingInfoStep = false;
            this.isModalOpen = false;
            console.error(ex);
            this.showToast('error', 'Error', reduceErrors(ex).join('\n'));
        }
    }

    checkFileTypeAndDownload(index) {
        if(this.files[index].type == 'Salesforce') {
            this.downloadASalesforceFile(index);
        }
        else {
            this.downloadAFile(index);
        }
    }

    async downloadASalesforceFile(fileIndex) {
        console.log('file Name:',this.files[fileIndex].fileName);
        this.downloadFileName = this.getFileNameWithFolder(this.files[fileIndex]);

        try {
            // let encodedData = await getSalesforceFile({fileId : this.files[fileIndex].id});
            // if(this.isCanceled) return;

            let decodedData = atob(encodedData);
            this.files[fileIndex].downloadSize += decodedData.length;
            this.downloadedFileSize += decodedData.length;
            let uni8Array = this.base64toUni8Arrays(decodedData);

            this.files[fileIndex].blob = new Blob(uni8Array);
            this.files[fileIndex].isSuccess = true;
            this.downloadNextFileOrChunk(fileIndex);
        }
        catch(ex) {
            console.error('ex==>>', ex);
            if(this.isCanceled) return;
            this.downloadedFileSize = this.downloadedFileSize - this.files[fileIndex].downloadSize;
            this.totalFileSize = this.totalFileSize - this.files[fileIndex].fileSize;
            this.downloadNextFileOrChunk(fileIndex);
            this.files[fileIndex].isSuccess = false;
        }
        
    }

    downloadNextFileOrChunk(fileIndex, startIndex, endIndex) {
        //Salesforce Files will be downloaded into a single chunk
        //hence if last downloaded file is a salesforce file we need to 
        //download next file (salesforce or sharepoint) and not the next chunk
        if(
            this.files[fileIndex].type == 'Salesforce' ||
            (!startIndex && !endIndex) ||
            endIndex == this.files[fileIndex].fileSize
        ) {
            fileIndex = fileIndex + 1;
            if(fileIndex < this.files.length) {
                if(this.files[fileIndex].type == 'Salesforce') {
                    this.downloadASalesforceFile(fileIndex);
                }
                else {
                    this.downloadAFile(fileIndex);
                }
            }
            else {
                this.completeDownload();
            }
        }
        else if(endIndex < this.files[fileIndex].fileSize) {
            this.downloadAFile(fileIndex, startIndex, endIndex);
        }
    }

    completeDownload() {
        this.failedFiles = [];
        this.isProgressStep = false;
        let errFiles = this.files.filter(item => !item.isSuccess);

        if(errFiles.length == 0) {
            this.isZippingStep = true;
            this.downloadAsZip();
        }
        else {
            this.isWarningStep = true;
            this.setFailedFiles();
        }
    }

    setFailedFiles() {
        let failedFilesList = [];
        
        for(let file of this.files) {
            if(!file.isSuccess) {
                failedFilesList.push({
                    id : file.id,
                    fileName: this.getFileNameWithFolder(file)
                });
            }
        }

        failedFilesList.sort((a, b) => a.fileName.toLocaleLowerCase() < b.fileName.toLocaleLowerCase() ? - 1 : 1);

        this.failedFiles = failedFilesList;
    }

    getFileNameWithFolder(file) {
        let fileId = file.id;
        let fileName = file.fileName;
        let accId = this.fileIdVsAccId.get(fileId);
        let folderName = this.accIdVsFolderName.get(accId);

        return folderName + '/' + fileName;
    }

    downloadAsZip() {
        this.zipProgressPercent = 0;
        this.zipProgressFileName = '';

        let successFulFiles = this.files.filter(item => item.isSuccess);
        let ref = this;

        Promise.all([
            loadScript(this, jszip + '/jszip/jszip.js'),
            loadScript(this, jszip + '/jszip/FileSaver.js')
        ])
        .then(() => {
            let zip = new JSZip();
                
            let folderNameVsUsedFileNames = new Map();

            for(let file of successFulFiles) {
                let fileId = file.id;
                let fileName = file.fileName;
                let accId = this.fileIdVsAccId.get(fileId);
                let folderName = this.accIdVsFolderName.get(accId);

                if(!folderNameVsUsedFileNames.has(accId)) {
                    folderNameVsUsedFileNames.set(accId, new Set());
                }

                let usedFileNames = folderNameVsUsedFileNames.get(accId);
                fileName = ref.getNewFileName(file, usedFileNames);
                
                zip.folder(folderName).file(fileName, file.blob);
            }
            
            zip.generateAsync({type:"blob"}, (progress) => {
                this.zipProgressPercent = progress.percent.toFixed();
                this.zipProgressFileName = progress.currentFile || '';
            })
            .then(function(content) {
                saveAs(content, 'Archive.zip');
                ref.isZippingStep = false;
                ref.handleCompletion();
            })
            .catch(ex => {
                console.error('Error in zipping: ', ex);
            });
            
        }).catch(ex => {
            console.error(ex);
        })
    }

    base64toUni8Arrays(decodedData) {
        let sliceSize = 512;
        const byteCharacters = decodedData;
        const byteArrays = [];
      
        for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
          const slice = byteCharacters.slice(offset, offset + sliceSize);
      
          const byteNumbers = new Array(slice.length);
          for (let i = 0; i < slice.length; i++) {
            byteNumbers[i] = slice.charCodeAt(i);
          }
      
          const byteArray = new Uint8Array(byteNumbers);
          byteArrays.push(byteArray);
        }
        return byteArrays;
    }

    closeModal() {
        this.isModalOpen = false;
        this.files = [];
        this.isModalOpen = false;
        this.totalFileSize = 0;
        this.downloadedFileSize = 0;
        this.isFechingInfoStep = false;
        this.isProgressStep = false;
        this.isWarningStep = false;
        this.isZippingStep = false;
        this.isSuccessStep = false;

        this.fileIdVsAccId = new Map();
        this.accIdVsFolderName = new Map();
    }

    cancelDownload() {
        this.isCanceled = true;
        this.files = [];
        this.isModalOpen = false;
        this.totalFileSize = 0;
        this.downloadedFileSize = 0;
        this.isFechingInfoStep = false;
        this.isProgressStep = false;
        this.isWarningStep = false;
        this.isZippingStep = false;
        this.isSuccessStep = false;

        this.fileIdVsAccId = new Map();
        this.accIdVsFolderName = new Map();
    }

    closeIconModal(){
        this.isModalOpen = false;
    }
    
    handleCompletion() {
        this.isSuccessStep = true;
        this.dispatchEvent(
            new CustomEvent('completion')
        );
    }

    getNewFileName(file, usedFileNames) {
        let fileName = file.fileName || '';
        let lastDotIndex = fileName.lastIndexOf('.');

        let fileNameWithoutExtention = fileName.substring(0, lastDotIndex);
        let extention = fileName.substring(lastDotIndex);

        let newName = this.getNewFileOrFolderName(fileNameWithoutExtention, 0, usedFileNames);

        return newName + extention;
    }

    downloadSalesforceFiles(bigFilesIds) {
        if(bigFilesIds && bigFilesIds.length > 0) {
            let urlIdArrays = [];
            let maxUrlIdArrayLength = 100;

            for(let i = 0; i < bigFilesIds.length; i = i + maxUrlIdArrayLength) {
                let partialArray = bigFilesIds.slice(i, i + maxUrlIdArrayLength);
                urlIdArrays.push(partialArray);
            }

            for(let urlIdArray of urlIdArrays) {
                let a = document.createElement("a");
                a.href = '/sfc/servlet.shepherd/version/download/' + urlIdArray.join('/');
                a.style = "display: none";
                a.target = '_blank';
                document.body.appendChild(a);
                a.click();
                a.remove();
            }
        }
    }
    
    get showCancelButton() {
        return this.isFechingInfoStep || this.isProgressStep || this.isZippingStep;
    }

    get showOkButton() {
        return this.isWarningStep;
    }

    get showCloseButton() {
        return this.isSuccessStep;
    }

    nextToWarning() {
        this.isWarningStep = false;
        this.isZippingStep = true;
        let susessFulFiles = this.files.filter(item => item.isSuccess);
        if(susessFulFiles.length > 0) {
            this.downloadAsZip();
        }
        else {
            this.closeModal();
        }
    }
}