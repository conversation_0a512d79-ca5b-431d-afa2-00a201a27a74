<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>Accept</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Accept</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Accept</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Self_Service_Picklist_Record_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Self_Service_Picklist_Record_Page</content>
        <formFactor>Small</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <type>Default</type>
    </actionOverrides>
    <allowInChatterGroups>false</allowInChatterGroups>
    <compactLayoutAssignment>SYSTEM</compactLayoutAssignment>
    <deploymentStatus>Deployed</deploymentStatus>
    <description>Manage picklist options for custom build (SFP-51376)</description>
    <enableActivities>false</enableActivities>
    <enableBulkApi>false</enableBulkApi>
    <enableFeeds>false</enableFeeds>
    <enableHistory>true</enableHistory>
    <enableLicensing>false</enableLicensing>
    <enableReports>false</enableReports>
    <enableSearch>true</enableSearch>
    <enableSharing>false</enableSharing>
    <enableStreamingApi>false</enableStreamingApi>
    <externalSharingModel>Private</externalSharingModel>
    <fields>
        <fullName>Active__c</fullName>
        <defaultValue>true</defaultValue>
        <externalId>false</externalId>
        <label>Active</label>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>Controlling_Picklist__c</fullName>
        <deleteConstraint>SetNull</deleteConstraint>
        <externalId>false</externalId>
        <label>Controlling Picklist</label>
        <referenceTo>Self_Service_Picklist__c</referenceTo>
        <relationshipLabel>Picklist Dependencies</relationshipLabel>
        <relationshipName>Picklist_Dependencies</relationshipName>
        <required>false</required>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>Field__c</fullName>
        <externalId>false</externalId>
        <label>Field</label>
        <required>true</required>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Picklist</type>
        <valueSet>
            <controllingField>Object__c</controllingField>
            <restricted>true</restricted>
            <valueSetDefinition>
                <sorted>true</sorted>
                <value>
                    <fullName>Designation__c</fullName>
                    <default>false</default>
                    <label>Designation</label>
                </value>
                <value>
                    <fullName>Membership_Type__c</fullName>
                    <default>false</default>
                    <label>Membership Type</label>
                </value>
                <value>
                    <fullName>Other_Provider__c</fullName>
                    <default>false</default>
                    <label>Provider</label>
                </value>
                <value>
                    <fullName>University__c</fullName>
                    <default>false</default>
                    <label>University</label>
                </value>
            </valueSetDefinition>
            <valueSettings>
                <controllingFieldValue>MLC_Qualification__c</controllingFieldValue>
                <valueName>Other_Provider__c</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>MLC_Qualification__c</controllingFieldValue>
                <valueName>University__c</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>MLC_Membership_Designation__c</controllingFieldValue>
                <valueName>Designation__c</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>MLC_Membership_Designation__c</controllingFieldValue>
                <valueName>Membership_Type__c</valueName>
            </valueSettings>
        </valueSet>
    </fields>
    <fields>
        <fullName>Object__c</fullName>
        <externalId>false</externalId>
        <label>Object</label>
        <required>true</required>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Picklist</type>
        <valueSet>
            <restricted>true</restricted>
            <valueSetDefinition>
                <sorted>true</sorted>
                <value>
                    <fullName>MLC_Membership_Designation__c</fullName>
                    <default>false</default>
                    <label>Membership &amp; Designation</label>
                </value>
                <value>
                    <fullName>MLC_Qualification__c</fullName>
                    <default>false</default>
                    <label>Qualification</label>
                </value>
            </valueSetDefinition>
        </valueSet>
    </fields>
    <label>Self-Service Picklist</label>
    <listViews>
        <fullName>All</fullName>
        <columns>RECORDTYPE</columns>
        <columns>NAME</columns>
        <columns>Object__c</columns>
        <columns>Field__c</columns>
        <columns>Active__c</columns>
        <filterScope>Everything</filterScope>
        <label>All</label>
    </listViews>
    <nameField>
        <encryptionScheme>None</encryptionScheme>
        <label>Picklist Option</label>
        <trackHistory>true</trackHistory>
        <type>Text</type>
    </nameField>
    <pluralLabel>Self-Service Picklists</pluralLabel>
    <recordTypeTrackHistory>true</recordTypeTrackHistory>
    <recordTypes>
        <fullName>Learning_Picklist</fullName>
        <active>true</active>
        <label>Learning Picklist</label>
        <picklistValues>
            <picklist>Field__c</picklist>
            <values>
                <fullName>Designation%5F%5Fc</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Membership_Type%5F%5Fc</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Other_Provider%5F%5Fc</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>University%5F%5Fc</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>Object__c</picklist>
            <values>
                <fullName>MLC_Membership_Designation%5F%5Fc</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>MLC_Qualification%5F%5Fc</fullName>
                <default>false</default>
            </values>
        </picklistValues>
    </recordTypes>
    <searchLayouts>
        <searchResultsAdditionalFields>RECORDTYPE</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Object__c</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Field__c</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Active__c</searchResultsAdditionalFields>
    </searchLayouts>
    <sharingModel>Read</sharingModel>
    <validationRules>
        <fullName>Object_Type_same_Picklist_Object</fullName>
        <active>true</active>
        <errorConditionFormula>AND(
!ISBLANK(Controlling_Picklist__c),
TEXT(Object__c) != TEXT(Controlling_Picklist__r.Object__c)
)</errorConditionFormula>
        <errorDisplayField>Object__c</errorDisplayField>
        <errorMessage>Object selected must be the same as the Controlling Picklist&apos;s Object</errorMessage>
    </validationRules>
    <visibility>Public</visibility>
</CustomObject>
