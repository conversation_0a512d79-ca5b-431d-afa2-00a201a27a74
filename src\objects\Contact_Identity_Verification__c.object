<?xml version="1.0" encoding="UTF-8"?> 
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>Accept</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Accept</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Accept</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <allowInChatterGroups>false</allowInChatterGroups>
    <compactLayoutAssignment>SYSTEM</compactLayoutAssignment>
    <deploymentStatus>Deployed</deploymentStatus>
    <enableActivities>false</enableActivities>
    <enableBulkApi>true</enableBulkApi>
    <enableFeeds>false</enableFeeds>
    <enableHistory>true</enableHistory>
    <enableLicensing>false</enableLicensing>
    <enableReports>true</enableReports>
    <enableSearch>true</enableSearch>
    <enableSharing>true</enableSharing>
    <enableStreamingApi>true</enableStreamingApi>
    <externalSharingModel>Private</externalSharingModel>
    <fields>
        <fullName>Address__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Address</label>
        <length>1000</length>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>LongTextArea</type>
        <visibleLines>3</visibleLines>
    </fields>
    <fields>
        <fullName>Case__c</fullName>
        <deleteConstraint>SetNull</deleteConstraint>
        <externalId>false</externalId>
        <label>Case</label>
        <referenceTo>Case</referenceTo>
        <relationshipLabel>Contact Identity Verifications</relationshipLabel>
        <relationshipName>Contact_Identity_Verifications</relationshipName>
        <required>false</required>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>Changed_bank_details__c</fullName>
        <defaultValue>false</defaultValue>
        <externalId>false</externalId>
        <label>Changed bank details</label>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>Changed_bank_details_expiry_date__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Changed bank details expiry date</label>
        <required>false</required>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>DateTime</type>
    </fields>
    <fields>
        <fullName>Consented_credit_history__c</fullName>
        <defaultValue>false</defaultValue>
        <externalId>false</externalId>
        <label>Consented credit history</label>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>Contact__c</fullName>
        <deleteConstraint>SetNull</deleteConstraint>
        <externalId>false</externalId>
        <label>Contact</label>
        <referenceTo>Account</referenceTo>
        <relationshipLabel>Contact Identity Verifications</relationshipLabel>
        <relationshipName>Contact_Identity_Verifications</relationshipName>
        <required>false</required>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>Country__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Country</label>
        <length>255</length>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Created_date_and_time__c</fullName>
        <defaultValue>Now()</defaultValue>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Created date and time</label>
        <required>false</required>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>DateTime</type>
    </fields>
    <fields>
        <fullName>Date_of_birth__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Date of birth</label>
        <required>false</required>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Date</type>
    </fields>
    <fields>
        <fullName>Email__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Email</label>
        <length>255</length>
        <required>false</required>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Expiry_date__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Expiry date</label>
        <required>false</required>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Date</type>
    </fields>
    <fields>
        <fullName>First_Name__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>First name</label>
        <length>255</length>
        <required>false</required>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Full_Name__c</fullName>
        <externalId>false</externalId>
        <formula>IF( ISBLANK( First_Name__c ), &apos;&apos;, First_Name__c )
&amp;
IF( ISBLANK( Middle_Name__c ), &apos;&apos;, &apos; &apos; &amp; Middle_Name__c )
&amp;
IF( ISBLANK( Last_Name__c ), &apos;&apos;, &apos; &apos; &amp; Last_Name__c )</formula>
        <formulaTreatBlanksAs>BlankAsZero</formulaTreatBlanksAs>
        <label>Full Name</label>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Identity_Verification_ID__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>true</externalId>
        <label>Identity Verification ID</label>
        <length>255</length>
        <required>false</required>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Identity_Verification_Status__c</fullName>
        <externalId>false</externalId>
        <label>Identity Verification status</label>
        <required>false</required>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Picklist</type>
        <valueSet>
            <restricted>true</restricted>
            <valueSetDefinition>
                <sorted>false</sorted>
                <value>
                    <fullName>VERIFIED</fullName>
                    <default>false</default>
                    <label>VERIFIED</label>
                </value>
                <value>
                    <fullName>VERIFIED_ADMIN</fullName>
                    <default>false</default>
                    <label>VERIFIED_ADMIN</label>
                </value>
                <value>
                    <fullName>VERIFIED_WITH_CHANGES</fullName>
                    <default>false</default>
                    <label>VERIFIED_WITH_CHANGES</label>
                </value>
                <value>
                    <fullName>IN_PROGRESS</fullName>
                    <default>false</default>
                    <label>IN_PROGRESS</label>
                </value>
                <value>
                    <fullName>PENDING</fullName>
                    <default>false</default>
                    <label>PENDING</label>
                </value>
                <value>
                    <fullName>LOCKED_OUT</fullName>
                    <default>false</default>
                    <label>LOCKED_OUT</label>
                </value>
                <value>
                    <fullName>SKIPPED</fullName>
                    <default>false</default>
                    <label>SKIPPED</label>
                </value>
            </valueSetDefinition>
        </valueSet>
    </fields>
    <fields>
        <fullName>Identity_verified_date_and_time__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Identity verified date and time</label>
        <required>false</required>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>DateTime</type>
    </fields>
    <fields>
        <fullName>Last_Name__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Last name</label>
        <length>255</length>
        <required>false</required>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Middle_Name__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Middle name</label>
        <length>255</length>
        <required>false</required>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Postcode__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Postal Code</label>
        <length>4</length>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Ruleset__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Ruleset</label>
        <length>255</length>
        <required>false</required>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>State__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>State</label>
        <length>4</length>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>StreetName__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Street Name</label>
        <length>255</length>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Suburb__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Suburb</label>
        <length>255</length>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Verification_Outcome__c</fullName>
        <externalId>false</externalId>
        <formula>IF(
ISPICKVAL(Identity_Verification_Status__c , &apos;VERIFIED&apos;), Full_Name__c &amp; &apos; is verified.&apos;,
 IF(
 ISPICKVAL(Identity_Verification_Status__c , &apos;VERIFIED_WITH_CHANGES&apos;), Full_Name__c &amp; &apos; is verified, with minor changes.&apos;,
  IF(
  ISPICKVAL(Identity_Verification_Status__c , &apos;PENDING&apos;), Full_Name__c &amp; &apos; is verified, pending review.&apos;,
   IF(
   ISPICKVAL(Identity_Verification_Status__c , &apos;LOCKED_OUT&apos;), Full_Name__c &amp; &apos; is locked out due to too many attempts.&apos;,
    IF(
	ISPICKVAL(Identity_Verification_Status__c , &apos;IN_PROGRESS&apos;), Full_Name__c &amp; &apos; is not yet verified.&apos;,
	&apos;&apos;
	)
   )
  )
 )
)</formula>
        <formulaTreatBlanksAs>BlankAsZero</formulaTreatBlanksAs>
        <label>Verification Outcome</label>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Verified_for__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Verified for</label>
        <length>255</length>
        <required>false</required>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <label>Contact Identity Verification</label>
    <listViews>
        <fullName>All</fullName>
        <columns>NAME</columns>
        <columns>Identity_Verification_Status__c</columns>
        <columns>Identity_verified_date_and_time__c</columns>
        <columns>Expiry_date__c</columns>
        <columns>Contact__c</columns>
        <columns>CREATED_DATE</columns>
        <columns>RECORDTYPE</columns>
        <filterScope>Everything</filterScope>
        <label>All</label>
    </listViews>
    <nameField>
        <displayFormat>IV-{0000}</displayFormat>
        <label>Contact Identity Verification Name</label>
        <trackHistory>true</trackHistory>
        <type>AutoNumber</type>
    </nameField>
    <pluralLabel>Contact Identity Verifications</pluralLabel>
    <recordTypeTrackHistory>true</recordTypeTrackHistory>
    <recordTypes>
        <fullName>Admin</fullName>
        <active>true</active>
        <label>Admin</label>
        <picklistValues>
            <picklist>Identity_Verification_Status__c</picklist>
            <values>
                <fullName>IN_PROGRESS</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>SKIPPED</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>VERIFIED_ADMIN</fullName>
                <default>true</default>
            </values>
        </picklistValues>
    </recordTypes>
    <recordTypes>
        <fullName>eVID</fullName>
        <active>true</active>
        <label>eVID</label>
        <picklistValues>
            <picklist>Identity_Verification_Status__c</picklist>
            <values>
                <fullName>IN_PROGRESS</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>LOCKED_OUT</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>PENDING</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>SKIPPED</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>VERIFIED</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>VERIFIED_ADMIN</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>VERIFIED_WITH_CHANGES</fullName>
                <default>false</default>
            </values>
        </picklistValues>
    </recordTypes>
    <searchLayouts>
        <searchResultsAdditionalFields>Identity_Verification_Status__c</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Identity_verified_date_and_time__c</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Expiry_date__c</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Contact__c</searchResultsAdditionalFields>
    </searchLayouts>
    <sharingModel>ReadWrite</sharingModel>
    <validationRules>
        <fullName>Identity_verified_date_and_time</fullName>
        <active>true</active>
        <errorConditionFormula>IF($Setup.Bypass__c.Validation_Rules__c, FALSE,
Identity_verified_date_and_time__c &gt; NOW())</errorConditionFormula>
        <errorMessage>Date cannot be in the future</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>eVID_RT_Verification_Status</fullName>
        <active>true</active>
        <errorConditionFormula>IF($Setup.Bypass__c.Validation_Rules__c, FALSE,
AND(RecordType.DeveloperName = &quot;eVID&quot;,
	OR (
		AND(
			ISCHANGED (Identity_Verification_Status__c),
					NOT(ISPICKVAL( Identity_Verification_Status__c, &quot;VERIFIED_ADMIN&quot;))
			),   
			ISCHANGED(Created_date_and_time__c), 
			ISCHANGED(Contact__c  )
			),
	OR ($Profile.Name = &quot;~Internal - Process Admin&quot;, $Profile.Name = &quot;~Internal - Contact Centre&quot;)
	)
)</errorConditionFormula>
        <errorDisplayField>Identity_Verification_Status__c</errorDisplayField>
        <errorMessage>You cannot select any value other than VERIFIED ADMIN</errorMessage>
    </validationRules>
    <visibility>Public</visibility>
</CustomObject>
