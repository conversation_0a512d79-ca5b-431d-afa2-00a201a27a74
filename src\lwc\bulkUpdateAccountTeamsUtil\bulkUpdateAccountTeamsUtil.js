const getConstants = () =>{
    const TYPES = {
        ACTION_TYPES : {
            "ADD" : "ACTION_ADD",
            "UPDATE" : "ACTION_UPDATE",
            "REMOVE" : "ACTION_REMOVE",
            "TRANSFER" : "ACTION_TRANSFER"
        },
        FILTER_TYPES : {
            "ALL" : "ALL",
            "ADVISER_NO_AT" : "ADVISER_NO_AT"
        }
    }
    return {
        ...TYPES,
        ACTION_OPTIONS : [
            { label: 'Add', value: TYPES.ACTION_TYPES.ADD},
            { label: 'Update', value: TYPES.ACTION_TYPES.UPDATE },
            { label: 'Remove', value: TYPES.ACTION_TYPES.REMOVE },
            { label: 'Transfer', value: TYPES.ACTION_TYPES.TRANSFER }
        ],
        FILTER_OPTIONS : [
            { label: 'Selected Accounts', value: TYPES.FILTER_TYPES.ALL},
            { label: 'Accounts with No Account Teams', value: TYPES.FILTER_TYPES.ADVISER_NO_AT }
        ],
        COLUMN_SETTINGS : [
            {
                type: 'url',
                fieldName: 'accountUrl',
                label: 'Account Name',
                typeAttributes: {
                    label: { fieldName: 'Name' },
                }
            },
            {
                type: 'text',
                fieldName: 'RecordTypeName',
                label: 'Account Record Type'
            },
            {
                type: 'url',
                fieldName: 'practiceUrl',
                label: 'Servicing Practice',
                typeAttributes: {
                    label: { fieldName: 'servicingPracticeName' },
                }
            }
        ],
        FIRST_INPUT_ROW_ID : 0,
        ROLE_NONE_VALUE: "ROLE_NONE_VALUE",
        VALIDATION_ERROR_NO_TEAM_MEMBER : "No user selected",
        VALIDATION_ERROR_NO_TEAM_ROLE : "No role selected",
        VALIDATION_ERROR_INVALID_INPUTS : "Please review errors on the page.",
        VALIDATION_ERROR_CANNOT_SELECT_SAME_USER : "User has already been selected."
    }
}

export {getConstants};