<?xml version="1.0" encoding="UTF-8"?>
<Report xmlns="http://soap.sforce.com/2006/04/metadata">
    <chart>
        <backgroundColor1>#FFFFFF</backgroundColor1>
        <backgroundColor2>#FFFFFF</backgroundColor2>
        <backgroundFadeDir>Diagonal</backgroundFadeDir>
        <chartSummaries>
            <axisBinding>y</axisBinding>
            <column>RowCount</column>
        </chartSummaries>
        <chartType>Donut</chartType>
        <enableHoverLabels>false</enableHoverLabels>
        <expandOthers>false</expandOthers>
        <groupingColumn>Ongoing_Services__c$Sales__c</groupingColumn>
        <legendPosition>Right</legendPosition>
        <location>CHART_BOTTOM</location>
        <showAxisLabels>true</showAxisLabels>
        <showPercentage>false</showPercentage>
        <showTotal>true</showTotal>
        <showValues>false</showValues>
        <size>Medium</size>
        <summaryAxisRange>Auto</summaryAxisRange>
        <textColor>#000000</textColor>
        <textSize>12</textSize>
        <titleColor>#000000</titleColor>
        <titleSize>18</titleSize>
    </chart>
    <columns>
        <field>Ongoing_Services__c$Servicing_Practice__c.Payee_Id__c</field>
    </columns>
    <columns>
        <field>Ongoing_Services__c$Servicing_Practice__c</field>
    </columns>
    <columns>
        <field>Ongoing_Services__c$Servicing_Adviser__c</field>
    </columns>
    <columns>
        <field>Ongoing_Services__c$Name</field>
    </columns>
    <columns>
        <field>Ongoing_Services__c$Customer__c</field>
    </columns>
    <columns>
        <field>Ongoing_Services__c$Customer__c.SCVID_Golden_Record__c</field>
    </columns>
    <columns>
        <field>Ongoing_Services__c$Client_1_Name__c</field>
    </columns>
    <columns>
        <field>Ongoing_Services__c$Secondary_Customer__c</field>
    </columns>
    <columns>
        <field>Ongoing_Services__c$Secondary_Customer__c.SCVID_Golden_Record__c</field>
    </columns>
    <columns>
        <field>Ongoing_Services__c$Client_2_Name__c</field>
    </columns>
    <columns>
        <field>Ongoing_Services__c$Agreement_Type__c</field>
    </columns>
    <columns>
        <field>Ongoing_Services__c$Comments__c</field>
    </columns>
    <columns>
        <field>Ongoing_Services__c$Start_Date__c</field>
    </columns>
    <columns>
        <field>Ongoing_Services__c$FDS_End_Date__c</field>
    </columns>
    <columns>
        <field>Ongoing_Services__c$OSA_Signed_Date__c</field>
    </columns>
    <columns>
        <aggregateTypes>Sum</aggregateTypes>
        <field>Ongoing_Services__c$Total_Amount__c</field>
    </columns>
    <columns>
        <field>Ongoing_Services__c$Customer__c.Party_Type__c</field>
    </columns>
    <filter>
        <criteriaItems>
            <column>Ongoing_Services__c$Agreement_Type__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>Short-Term Advice Agreement,Service Agreement</value>
        </criteriaItems>
        <criteriaItems>
            <column>Ongoing_Services__c$Servicing_Practice__c.Payee_Id__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>contains</operator>
            <value>NDFWD-W</value>
        </criteriaItems>
        <criteriaItems>
            <column>Ongoing_Services__c$Sales__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>Draft,Proposed,Active</value>
        </criteriaItems>
        <criteriaItems>
            <column>Ongoing_Services__c$Customer__c.Party_Type__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>Golden</value>
        </criteriaItems>
        <language>en_US</language>
    </filter>
    <format>Summary</format>
    <groupingsDown>
        <dateGranularity>Day</dateGranularity>
        <field>Ongoing_Services__c$Sales__c</field>
        <sortOrder>Asc</sortOrder>
    </groupingsDown>
    <name>BTI - Advice Agreements With Practice</name>
    <params>
        <name>co</name>
        <value>1</value>
    </params>
    <reportType>Advice_Agreements_with_Servicing_Practice__c</reportType>
    <scope>organization</scope>
    <showDetails>true</showDetails>
    <showGrandTotal>true</showGrandTotal>
    <showSubTotals>false</showSubTotals>
    <timeFrameFilter>
        <dateColumn>Ongoing_Services__c$CreatedDate</dateColumn>
        <interval>INTERVAL_CUSTOM</interval>
    </timeFrameFilter>
</Report>
