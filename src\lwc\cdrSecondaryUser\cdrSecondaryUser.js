/**
SFP-74650 - Open Banking Secondary Users - Management of financial accounts (Management Portal)
T&M Digital - This is the community page for the Secondary User data sharing; Visible for Account Owners of Joint Financial Accounts that enables/disables sharing- by Secondary Users
*/
import { LightningElement, track, wire, api } from 'lwc'; 
import getSecondaryUsersDataSharing from '@salesforce/apex/CdrSecondaryUserManagement.getSecondaryUsersDataSharing';
import getSecondaryUsersDataSharingForIndividualTrust from '@salesforce/apex/CdrSecondaryUserManagement.getSecondaryUsersDataSharingForIndividualTrust';
import fetchCDRSetting from '@salesforce/apex/CdrLandingDashboardController.fetchCDRSetting'; 
//Toast events
import {ShowToastEvent} from "lightning/platformShowToastEvent" 
//For navigation 
import { NavigationMixin } from 'lightning/navigation'; 
//CSS from static resource
import { loadStyle } from 'lightning/platformResourceLoader'; 
import cdrCss from '@salesforce/resourceUrl/CDR_Styles'; 
import LOGIN_LOGO from '@salesforce/resourceUrl/login_logo';
import CDR_LOGO from '@salesforce/resourceUrl/CDR_Logo';
// Custom labels
import CDR_Secondary_Data_Sharing from '@salesforce/label/c.CDR_Secondary_Data_Sharing';
import CDR_Secondary_Data_Sharing_1 from '@salesforce/label/c.CDR_Secondary_Data_Sharing_1';
import CDR_Secondary_Data_Sharing_K from '@salesforce/label/c.CDR_Secondary_Data_Sharing_K';
import CDR_Secondary_Data_Sharing_2 from '@salesforce/label/c.CDR_Secondary_Data_Sharing_2';
import CDR_Secondary_Data_Sharing_3 from '@salesforce/label/c.CDR_Secondary_Data_Sharing_3';
import CDR_Secondary_Data_Sharing_4 from '@salesforce/label/c.CDR_Secondary_Data_Sharing_4';
import CDR_Secondary_Data_Sharing_4_1 from '@salesforce/label/c.CDR_Secondary_Data_Sharing_4_1'; //SFP-75110
import CDR_Secondary_Data_Sharing_5 from '@salesforce/label/c.CDR_Secondary_Data_Sharing_5';
import CDR_Secondary_Data_Sharing_6 from '@salesforce/label/c.CDR_Secondary_Data_Sharing_6';
import CDR_PagePaper_Form_URL from '@salesforce/label/c.CDR_PagePaper_Form_URL'; //SFP-75110

export default class cdrSecondaryUser extends NavigationMixin(LightningElement) { 
    
    @track sharingEnabledFinAccts = []; 
    @track sharingDisabledFinAccts = [];
    @track enabledFinAccts = [];
    @track cdrFAQLink = '';
    @track cdrJointAcctSharing = false; 

    loaded; 
    error; 
    noSecondaryUsers = false;  
    loginLogo = LOGIN_LOGO;
    cdrLogo = CDR_LOGO;
    
    legend_color=  'status-style_border status-style_green-color';
    legend_color2=  'status-style_border status-style_grey-color';

    
    labels = {                 
        
        CDR_Secondary_Data_Sharing_1,
        CDR_Secondary_Data_Sharing,
        CDR_Secondary_Data_Sharing_K,
        CDR_Secondary_Data_Sharing_2,
        CDR_Secondary_Data_Sharing_3,
        CDR_Secondary_Data_Sharing_4,
        CDR_Secondary_Data_Sharing_4_1,
        CDR_Secondary_Data_Sharing_5,
        CDR_Secondary_Data_Sharing_6,
        CDR_PagePaper_Form_URL
        
    };

    secondaryUserAccId;
    indivTrustAccId;
    isIndivTrust = false;

    constructor() { 
        super(); 
        Promise.all([ 
            loadStyle(this, cdrCss) 
        ]).then(() => { 
            // console.log("Loaded css"); 
        }).catch(error => { 
            console.error('Error loading static resources', error); 
        }); 
    }

    connectedCallback(){
        if(this.getIndivTrustId()){
            this.indivTrustAccId = this.getIndivTrustId();
            this.isIndivTrust = true;
            console.log(this.indivTrustAccId + ' ' +  this.isIndivTrust);
        }

        fetchCDRSetting({ 
            cdrRecord: 'CDR Management Settings'
        }).then(data => {
            if(data){
                let cdrMngtSettings = JSON.parse(data);
                this.cdrFAQLink = cdrMngtSettings.CDR_FAQ__c;
                this.cdrJointAcctSharing = cdrMngtSettings.Joint_Accounts_Sharing__c; //SFP-61733
                this.secondaryUserSharing = cdrMngtSettings.Secondary_Users_Accounts_Sharing__c;
            }      
            else{
                console.log('Error fetching CDR Management Settings: ', error);
            }
        }).catch(error => {
            console.log('Error fetching CDR Management Settings: ', error);
        });

        if(this.isIndivTrust){
            console.log('true' + this.isIndivTrust);
            getSecondaryUsersDataSharingForIndividualTrust({
                individualTrusteeAccId: this.indivTrustAccId
            })
            .then(data => {
                if(data){
                    //remove spinner when loaded
                    this.loaded = true; 
                    for(var key in data){ 
                        var arrangementData = data[key]; 
                        var newMap = []; 
                        for (var key2 in arrangementData) { 
                            newMap.push({ 
                                value: arrangementData[key2], 
                                key: key2 
                            }) 
                        }
                        if(arrangementData['secondaryUserInstruction'] == 'Enabled'){ 
                            this.sharingEnabledFinAccts.push({value:newMap,  
                                key:key,  
                                secondaryUserName: arrangementData['secondaryUserName'], 
                                finAcctDetails: arrangementData['finAcctDetails'],
                                finAcctMasterDiscStatus: arrangementData['finAcctMasterDiscStatus'],
                                secondaryUserAccId: arrangementData['secondaryUserAccId'],
                                secondaryUserInstruction: arrangementData['secondaryUserInstruction']
                            });
                        }else{
                            this.sharingDisabledFinAccts.push({value:newMap,  
                                key:key,  
                                secondaryUserName: arrangementData['secondaryUserName'], 
                                finAcctDetails: arrangementData['finAcctDetails'],
                                finAcctMasterDiscStatus: arrangementData['finAcctMasterDiscStatus'],
                                secondaryUserAccId: arrangementData['secondaryUserAccId'],
                                secondaryUserInstruction: arrangementData['secondaryUserInstruction']
                            });
                        }
                    } 

                    if(this.sharingEnabledFinAccts.length === 0){ 
                        this.sharingEnabledFinAccts = false;
                    }
        
                    if(this.sharingDisabledFinAccts.length === 0){
                        this.sharingDisabledFinAccts = false;
                    }
        
                    // SFP-33107 by J.Mendoza 09/10/2020
                    if(!this.sharingEnabledFinAccts && !this.sharingDisabledFinAccts){
                        this.noSecondaryUsers = true;
                    }

                    //console.log('sharingEnabledFinAccts ' + JSON.stringify(sharingEnabledFinAccts));
                    //console.log('sharingDisabledFinAccts ' + JSON.stringify(sharingDisabledFinAccts));
                }
                else{
                    this.error = error;
                    console.log('Error fetching Data Sharing Arrangements: ', error);
                    this.showToast('An unexpected error occurred when retrieving data sharing arrangements', 'error', 'Please contact your system administrator.');
                }
            }).catch(error => {
                this.error = error;
                console.log('Error fetching Data Sharing Arrangements: ', error);
                this.showToast('An unexpected error occurred when retrieving data sharing arrangements', 'error', 'Please contact your system administrator.');
            })
        }else{
            console.log('false' + this.isIndivTrust);
            getSecondaryUsersDataSharing()
            .then(data => {
                if(data){
                    //remove spinner when loaded
                    this.loaded = true; 
                    for(var key in data){ 
                        var arrangementData = data[key]; 
                        var newMap = []; 
                        for (var key2 in arrangementData) { 
                            newMap.push({ 
                                value: arrangementData[key2], 
                                key: key2 
                            }) 
                        }
                        if(arrangementData['secondaryUserInstruction'] == 'Enabled'){ 
                            this.sharingEnabledFinAccts.push({value:newMap,  
                                key:key,  
                                secondaryUserName: arrangementData['secondaryUserName'], 
                                finAcctDetails: arrangementData['finAcctDetails'],
                                finAcctMasterDiscStatus: arrangementData['finAcctMasterDiscStatus'],
                                secondaryUserAccId: arrangementData['secondaryUserAccId'],
                                secondaryUserInstruction: arrangementData['secondaryUserInstruction']
                            });
                        }else{
                            this.sharingDisabledFinAccts.push({value:newMap,  
                                key:key,  
                                secondaryUserName: arrangementData['secondaryUserName'], 
                                finAcctDetails: arrangementData['finAcctDetails'],
                                finAcctMasterDiscStatus: arrangementData['finAcctMasterDiscStatus'],
                                secondaryUserAccId: arrangementData['secondaryUserAccId'],
                                secondaryUserInstruction: arrangementData['secondaryUserInstruction']
                            });
                        }
                    } 

                    if(this.sharingEnabledFinAccts.length === 0){ 
                        this.sharingEnabledFinAccts = false;
                    }
        
                    if(this.sharingDisabledFinAccts.length === 0){
                        this.sharingDisabledFinAccts = false;
                    }
        
                    // SFP-33107 by J.Mendoza 09/10/2020
                    if(!this.sharingEnabledFinAccts && !this.sharingDisabledFinAccts){
                        this.noSecondaryUsers = true;
                    }

                    //console.log('sharingEnabledFinAccts ' + JSON.stringify(sharingEnabledFinAccts));
                    //console.log('sharingDisabledFinAccts ' + JSON.stringify(sharingDisabledFinAccts));
                }
                else{
                    this.error = error;
                    console.log('Error fetching Data Sharing Arrangements: ', error);
                    this.showToast('An unexpected error occurred when retrieving data sharing arrangements', 'error', 'Please contact your system administrator.');
                }
            }).catch(error => {
                this.error = error;
                console.log('Error fetching Data Sharing Arrangements: ', error);
                this.showToast('An unexpected error occurred when retrieving data sharing arrangements', 'error', 'Please contact your system administrator.');
            })
        }

    }

    //handle toast events
    showToast(toastTitle, toastVariant, toastMessage){
        this.dispatchEvent(
            new ShowToastEvent({
                title: toastTitle,
                variant: toastVariant,
                message: toastMessage
            }),
        );
    }

    //Handle tile clicks
    tileOnclick(event) {
        if(this.isIndivTrust){
            event.preventDefault();
            console.log('clicked ' + event.currentTarget.dataset.id);
            //Navigate to /data-sharing-arrangement?arrangement=arrangementId
            this[NavigationMixin.Navigate]({
                type: 'comm__namedPage',
                attributes: {
                    pageName: 'manage-secondary-user-instructions'
                } ,
                state: {
                    'account': event.currentTarget.dataset.id,
                    'trustAcc': this.indivTrustAccId
                } 
            });

        }else{
            event.preventDefault();
            console.log('clicked ' + event.currentTarget.dataset.id);
            //Navigate to /data-sharing-arrangement?arrangement=arrangementId
            this[NavigationMixin.Navigate]({
                type: 'comm__namedPage',
                attributes: {
                    pageName: 'manage-secondary-user-instructions'
                } ,
                state: {
                    'account': event.currentTarget.dataset.id
                } 
            });
        }
        
        return false;
    }

    getIndivTrustId() {
        const queryString = window.location.search;
        
        const urlParams = new URLSearchParams(queryString);

        const account = urlParams.get('account');

        //console.log('account Id: ' + account);

        return account;
        
    }
}