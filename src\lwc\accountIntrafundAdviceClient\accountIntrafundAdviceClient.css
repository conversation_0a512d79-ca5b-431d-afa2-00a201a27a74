/* Main */
.slds-section.ifa-client {
    margin-top: -16px;
    padding-bottom: 16px;
}
.slds-section .slds-section__content {
    padding-top: .75rem;
    transition: opacity .4s ease-in-out, max-height .4s ease-in-out;
    max-height: 100%;
    height: auto;
    opacity: 1;
    overflow: visible;
    visibility: visible;
}
.slds-section_hide .slds-section__content {
    max-height: 0;
    opacity: 0;
}
.slds-section .slds-section__title-action svg {
    transition: transform .4s ease-in-out;
    transform: rotate(0deg);
}
.slds-section_hide .slds-section__title-action svg {
    transform: rotate(-90deg);
}
h2.slds-section__title {
    font-weight: bold;
}

/* Edit buttons */
.ifa-footer-buttons {
    padding-bottom: 10px;
}
.slds-form-element_edit:hover .edit-button_visibility {
    opacity: 1;
}
.slds-form-element_edit .edit-button_visibility {
    opacity: 0.2;
}

/* Spinner */
.ifa-spinner {
    width: 100%;
    height: 3.85%;
    display: flex;
    position: absolute;
    background-color: rgba(178, 173, 173, 0.5);
}