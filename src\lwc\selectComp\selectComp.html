<template>
    <div class={parentClassName}>
        <div class="slds-form-element__control">
          <div class="slds-select_container">
            <select class="slds-select" id="select-01" required="" onchange={handleSelectedValue}>
                <option value="Select">{placeholder}</option>
                <template for:each = {options} for:item="item">
                    <option key={item.value} value={item.value}>{item.label}</option>
                 </template>
            </select>
          </div>
        </div>
        <template if:true={showError}>
            <div class="slds-form-element__help" id="error-01">Select an option.</div>
        </template>
      </div>
</template>