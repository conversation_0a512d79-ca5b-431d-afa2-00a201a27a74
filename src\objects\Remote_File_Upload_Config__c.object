<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <customSettingsType>Hierarchy</customSettingsType>
    <enableFeeds>false</enableFeeds>
    <fields>
        <fullName>Base_Folder__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Base Folder</label>
        <length>255</length>
        <required>false</required>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Chunk_Size__c</fullName>
        <externalId>false</externalId>
        <label>Chunk Size</label>
        <precision>18</precision>
        <required>false</required>
        <scale>0</scale>
        <trackTrending>false</trackTrending>
        <type>Number</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Download_Chunk_Size__c</fullName>
        <externalId>false</externalId>
        <label>Download Chunk Size</label>
        <precision>18</precision>
        <required>false</required>
        <scale>0</scale>
        <trackTrending>false</trackTrending>
        <type>Number</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Drive_Name__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Drive Name</label>
        <length>255</length>
        <required>false</required>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Named_Credential__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Named Credential</label>
        <length>255</length>
        <required>false</required>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Site_Id__c</fullName>
        <description>Need site in the case when only Sites.Selected permission is added to the app.</description>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <inlineHelpText>Need site in the case when only Sites.Selected permission is added to the app.</inlineHelpText>
        <label>Site Id</label>
        <length>255</length>
        <required>false</required>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Site_Name__c</fullName>
        <description>This field contains the site name for SharePoint Sites.</description>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <inlineHelpText>Provide a value if &apos;Is Main Site&apos; is checked.</inlineHelpText>
        <label>Site Name</label>
        <length>255</length>
        <required>false</required>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <label>Remote File Upload Config</label>
    <visibility>Public</visibility>
</CustomObject>
