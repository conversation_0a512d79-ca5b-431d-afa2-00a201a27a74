import { LightningElement, track, wire, api } from 'lwc'; 
 //For navigation
 import { NavigationMixin } from 'lightning/navigation'; 
 //CSS from static resource
 import { loadStyle } from 'lightning/platformResourceLoader';
 import {ShowToastEvent} from "lightning/platformShowToastEvent"
 import cdrCss from '@salesforce/resourceUrl/CDR_Styles'; 
 import LOGIN_LOGO from '@salesforce/resourceUrl/login_logo';
 import getEligiblePartyAccountsOfLoggedInUserAsNomRepAndTrustee from '@salesforce/apex/CdrAuthBusinessAccController.getEligiblePartyAccountsOfLoggedInUserAsNomRepAndTrustee';
 import getAvailableProfilesOfLoggedInUserAsSecondaryUser from '@salesforce/apex/CdrSecondaryUserManagement.getAvailableProfilesOfLoggedInUserAsSecondaryUser';
 import fetchCDRSetting from '@salesforce/apex/CdrLandingDashboardController.fetchCDRSetting'; 

 export default class cdrManagementProfileSelect extends NavigationMixin(LightningElement) {

    @api picklistValue;
    error; 
    loginLogo = LOGIN_LOGO;
    @track profileArray = [];
    @api profileListWrapper;
    @api profileListWrapperSecondaryUser;
    loggedInUserName;
    businessAccSharing = false;
    secondaryUserSharing = false;

     constructor() { 
         super(); 
         Promise.all([ 
             loadStyle(this, cdrCss) 
         ]).then(() => { 
             // console.log("Loaded css"); 
         }).catch(error => { 
             console.error('Error loading static resources', error); 
         }); 
     }


    connectedCallback(){
       
        getEligiblePartyAccountsOfLoggedInUserAsNomRepAndTrustee()
        .then(data => {
            if(data){
                this.profileListWrapper = data;
                for(let profileData of data){
                    this.loggedInUserName = profileData.loggedInUserName;
                }
                console.log('loggedInUserName: ' + this.loggedInUserName);
            }
        }).catch(error => {
            this.showToast('An unexpected error occurred', 'error', 'Please contact your system administrator.');
            console.log('Error message: ' + error);
        });

        //Start: Secondary user changes (Jun'23)
        getAvailableProfilesOfLoggedInUserAsSecondaryUser()
        .then(data => {
            if(data){
                this.profileListWrapperSecondaryUser = data;
            }
        }).catch(error => {
            this.showToast('An unexpected error occurred', 'error', 'Please contact your system administrator.');
            console.log('Error message: ' + JSON.stringify(error));
        });
        //End: Secondary user changes (Jun'23)

        fetchCDRSetting({ 
            cdrRecord: 'CDR Management Settings'
        }).then(data => {
            if(data){
                let cdrMngtSettings = JSON.parse(data);
                this.businessAccSharing = cdrMngtSettings.Business_Business_Trust_Accounts_Sharing__c;
                this.secondaryUserSharing = cdrMngtSettings.Secondary_Users_Accounts_Sharing__c;
                console.log('is business acc sharing on ' + this.businessAccSharing);
            }      
            else{
                console.log('Error fetching CDR Management Settings: ', error);
            }
        }).catch(error => {
            console.log('Error fetching CDR Management Settings: ', error);
        });  
    }

    handleChange(event){
        this.picklistValue = event.detail.value;
    }

    get options() {

        var returnOptions = [];
        returnOptions.push({label: this.loggedInUserName , value: 'individual'})
        if(this.businessAccSharing){
            if(this.profileListWrapper){
                for(let profile of this.profileListWrapper){
                    if(profile.acc.Id){
                        returnOptions.push({label: profile.acc.Name , value: profile.acc.Id});
                    }
                }
            }
        }
        //Start: Secondary user changes (Jun'23)
        if(this.secondaryUserSharing){
            if(this.profileListWrapperSecondaryUser){
                for(let profile of this.profileListWrapperSecondaryUser){
                    if(profile.acc.Id){
                        if(profile.isIndividualTrust){
                            returnOptions.push({label: profile.acc.Name , value: 'indivTrust=' + profile.acc.Id});
                        }else{
                            returnOptions.push({label: profile.acc.Name + ' - Secondary User', value: 'secondaryUser=' + profile.acc.Id});
                        }
                    }
                }
            }
        }
        //End: Secondary user changes (Jun'23)
        console.log(JSON.stringify(returnOptions));
        return returnOptions;

    }

    get disableContinue(){
        let disableContinue;
        console.log('picklistValue ',this.picklistValue);
        if(this.picklistValue == ''){
            disableContinue = true;
        }else{
            disableContinue = false;
        }

        return disableContinue;
    }

    handleClick(event){

        if(this.picklistValue == 'individual'){
            event.preventDefault();
        //Navigate to /authorisation-business
        this[NavigationMixin.Navigate]({
            type: 'comm__namedPage',
            attributes: {
                pageName: 'management-individual'
            }
        });
            
        }else if(this.picklistValue.includes("secondaryUser")){
            event.preventDefault();
        //Navigate to /authorisation-business
        this[NavigationMixin.Navigate]({
            type: 'comm__namedPage',
            attributes: {
                pageName: 'management-secondary'
            },
            state: {
               'account': this.picklistValue.slice(14)
           }
        });
        }else if(this.picklistValue.includes("indivTrust")){
            event.preventDefault();
        //Navigate to /authorisation-business
        this[NavigationMixin.Navigate]({
            type: 'comm__namedPage',
            attributes: {
                pageName: 'management-business'
            },
            state: {
               'account': this.picklistValue.slice(11)
           }
        });

        }else{
            event.preventDefault();
         //Navigate to /authorisation-individual
         this[NavigationMixin.Navigate]({
             type: 'comm__namedPage',
             attributes: {
                 pageName: 'management-business'
             },
             state: {
                'account': this.picklistValue
            }
         });
        }

    }

    showToast(toastTitle, toastVariant, toastMessage, toastMode){
        this.dispatchEvent(
            new ShowToastEvent({
                title: toastTitle,
                variant: toastVariant,
                message: toastMessage,
                mode: toastMode
            }),
        );

        //if error occurs, we keep the spinner loading
        if(toastVariant === 'error'){
            //this.loaded = false;
        }
    }
 }