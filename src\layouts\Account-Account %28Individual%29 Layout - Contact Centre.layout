<?xml version="1.0" encoding="UTF-8"?> 
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Fields</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Principal_Bus_addr_same_as_Reg_Office__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Principal_Place_of_Business_Address__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Company_s_Australian_Address__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Australian_Address__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns/>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Communications Preferences</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Statements_updates_about_your_products__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Servicing_SMS_messages__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>News_insights__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Online_Statements_Commencement_Note_Sent__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Annual_Report__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Marketing Preferences</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Marketing_Calls__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Marketing_Emails__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Market_Research__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Marketing_Letters__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Marketing_SMS_messages__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Additional Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ParentId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Password__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Client_Status__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Servicing_Practice__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>RecordTypeId</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Client_XPLAN_Id__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FinServ__PrimaryContact__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
        <platformActionListItems>
            <actionName>Account.New_Task_Account</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>0</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Account.New_Event_Account</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>1</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Sales_Comms_Log</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>2</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Account.New_Case</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>3</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Account.New_3rd_Party_Authority_Individual</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>4</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Account.Create_New_Alert</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>5</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Account.New_Super_Search</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>6</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Account.Notification_of_Death</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>7</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Account.Goals_Exploration</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>8</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Account.Traditional_Advice</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>9</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>dsfs__Send_with_DocuSign</actionName>
            <actionType>CustomButton</actionType>
            <sortOrder>10</sortOrder>
        </platformActionListItems>
    </platformActionList>
    <relatedLists>
        <fields>CASES.CASE_NUMBER</fields>
        <fields>CASES.SUBJECT</fields>
        <fields>CASES.CREATED_DATE</fields>
        <fields>CASES.PRIORITY</fields>
        <relatedList>Case.Account_Name__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>TASK.SUBJECT</fields>
        <fields>TASK.WHO_NAME</fields>
        <fields>TASK.WHAT_NAME</fields>
        <fields>ACTIVITY.TASK</fields>
        <fields>TASK.DUE_DATE</fields>
        <fields>TASK.STATUS</fields>
        <fields>TASK.CLOSED</fields>
        <fields>TASK.PRIORITY</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <fields>TASK.LAST_UPDATE</fields>
        <relatedList>Activity.FinServ__Household__c</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedContentNoteList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Address_History__c.Customer_Name__c</relatedList>
    </relatedLists>
    <relatedLists>
        <excludeButtons>New</excludeButtons>
        <fields>NAME</fields>
        <fields>Applies_to_all_Financial_Accounts__c</fields>
        <fields>FinServ__FinancialAccount__c</fields>
        <fields>FinServ__Message__c</fields>
        <fields>Active__c</fields>
        <fields>Start_Date__c</fields>
        <fields>End_Date__c</fields>
        <relatedList>FinServ__Alert__c.FinServ__Account__c</relatedList>
        <sortField>Active__c</sortField>
        <sortOrder>Desc</sortOrder>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>RECORDTYPE</fields>
        <fields>Category__c</fields>
        <fields>Overall_Status__c</fields>
        <fields>Action_Plan_Status__c</fields>
        <relatedList>Action_Plan__c.Related_To__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>FinServ__Status__c</fields>
        <fields>FinServ__FinancialAccountNumber__c</fields>
        <fields>FinServ__OpenDate__c</fields>
        <relatedList>FinServ__FinancialAccount__c.SCV_PrimaryOwner__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>FinServ__Status__c</fields>
        <fields>FinServ__FinancialAccountNumber__c</fields>
        <fields>FinServ__OpenDate__c</fields>
        <relatedList>FinServ__FinancialAccount__c.SCV_JointOwner__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>FinServ__FinancialAccountRole__c.SCV_RelatedAccount__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Restricted_Financial_Account__c.Account__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>FinServ__ContactContactRelation__c.Account__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>FinServ__AccountAccountRelation__c.FinServ__Account__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Financial_Account_Party__c.Account_Name__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Reporting_Country__c</fields>
        <fields>Record_Status__c</fields>
        <fields>Start_Date__c</fields>
        <fields>End_Date__c</fields>
        <fields>Account_Holder_TIN__c</fields>
        <fields>Reportable_Controlling_Person__c</fields>
        <fields>Controlling_Person_TIN__c</fields>
        <fields>CRS_Exempted_Entity__c</fields>
        <relatedList>Foreign_Tax_Residency__c.Account__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>OPPORTUNITY.NAME</fields>
        <fields>OPPORTUNITY.STAGE_NAME</fields>
        <fields>OPPORTUNITY.AMOUNT</fields>
        <fields>OPPORTUNITY.CLOSE_DATE</fields>
        <relatedList>RelatedOpportunityList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Tax_File_Number__c.Account_Name__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>XPLAN_Sync_Logs__c.Client__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Data_Recipient_Software_Product__c</fields>
        <fields>Status__c</fields>
        <fields>Start_Date__c</fields>
        <fields>End_Date__c</fields>
        <fields>CREATEDBY_USER</fields>
        <fields>CREATED_DATE</fields>
        <relatedList>Consent_Arrangement_Record__c.Client__c</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedEntityHistoryList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Contact_Identity_Verification__c.Contact__c</relatedList>
    </relatedLists>
    <relatedObjects>ParentId</relatedObjects>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00h8r000003qk3R</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
