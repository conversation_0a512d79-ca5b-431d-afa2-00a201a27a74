<?xml version="1.0" encoding="UTF-8"?>
<PermissionSet xmlns="http://soap.sforce.com/2006/04/metadata">
    <description>New Permission set for Arrears Management (Bank)</description>
    <hasActivationRequired>false</hasActivationRequired>
    <label>Arrears Management (Bank)</label>
    <objectPermissions>
        <allowCreate>true</allowCreate>
        <allowDelete>false</allowDelete>
        <allowEdit>true</allowEdit>
        <allowRead>true</allowRead>
        <modifyAllRecords>false</modifyAllRecords>
        <object>Case</object>
        <viewAllRecords>false</viewAllRecords>
    </objectPermissions>
    <recordTypeVisibilities>
        <recordType>Case.Arrears_Management_Bank</recordType>
        <visible>true</visible>
    </recordTypeVisibilities>
    <recordTypeVisibilities>
        <recordType>Case_Note__c.Case_Note_Arrears_Management_Bank</recordType>
        <visible>true</visible>
    </recordTypeVisibilities>
    <userPermissions>
        <enabled>true</enabled>
        <name>EditTask</name>
    </userPermissions>
    <userPermissions>
        <enabled>false</enabled>
        <name>EmailSingle</name>
    </userPermissions>
</PermissionSet>
