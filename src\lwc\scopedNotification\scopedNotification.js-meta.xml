<?xml version="1.0" encoding="UTF-8"?> 
<LightningComponentBundle xmlns="urn:metadata.tooling.soap.sforce.com" fqn="scopedNotification">
    <apiVersion>52.0</apiVersion>
    <isExposed>true</isExposed>
    <masterLabel>Scoped Notification</masterLabel>
    <targets>
        <target>lightning__AppPage</target>
        <target>lightning__RecordPage</target>
        <target>lightning__HomePage</target>
        <target>lightning__FlowScreen</target>
    </targets>
     <targetConfigs>
        <targetConfig targets="lightning__RecordPage,lightning__AppPage,lightning__HomePage,lightning__FlowScreen">
            <property name="success" type="Boolean" default="false" label="Show Message as Success" />
            <property name="warning" type="Boolean" default="false" label="Show Message as Warning" />
            <property name="error" type="Boolean" default="false" label="Show Message as Error" />
            <property name="infoLight" type="Boolean" default="false" label="Show Message as Info Light" />
            <property name="infoDark" type="Boolean" default="false" label="Show Message as Info Dark" />
            <property name="message" type="String" default="" label="Display Message"/>
        </targetConfig>
    </targetConfigs>

</LightningComponentBundle>