<template>
    <template if:true={isCardVisible}>
        <template if:true={allowMultipleSectionsOpen}>
            <div class="slds-card">
                <div class="accordionHeader">
                    <lightning-accordion allow-multiple-sections-open>
                    <template if:true={sectionHeading1}>
                        <lightning-accordion-section name="A" label={sectionHeading1}>
                                <lightning-formatted-rich-text
                                    value={sectionText1}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template>
                    <template if:true={sectionHeading2}>
                        <lightning-accordion-section name="B" label={sectionHeading2}>
                                <lightning-formatted-rich-text
                                    value={sectionText2}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading3}>
                        <lightning-accordion-section name="C" label={sectionHeading3}>
                                <lightning-formatted-rich-text
                                    value={sectionText3}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading4}>
                        <lightning-accordion-section name="D" label={sectionHeading4}>
                                <lightning-formatted-rich-text
                                    value={sectionText4}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading5}>
                        <lightning-accordion-section name="E" label={sectionHeading5}>
                                <lightning-formatted-rich-text
                                    value={sectionText5}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading6}>
                        <lightning-accordion-section name="F" label={sectionHeading6}>
                                <lightning-formatted-rich-text
                                    value={sectionText6}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading7}>
                        <lightning-accordion-section name="G" label={sectionHeading7}>
                                <lightning-formatted-rich-text
                                    value={sectionText7}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading8}>
                        <lightning-accordion-section name="H" label={sectionHeading8}>
                                <lightning-formatted-rich-text
                                    value={sectionText8}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading9}>
                        <lightning-accordion-section name="I" label={sectionHeading9}>
                                <lightning-formatted-rich-text
                                    value={sectionText9}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading10}>
                        <lightning-accordion-section name="J" label={sectionHeading10}>
                                <lightning-formatted-rich-text
                                    value={sectionText10}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading11}>
                        <lightning-accordion-section name="K" label={sectionHeading11}>
                                <lightning-formatted-rich-text
                                    value={sectionText11}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template>
                    </lightning-accordion>
                </div>
            </div>
        </template>
        <template if:false={allowMultipleSectionsOpen}>
            <div class="slds-card">
                <div class="accordionHeader">
                    <lightning-accordion>
                    <template if:true={sectionHeading1}>
                        <lightning-accordion-section name="A" label={sectionHeading1}>
                                <lightning-formatted-rich-text
                                    value={sectionText1}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template>
                    <template if:true={sectionHeading2}>
                        <lightning-accordion-section name="B" label={sectionHeading2}>
                                <lightning-formatted-rich-text
                                    value={sectionText2}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading3}>
                        <lightning-accordion-section name="C" label={sectionHeading3}>
                                <lightning-formatted-rich-text
                                    value={sectionText3}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading4}>
                        <lightning-accordion-section name="D" label={sectionHeading4}>
                                <lightning-formatted-rich-text
                                    value={sectionText4}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading5}>
                        <lightning-accordion-section name="E" label={sectionHeading5}>
                                <lightning-formatted-rich-text
                                    value={sectionText5}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading6}>
                        <lightning-accordion-section name="F" label={sectionHeading6}>
                                <lightning-formatted-rich-text
                                    value={sectionText6}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading7}>
                        <lightning-accordion-section name="G" label={sectionHeading7}>
                                <lightning-formatted-rich-text
                                    value={sectionText7}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading8}>
                        <lightning-accordion-section name="H" label={sectionHeading8}>
                                <lightning-formatted-rich-text
                                    value={sectionText8}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading9}>
                        <lightning-accordion-section name="I" label={sectionHeading9}>
                                <lightning-formatted-rich-text
                                    value={sectionText9}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading10}>
                        <lightning-accordion-section name="J" label={sectionHeading10}>
                                <lightning-formatted-rich-text
                                    value={sectionText10}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading11}>
                        <lightning-accordion-section name="K" label={sectionHeading11}>
                                <lightning-formatted-rich-text
                                    value={sectionText11}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template>
                    </lightning-accordion>
                </div>
            </div>  
        </template>
    </template>
    <template if:false={isCardVisible}>
        <template if:true={allowMultipleSectionsOpen}>
            <div class="slds-card">
                <div class="accordionHeader">
                    <lightning-accordion allow-multiple-sections-open>
                    <template if:true={sectionHeading1}>
                        <lightning-accordion-section name="A" label={sectionHeading1}>
                                <lightning-formatted-rich-text
                                    value={sectionText1}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template>
                    <template if:true={sectionHeading2}>
                        <lightning-accordion-section name="B" label={sectionHeading2}>
                                <lightning-formatted-rich-text
                                    value={sectionText2}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading3}>
                        <lightning-accordion-section name="C" label={sectionHeading3}>
                                <lightning-formatted-rich-text
                                    value={sectionText3}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading4}>
                        <lightning-accordion-section name="D" label={sectionHeading4}>
                                <lightning-formatted-rich-text
                                    value={sectionText4}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading5}>
                        <lightning-accordion-section name="E" label={sectionHeading5}>
                                <lightning-formatted-rich-text
                                    value={sectionText5}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading6}>
                        <lightning-accordion-section name="F" label={sectionHeading6}>
                                <lightning-formatted-rich-text
                                    value={sectionText6}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading7}>
                        <lightning-accordion-section name="G" label={sectionHeading7}>
                                <lightning-formatted-rich-text
                                    value={sectionText7}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading8}>
                        <lightning-accordion-section name="H" label={sectionHeading8}>
                                <lightning-formatted-rich-text
                                    value={sectionText8}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading9}>
                        <lightning-accordion-section name="I" label={sectionHeading9}>
                                <lightning-formatted-rich-text
                                    value={sectionText9}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading10}>
                        <lightning-accordion-section name="J" label={sectionHeading10}>
                                <lightning-formatted-rich-text
                                    value={sectionText10}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading11}>
                        <lightning-accordion-section name="K" label={sectionHeading11}>
                                <lightning-formatted-rich-text
                                    value={sectionText11}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template>
                    </lightning-accordion>
                </div>
            </div>
        </template>
        <template if:false={allowMultipleSectionsOpen}>
            <div class="slds-card">
                <div class="accordionHeader">
                    <lightning-accordion>
                    <template if:true={sectionHeading1}>
                        <lightning-accordion-section name="A" label={sectionHeading1}>
                                <lightning-formatted-rich-text
                                    value={sectionText1}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template>
                    <template if:true={sectionHeading2}>
                        <lightning-accordion-section name="B" label={sectionHeading2}>
                                <lightning-formatted-rich-text
                                    value={sectionText2}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading3}>
                        <lightning-accordion-section name="C" label={sectionHeading3}>
                                <lightning-formatted-rich-text
                                    value={sectionText3}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading4}>
                        <lightning-accordion-section name="D" label={sectionHeading4}>
                                <lightning-formatted-rich-text
                                    value={sectionText4}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading5}>
                        <lightning-accordion-section name="E" label={sectionHeading5}>
                                <lightning-formatted-rich-text
                                    value={sectionText5}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading6}>
                        <lightning-accordion-section name="F" label={sectionHeading6}>
                                <lightning-formatted-rich-text
                                    value={sectionText6}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading7}>
                        <lightning-accordion-section name="G" label={sectionHeading7}>
                                <lightning-formatted-rich-text
                                    value={sectionText7}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading8}>
                        <lightning-accordion-section name="H" label={sectionHeading8}>
                                <lightning-formatted-rich-text
                                    value={sectionText8}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading9}>
                        <lightning-accordion-section name="I" label={sectionHeading9}>
                                <lightning-formatted-rich-text
                                    value={sectionText9}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading10}>
                        <lightning-accordion-section name="J" label={sectionHeading10}>
                                <lightning-formatted-rich-text
                                    value={sectionText10}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template><template if:true={sectionHeading11}>
                        <lightning-accordion-section name="K" label={sectionHeading11}>
                                <lightning-formatted-rich-text
                                    value={sectionText11}
                                ></lightning-formatted-rich-text>
                        </lightning-accordion-section>
                    </template>
                    </lightning-accordion>
                </div>
            </div>  
        </template>
    </template>
</template>