import { LightningElement,api } from 'lwc'; 
export default class SampleRecordPage extends LightningElement {

    contactId2;
    dnbCreditConsentGiven;
    newStreetName;
    newSuburb;
    newState;
    newPostcode;
    newCountry;
    fName;
	mName;
	lName;
	email;
	completeAdd;
	dob;
    siteURL;
    @api recordId;
    baseUrl;

    connectedCallback() {
        this.baseUrl = window.location.origin;
        if(this.baseUrl.includes("ampfs") && !this.baseUrl.includes("sandbox")){
            this.siteURL = '/apex/GreenID_VF?contactId='+this.contactId2+'&dnbCreditConsentGiven='+this.dnbCreditConsentGiven+'&newStreetName='+this.newStreetName+'&newSuburb='+this.newSuburb+'&newState='+this.newState+'&newPostcode='+this.newPostcode+'&newCountry='+this.newCountry+'&fName='+this.fName+'&mName='+this.mName+'&lName='+this.lName+'&email='+this.email+'&completeAdd='+this.completeAdd+'&dob='+this.dob;       //Flow debug
        } else{
            this.siteURL = '/service/apex/GreenID_VF?contactId='+this.contactId2+'&dnbCreditConsentGiven='+this.dnbCreditConsentGiven+'&newStreetName='+this.newStreetName+'&newSuburb='+this.newSuburb+'&newState='+this.newState+'&newPostcode='+this.newPostcode+'&newCountry='+this.newCountry+'&fName='+this.fName+'&mName='+this.mName+'&lName='+this.lName+'&email='+this.email+'&completeAdd='+this.completeAdd+'&dob='+this.dob;  //Community
        }
        console.log('url@@@@@@@@@@@ '+this.siteURL);
        console.log('baseUrl@@@@@@@@@@@ '+this.baseUrl);

    }

    @api
	get contactId(){
	}
	set contactId(value){
		this.contactId2 = value;
	}

    @api
	get consentCredit(){
	}
	set consentCredit(value){
		this.dnbCreditConsentGiven = value;
	}
    
    @api
	get streetName(){
	}
	set streetName(value){
		this.newStreetName = value;
	}
    
    @api
	get suburb(){
	}
	set suburb(value){
		this.newSuburb = value;
	}
    
    @api
	get state(){
	}
	set state(value){
		this.newState = value;
	}
    
    @api
	get postcode(){
	}
	set postcode(value){
		this.newPostcode = value;
	}
    
    @api
	get country(){
	}
	set country(value){
		this.newCountry = value;
	}

    @api
	get firstName(){
	}
	set firstName(value){
		this.fName = value;
	}

	@api
	get middleName(){
	}
	set middleName(value){
		this.mName = value;
	}

	@api
	get lastName(){
	}
	set lastName(value){
		this.lName = value;
	}

	@api
	get electronicMail(){
	}
	set electronicMail(value){
		this.email = value;
	}

	@api
	get completeAddress(){
	}
	set completeAddress(value){
		this.completeAdd = value;
	}

	@api
	get dateOfBirth(){
	}
	set dateOfBirth(value){
		this.dob = value;
	}
}
