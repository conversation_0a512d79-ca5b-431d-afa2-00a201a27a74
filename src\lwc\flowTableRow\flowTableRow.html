<template> 
    <template if:true={isEditMode}>
		<template if:true={isPicklist}>
			<lightning-combobox
				data-id={itemId}
				name={inputName}
				label=""
				value={rowValue}
				placeholder="Please Select"
				options={localPicklistOptions}
				onchange={handleOnChangeInput}>
			</lightning-combobox>
		</template>
		
		
		<template if:false={isPicklist}>
			<!-- //SFP-59427, burhan, changed type={colType} to type={colTypeToUse}, max and step -->
			<lightning-input 
				data-id={itemId}
				type={colTypeToUse}
				name={inputName}
				label={label} 
				value={rowValue} 
				variant="label-hidden" 
				min={minValue}
				max={maxValue}
				step={step}
				onchange={handleOnChangeInput}>
			</lightning-input>
 
		</template>
	</template>
	<template if:false={isEditMode}>
		<div class="rowOutputValue">{rowValue}</div>
	</template>
</template>