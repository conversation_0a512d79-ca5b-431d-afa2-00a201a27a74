// @TestClass	PE_KnowledgeExchangeTrigger_Test
trigger PE_KnowledgeExchangeTrigger on Knowledge_Exchange_PE__e (after insert) {
	PE_Subscriber.createSubscriber(ConstantsPlatformEvents.PE_DEACTIVATE_LITMOS_USER).process((List<Knowledge_Exchange_PE__e>) Trigger.new);
	PE_Subscriber.createSubscriber(ConstantsPlatformEvents.PE_REACTIVATE_LITMOS_USER).process((List<Knowledge_Exchange_PE__e>) Trigger.new);
}