import { LightningElement, track, api, wire } from 'lwc';
import { loadScript } from 'lightning/platformResourceLoader';
import MomentJs from '@salesforce/resourceUrl/MomentJs';
import getUpcomingInteractions from '@salesforce/apex/ClientCommInteractionDataService.getUpcomingInteractions';
import getPrimaryAccountAdviser from '@salesforce/apex/ClientCommDataService.getPrimaryAccountAdviser';

const COLORS = ["rgb(var(--main-accent-color-rgb))",
"rgb(var(--main-accent2-color-rgb))","rgb(var(--main-accent3-color-rgb))","rgb(var(--main-bg2-color-rgb))",
"rgb(var(--main-text-color-rgb))"];

export default class ClientCommApptDateTimeline extends LightningElement {
    @track _apptDates = [];
    _adviserName = 'your adviser';
    _interactions = [];
    _dataParsed = false;

    @api accountId;


    constructor() {
        super();
        Promise.all([
            loadScript(this, MomentJs)
        ]).then(() => {
            console.log("Loaded static resources");
            if(!this._dataParsed && this._interactions.length > 0){
                this._parseInteractions();
            }
        }).catch(error => {
            console.error('Error loading static resources', error);
        });
    }

    @wire(getUpcomingInteractions, {accountId: '$accountId'})
    handleUpcomingInteractions({data, error}){
        if(data){
            this._interactions = data;
            if(moment){
                this._parseInteractions();
            }
            
        }else if(error){
            console.log(error);
        }
    }

    @wire(getPrimaryAccountAdviser, {accountId: '$accountId'})
    handleGetPrimaryAccountAdviser({data, error}){
        if(data?.Name){
            this._adviserName = "<strong>"+data.Name+"</strong>";
        }else if(error){
            console.log(error);
        }
    }


    /**
     * expected output:
     * [{ 'key' : 'january 2021'
     *    'value' :[
     *      {'id': 0,
     *       'date' : {
     *           'formatted' : '2 January 2021',
     *           'day' : '2',
     *           'month' : 'January'
     *       },
     *        'type' : 'Initial Engagement'
     *       },...
     * ],...
     * }]
     */
    _parseInteractions(){
        console.log(this._interactions);
        
        // Parse into Map for month-year grouping
        let i = 0;
        let parsedMap = this._interactions.reduce((apptDates, interaction) =>{
            const momentDate = moment(interaction['nextDate'], moment.ISO_8601);
            const month = momentDate.format('MMMM YYYY');
            let parsedRow = {
                'date' : {
                    'formatted' : momentDate.format('D MMMM YYYY'),
                    'day' : momentDate.format('D'),
                    'month' : momentDate.format('MMMM')
                },
                'type' : interaction['Interaction_Type__c'],
                'class' : (i === 0) ? "earliest" : '',
                'style' : this._getRandomStyle(i),
                'id' : i
            };
            if(apptDates.has(month)){
                apptDates.get(month).push(parsedRow);
            }else{
                apptDates.set(month, [parsedRow]);
            }
            i++;
            return apptDates;
        }, new Map()); 
        console.log(parsedMap);
        // Parse again to array form
        let parsedArray = [];
        for(const [key, value] of parsedMap.entries()){
            parsedArray.push({'key' : key, 'value' : value});
        }
        console.log(parsedArray);
        this._apptDates = parsedArray;
        this._dataParsed = true;
    }

    _getRandomStyle(i){
        return "border-color: "+COLORS[(i % COLORS.length)];
    }
}