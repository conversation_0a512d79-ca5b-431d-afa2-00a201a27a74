<?xml version="1.0" encoding="UTF-8"?>
<QuickAction xmlns="http://soap.sforce.com/2006/04/metadata">
    <fieldOverrides>
        <field>AccountId</field>
        <formula>Account.Id</formula>
    </fieldOverrides>
    <fieldOverrides>
        <field>Name</field>
        <formula>Account.FinServ__PrimaryContact__r.LastName &amp; 
&quot; Intrafund Advice &quot; &amp; 
TEXT(MONTH(TODAY())) &amp; &quot; &quot; &amp; TEXT(YEAR(TODAY()))</formula>
    </fieldOverrides>
    <label>New Opportunity</label>
    <optionsCreateFeedItem>true</optionsCreateFeedItem>
    <quickActionLayout>
        <layoutSectionStyle>TwoColumnsLeftToRight</layoutSectionStyle>
        <quickActionLayoutColumns>
            <quickActionLayoutItems>
                <emptySpace>false</emptySpace>
                <field>Name</field>
                <uiBehavior>Required</uiBehavior>
            </quickActionLayoutItems>
            <quickActionLayoutItems>
                <emptySpace>false</emptySpace>
                <field>Opportunity_Source__c</field>
                <uiBehavior>Edit</uiBehavior>
            </quickActionLayoutItems>
            <quickActionLayoutItems>
                <emptySpace>false</emptySpace>
                <field>CloseDate</field>
                <uiBehavior>Required</uiBehavior>
            </quickActionLayoutItems>
            <quickActionLayoutItems>
                <emptySpace>false</emptySpace>
                <field>Advice_Areas__c</field>
                <uiBehavior>Edit</uiBehavior>
            </quickActionLayoutItems>
        </quickActionLayoutColumns>
        <quickActionLayoutColumns>
            <quickActionLayoutItems>
                <emptySpace>false</emptySpace>
                <field>StageName</field>
                <uiBehavior>Required</uiBehavior>
            </quickActionLayoutItems>
            <quickActionLayoutItems>
                <emptySpace>false</emptySpace>
                <field>FinServ__FinancialAccount__c</field>
                <uiBehavior>Edit</uiBehavior>
            </quickActionLayoutItems>
            <quickActionLayoutItems>
                <emptySpace>false</emptySpace>
                <field>Account_Balance__c</field>
                <uiBehavior>Edit</uiBehavior>
            </quickActionLayoutItems>
        </quickActionLayoutColumns>
    </quickActionLayout>
    <targetObject>Opportunity</targetObject>
    <targetParentField>Account</targetParentField>
    <targetRecordType>Opportunity.Intrafund_Advice</targetRecordType>
    <type>Create</type>
</QuickAction>
