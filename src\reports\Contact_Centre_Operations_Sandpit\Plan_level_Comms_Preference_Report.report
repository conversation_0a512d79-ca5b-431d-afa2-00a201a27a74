<?xml version="1.0" encoding="UTF-8"?>
<Report xmlns="http://soap.sforce.com/2006/04/metadata">
    <columns>
        <field>FinServ__FinancialAccount__c$Plan_Short_Code__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$Plan_Number__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$Plan_Name__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$Account_Class__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$Account_Number_ProductCode__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$FinServ__PrimaryOwner__c.FinServ__PrimaryContact__c.Salutation</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$FinServ__PrimaryOwner__c.FinServ__PrimaryContact__c.FirstName</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$FinServ__Status__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$FinServ__PrimaryOwner__c.FinServ__PrimaryContact__c.LastName</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$FinServ__PrimaryOwner__c.Primary_Contact_Birthdate__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$FinServ__PrimaryOwner__c.Primary_Contact_Email__c</field>
    </columns>
    <columns>
        <aggregateTypes>Sum</aggregateTypes>
        <field>FinServ__FinancialAccount__c$FinServ__PrimaryOwner__c.Email_Invalid__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$FinServ__PrimaryOwner__c.Account_Mailing_Address__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$FinServ__PrimaryOwner__c.Statements_updates_about_your_products__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$FinServ__PrimaryOwner__c.Servicing_SMS_messages__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$FinServ__PrimaryOwner__c.Online_Statements_Commencement_Note_Sent__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$FinServ__PrimaryOwner__c.Annual_Report__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$FinServ__PrimaryOwner__c.News_insights__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$Product__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$CreatedDate</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$SCV_PrimaryOwner__c.Marketing_Calls__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$SCV_PrimaryOwner__c.Marketing_Emails__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$SCV_PrimaryOwner__c.Market_Research__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$SCV_PrimaryOwner__c.Marketing_Letters__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$SCV_PrimaryOwner__c.Marketing_SMS_messages__c</field>
    </columns>
    <description>Save this report using the plan name you are running it for.  Do not change this template.</description>
    <filter>
        <criteriaItems>
            <column>FinServ__FinancialAccount__c$Plan_Short_Code__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>9054625</value>
        </criteriaItems>
        <criteriaItems>
            <column>FinServ__FinancialAccount__c$FinServ__Status__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>Active</value>
        </criteriaItems>
        <language>en_US</language>
    </filter>
    <format>Tabular</format>
    <name>Plan level Comms Preference Report</name>
    <params>
        <name>co</name>
        <value>1</value>
    </params>
    <reportType>Plan_level_Comms_Preference__c</reportType>
    <scope>organization</scope>
    <showDetails>true</showDetails>
    <showGrandTotal>true</showGrandTotal>
    <showSubTotals>true</showSubTotals>
    <timeFrameFilter>
        <dateColumn>FinServ__FinancialAccount__c$FinServ__PrimaryOwner__c.Primary_Contact_Birthdate__c</dateColumn>
        <interval>INTERVAL_CUSTOM</interval>
    </timeFrameFilter>
</Report> 
