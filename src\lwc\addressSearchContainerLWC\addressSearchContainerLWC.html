<template>    
	<div class="cutomer-details-container" onclick={handleWrapperClick}>
		<template if:false={addressManually}>
			<!-- SFP-60672, burhan, added is-empty-address={isEmptyAddress} -->
			<c-ccd-form-input-l-w-c
			data-input-name="address"
			required
			is-empty-address={isEmptyAddress}
			input-type="search"
			input-label="Address"
			parent-input-value={address}
			input-name="address"
			oncheckaddressvalue={handleAddressValueCheck}
			oninputchange={addressValidation}>
			</c-ccd-form-input-l-w-c>
		</template>
		<template if:true={addressManually}>
			<h5 class="slds-p-vertical_large">Address</h5>
			<div class="address-search">Try the <a onclick={handleAddressManually}>address search</a> again</div>
			<lightning-layout horizontal-align="left" vertical-align="strech" >
			<lightning-layout-item size="8">
				<lightning-combobox
				data-input
				name="country"
				label="Country"
				value={country}
				parent-input-value={country}
				placeholder="Select country"
				options={countryList}
				onchange={handleChange} ></lightning-combobox>
			</lightning-layout-item>
			</lightning-layout>
			<c-ccd-form-input-l-w-c
			required
			input-type="text"
			input-label="Street"
			input-name="street"
			parent-input-value={street}
			oninputchange={handleChange}>
			</c-ccd-form-input-l-w-c>
			<c-ccd-form-input-l-w-c
			required={manualAddressFieldsRequired}
			input-type="text"
			input-label="Suburb"
			input-name="suburb"
			parent-input-value={suburb}
			oninputchange={handleChange}>
			</c-ccd-form-input-l-w-c>
			<template if:false={countrySelection}>
			<c-ccd-form-input-l-w-c
			input-type="text"
			input-label="City"
			input-name="city"
			parent-input-value={city}
			oninputchange={handleChange}>
			</c-ccd-form-input-l-w-c>
			</template>
			<lightning-layout horizontal-align="left" vertical-align="strech" >
			<lightning-layout-item size="6">
				<template if:true={countrySelection}>
					<lightning-combobox
					required={manualAddressFieldsRequired}
					data-input
					name="state"
					label="State"
					value={state}
					placeholder="Select state"
					options={stateList}
					onchange={handleChange} ></lightning-combobox>
				</template>
				<template if:false={countrySelection}>
					<c-ccd-form-input-l-w-c
					input-type="text"
					input-label="State"
					input-name="stateNonAus"
					parent-input-value={stateNonAus}
					oninputchange={handleChange}>
				</c-ccd-form-input-l-w-c>
				</template>
			</lightning-layout-item>
			</lightning-layout>
			<c-ccd-form-input-l-w-c
			required={manualAddressFieldsRequired}
			is-empty-address={isEmptyAddress}
			input-type="text"
			input-label="Post code"
			input-name="postcode"
			parent-input-value={postcode}
			oninputchange={handleChange}>
			</c-ccd-form-input-l-w-c>
		</template>
		<c-address-search-l-w-c
		address={address}
		onsearchresult={handleaddressSearchResult}
		onmanualaddress={handleAddressManually}
		></c-address-search-l-w-c>
	</div>
</template>