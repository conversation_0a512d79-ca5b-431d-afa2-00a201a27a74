import { LightningElement, api, wire, track } from 'lwc'; 
import { getConstants } from "c/bulkUpdateAccountTeamsUtil";

/** Apex methods from CustomLookupDataService */
import search from '@salesforce/apex/CustomLookupDataService.searchActiveUsers';
import getRecentlyViewed from '@salesforce/apex/CustomLookupDataService.getRecentlyViewedActiveUsers';

const CONSTANTS = getConstants();

/**
 * An account team member displayed in row form.
 * <AUTHOR> [Accenture]
 * @version 1 (21/04/2021)
 */
export default class AccountTeamMemberRow extends LightningElement {
    // "FROM" lookup attributes
    @track _lookupErrors = []; 
    recentlyViewed = [];
    _initialSelection = [];

    // "TO" lookup attributes
    @track _toLookupErrors = []; 
    _recentlyViewedToLookup = [];
    _toLookupSelection = [];

    _teamRoleOptions = [];
    _alreadySelectedUsers = []; // List of user ids that have already been selected on other rows and cannot be selected again.


    @api rowId; // Unique id for this component
    @api disableTeamMemberLookup = false;
    @api actionType; // Determines the inputs to display / hide.
    @api 
    get teamRoleOptions(){
        return this._teamRoleOptions;
    }
    set teamRoleOptions(_teamRoleOptions){
        let opts = [];
        if(Array.isArray(_teamRoleOptions)){
            opts = [..._teamRoleOptions];
            let i = opts.findIndex(r => {
                return r.value === 0;
            });
            if(i < 0){
                opts.unshift({ label: '- None -', value: CONSTANTS.ROLE_NONE_VALUE });
            }
        }
        
        this._teamRoleOptions = opts;
    }
    @api 
    get defaultTeamMember(){
        return this._initialSelection;
    }
    set defaultTeamMember(userDetails){
        if(userDetails){
            let teamMember = {
                id: userDetails.Id,
                sObjectType: 'User',
                icon: 'standard:user',
                title: userDetails.Name
            };
            this._initialSelection = [teamMember];

            this._fireRowChange([teamMember],'',[]);
        }
    }
    
    @api 
    get alreadySelectedUsers(){
        return this._alreadySelectedUsers;
    }; 
    set alreadySelectedUsers(_alreadySelectedUsers){
        // Get recently viewed that excludes users selected from other rows
        let params = {excludeUsers : _alreadySelectedUsers};
        if(this.actionType === CONSTANTS.ACTION_TYPES.REMOVE || this.actionType === CONSTANTS.ACTION_TYPES.TRANSFER){
            params.searchOnlyActiveUser = false; // SFP - 55221
            params.searchOnlyWithActiveRole = false;
        }
        getRecentlyViewed(params)
        .then((data) => {
            console.log(data); 
            if (data) {
                // Set the data to the "TO" lookup only
                this.recentlyViewed = data;
                this.initLookupDefaultResults();
            }
        })
        .catch((error) => {
            console.error('Lookup error', JSON.stringify(error));
            this._lookupErrors = [error];
        });
        this._alreadySelectedUsers = _alreadySelectedUsers;
    }; 

    /** Render conditionals */

    get _isActionTransfer(){
        return this.actionType === CONSTANTS.ACTION_TYPES.TRANSFER;
    }
    

    // Lookup properties and methods. See https://github.com/pozil/sfdc-ui-lookup-lwc for guidelines on implementation of custom lookups.

    

    /**
     * Loads recently viewed records and set them as default lookpup search results (optional)
     */
    @wire(getRecentlyViewed)
    getRecentlyViewed({ data }) {
        console.log(data); 
        if (data) {
            this.recentlyViewed = data;
            this._recentlyViewedToLookup = data;
            this.initLookupDefaultResults();
        }
    }


    connectedCallback() {
        this.initLookupDefaultResults();
    }

    /**
     * Initializes the lookup default results with a list of recently viewed records (optional).
     * Updates the lookup field default results by removing already seleted users.
     */
    initLookupDefaultResults() {
        // // Make sure that the lookup is present and if so, set its default results
        // const lookup = this.template.querySelector('c-lookup.fromLookup');
        // if (lookup) {
        //     lookup.setDefaultResults(this.recentlyViewed, true);       
        // }

        const fromLookup = this.template.querySelector('c-lookup.fromLookup');
        const toLookup = this.template.querySelector('c-lookup.toLookup');
        if(fromLookup){
            let toSeletedIds = this._getToLookupSelectedIds();
            fromLookup.setDefaultResults(
                this.recentlyViewed.filter(r => !toSeletedIds.includes(r.id)),
                true);
        }
        if(this._isActionTransfer && toLookup){
            const fromSeletedIds = this._getFromLookupSelectedIds();
            toLookup.setDefaultResults(
                this._recentlyViewedToLookup.filter(r => !fromSeletedIds.includes(r.id)),
                true);
        }
    }

    /**
     * Handles the lookup search event for the "FROM" lookup.
     * @param {event} event `search` event emmitted by the lookup
     */
    handleLookupSearch(event) {
        const lookupElement = event.target;
        const searchParams = {...event.detail};
        // Include already selected users (from other rows), and users selected in the "TO" lookup, to exclude from search
        searchParams.selectedIds = [...searchParams.selectedIds, 
            ...this.alreadySelectedUsers,
            ...this._getToLookupSelectedIds()];
        if(this.actionType === CONSTANTS.ACTION_TYPES.REMOVE || this.actionType === CONSTANTS.ACTION_TYPES.TRANSFER){
            searchParams.searchOnlyActiveUser = false; // SFP - 55221 
            searchParams.searchOnlyWithActiveRole = false;
        }
        this._callSearch(searchParams, lookupElement);
    }
    /**
     * Handles the lookup search event for the "TO" lookup.
     * @param {event} event `search` event emmitted by the lookup
     */
    handleToLookupSearch(event){
        const lookupElement = event.target;
        const searchParams = {...event.detail};
        // Include users selected in the "FROM" lookup to exclude from search
        searchParams.selectedIds = [...searchParams.selectedIds,
            ...this._getFromLookupSelectedIds()];
        
        this._callSearch(searchParams, lookupElement);
    }
    /**
     * Calls Apex endpoint to search for records and pass results to the lookup
     * @param {*} searchParams 
     * @param {*} lookupElement 
     */
    _callSearch(searchParams, lookupElement){
        search(searchParams)
        .then((results) => {
            lookupElement.setSearchResults(results);
        })
        .catch((error) => {
            console.error('Lookup error', JSON.stringify(error));
        });
    }

    /**
     * Handles the lookup selection change
     * @param {event} event `selectionchange` event emmitted by the lookup.
     * The event contains the list of selected ids.
     */
    // eslint-disable-next-line no-unused-vars
    handleLookupSelectionChange(event) {
        this.checkForErrors();
        this._fireRowChange();
    }
    
    checkForErrors() {
        this._lookupErrors = [];
        const fromSelection = this.template.querySelector('c-lookup.fromLookup').getSelection();
        
        if (this.actionType !== CONSTANTS.ACTION_TYPES.REMOVE && fromSelection.length === 0) {
            // Enforcing required field
            this._lookupErrors.push({ id: 0, message: CONSTANTS.VALIDATION_ERROR_NO_TEAM_MEMBER });
        }
        if(this.actionType === CONSTANTS.ACTION_TYPES.TRANSFER){
            // Validate "TO" lookup
            this._toLookupErrors = [];
            const toSelection = this.template.querySelector('c-lookup.toLookup').getSelection();
            // Enforcing required field
            if(toSelection.length === 0){
                this._toLookupErrors.push({ id: 0, message: CONSTANTS.VALIDATION_ERROR_NO_TEAM_MEMBER });
            }
        }
        
    }

    // <<< End of lookup methods


    _handleRoleChange(event){
        if(event.detail.value){
            const teamRole = this.template.querySelector('lightning-combobox');
            if(teamRole){
                teamRole.setCustomValidity(null);
            }
        }
        this._fireRowChange();
    }

    _fireRowChange(teamMember, teamRole, toTeamMember){
        let _teamMember = teamMember ?? this.template.querySelector('c-lookup.fromLookup').getSelection();
        let _teamRole = teamRole;
        if(!teamRole && this.template.querySelector('lightning-combobox')){
            _teamRole = this.template.querySelector('lightning-combobox').value;
        }
        const toLookup = this.template.querySelector('c-lookup.toLookup');
        let _toTeamMember;
        if(toTeamMember){
            _toTeamMember = toTeamMember;
        }else if(this._isActionTransfer && toLookup){
            _toTeamMember = toLookup.getSelection();
        }
        const selectedEvent = new CustomEvent('rowinputchange', { 
            detail: {
                'rowId' : this.rowId,
                'user' : _teamMember,
                'role' : _teamRole,
                'toUser' : _toTeamMember
            } 
        });
        this.dispatchEvent(selectedEvent); 
    }

    /**
     * Removes the selected users from the "FROM" and "TO" lookups from an array of options. Can be used
     * for the recently viewed options and the search result options.
     */
    // _filterLookupInputOptions(options){
    //     const fromLookup = this.template.querySelector('c-lookup.fromLookup');
    //     const toLookup = this.template.querySelector('c-lookup.toLookup');
        
    //     const fromSeletedIds = fromLookup?.getSelection()?.map(r => r.id);
    //     const toSeletedIds = toLookup?.getSelection()?.map(r => r.id);
    //     return options.filter(o => fromSeletedIds?.[0] !== o.id && toSeletedIds?.[0] !== o.id);
    // }

    /**
     * Gets the ids of users selected in the "FROM" lookup
     * @returns List of selected ids. e.g. ['0054Y000000mB7PQAU']
     */
    _getFromLookupSelectedIds(){
        const fromLookup = this.template.querySelector('c-lookup.fromLookup');
        const fromSeletedIds = fromLookup?.getSelection()?.map(r => r.id);
        return (Array.isArray(fromSeletedIds)) ? fromSeletedIds : [];
    }
    /**
     * Gets the ids of users selected in the "TO" lookup
     * @returns List of selected ids. e.g. ['0054Y000000mB7PQAU']
     */
    _getToLookupSelectedIds(){
        const toLookup = this.template.querySelector('c-lookup.toLookup');
        const toSeletedIds = toLookup?.getSelection()?.map(r => r.id);
        return (Array.isArray(toSeletedIds)) ? toSeletedIds : [];
    }

    // Component methods

    // Gets all inputs of this row
    @api
    getInputs(){
        const teamMember = this.template.querySelector('c-lookup.fromLookup').getSelection();
        const teamRoleLookup = this.template.querySelector('lightning-combobox');
        let teamRole;
        if(teamRoleLookup){
            teamRole = teamRoleLookup.value;
        }
        const toLookup = this.template.querySelector('c-lookup.toLookup');
        let toTeamMember;
        if(this._isActionTransfer && toLookup){
            toTeamMember = toLookup.getSelection();
        }
        return {
            'rowId' : this.rowId,
            'user' : teamMember,
            'role' : teamRole,
            'toUser' : toTeamMember
        }
    }

    // Sets validation errors
    @api
    setErrors(errors){
        console.log('errors', errors);
        for(let i=0; i < errors.length; i++){
            const e = errors[i];
            if(e.inputType === "teamRole"){
                const teamRole = this.template.querySelector('lightning-combobox');
                if(teamRole){
                    teamRole.setCustomValidity(e.message);
                    teamRole.reportValidity();
                }
            }else if(e.inputType === "teamMember"){
                this._lookupErrors = [{id: i,  message: e.message}];
            }
        }
    }

    // Removes all input validation errors
    @api
    resetErrors(){
        this._lookupErrors = [];
        const teamRole = this.template.querySelector('lightning-combobox');
        if(teamRole){
            teamRole.setCustomValidity(null);
            teamRole.reportValidity();
        }
    }

    // Removes all input validation errors
    // ! Will fire the rowinputchange event as well.
    @api
    resetInputs(excludeFirstLookup){
        
        if(this.rowId !== CONSTANTS.FIRST_INPUT_ROW_ID || !excludeFirstLookup){
            this._initialSelection = [];
        }

        let teamRoleCombo = this.template.querySelector('lightning-combobox');
        if(teamRoleCombo){
            teamRoleCombo.value = null;
        }
        
        this._toLookupSelection = [];

        this._fireRowChange([], null, []);
    }
}