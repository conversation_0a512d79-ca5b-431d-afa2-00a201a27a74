import { LightningElement, track, wire, api } from 'lwc';  
// For navigation
import { NavigationMixin } from 'lightning/navigation'; 
import { CurrentPageReference } from 'lightning/navigation';
// CSS from static resource
import { loadStyle } from 'lightning/platformResourceLoader';
import { ShowToastEvent } from "lightning/platformShowToastEvent";
import cdrCss from '@salesforce/resourceUrl/CDR_Styles'; 
import LOGIN_LOGO from '@salesforce/resourceUrl/login_logo';
import getEligiblePartyAccountsOfLoggedInUserAsNomRepAndTrustee from '@salesforce/apex/CdrAuthBusinessAccController.getEligiblePartyAccountsOfLoggedInUserAsNomRepAndTrustee';
import getAvailableProfilesOfLoggedInUserAsSecondaryUser from '@salesforce/apex/CdrSecondaryUserManagement.getAvailableProfilesOfLoggedInUserAsSecondaryUser';
import fetchCDRSetting from '@salesforce/apex/CdrLandingDashboardController.fetchCDRSetting'; 

export default class CdrAuthorisationLandingPage extends NavigationMixin(LightningElement) {

    @api picklistValue;
    error; 
    loginLogo = LOGIN_LOGO;
    @track profileArray = [];
    @api profileListWrapper;
    @api profileListWrapperSecondaryUser; // Secondary user changes (Jun'23)
    loggedInUserName;
    businessAccSharing = false;
    secondaryUserSharing = false;
    uriKey= null;
    scvid;
    adr;

    currentPageReference = null; 
    urlStateParameters = null;


    constructor() { 
        super(); 
        Promise.all([ 
            loadStyle(this, cdrCss) 
        ]).then(() => { 
            // console.log("Loaded css"); 
        }).catch(error => { 
            console.error('Error loading static resources', error); 
        }); 
    }
    //get URL Param
    @wire(CurrentPageReference)
        getStateParameters(currentPageReference) {
           if (currentPageReference) {
              this.urlStateParameters = currentPageReference.state;
              this.setParametersBasedOnUrl();
              console.log('uriKey'+this.uriKey);
           }
        }

        setParametersBasedOnUrl() {
           this.uriKey = this.urlStateParameters.requestUriKey || ''; 
        }


    connectedCallback() {
        //SFP-78773 - Jeff Gacusan
        //Adobe Analytics - PageLoad Tagging

        if (!window.digitalData) {
            
            window.digitalData = {};
            window.digitalData = {
                event: "pageload",
                page: {
                    pageInfo: {
                        stage: "userAuthenticated",
                        timestamp: Date.now(), // or a specific timestamp if you have one
                        ADR: ""
                    },
                },
                userInfo: {
                    profile: {
                        requestUriKey: this.uriKey,
                        consentId: "",
                        userID: ""
                    }
                }
            };
        }else{
            if (window.digitalData && window.digitalData.userInfo) {
                //console.log('DD on');
                this.uriKey = window.digitalData.userInfo.profile.requestUriKey? window.digitalData.userInfo.profile.requestUriKey:"";
                this.scvid = window.digitalData.userInfo.profile.userID? window.digitalData.userInfo.profile.userID:"";
                this.adr = window.digitalData.page.pageInfo.ADR? window.digitalData.page.pageInfo.ADR:"";
    
                window.digitalData = {
                    event: "pageload",
                    page: {
                        pageInfo: {
                            stage: "accountSelected",
                            timestamp: Date.now(), // or a specific timestamp if you have one
                            ADR: this.adr
                        },
                    },
                    userInfo: {
                        profile: {
                            requestUriKey: this.uriKey,
                            consentId: "",
                            userID: this.scvid
                        }
                    }
                };
            } else {
                console.log('digitalData.user is not available on page load.');
            }
            console.log('window.digitalData:', window.digitalData);;
        }

        getEligiblePartyAccountsOfLoggedInUserAsNomRepAndTrustee()
        .then(data => {
            if (data) {
                this.profileListWrapper = data;
                for (let profileData of data) {
                    this.loggedInUserName = profileData.loggedInUserName;
                }
                console.log('loggedInUserName: ' + this.loggedInUserName);
            }
        }).catch(error => {
            this.showToast('An unexpected error occurred', 'error', 'Please contact your system administrator.');
            console.log('Error message: ' + error);
        });

        // Start: Secondary user changes (Jun'23)
        getAvailableProfilesOfLoggedInUserAsSecondaryUser()
        .then(data => {
            if (data) {
                this.profileListWrapperSecondaryUser = data;
            }
        }).catch(error => {
            this.showToast('An unexpected error occurred', 'error', 'Please contact your system administrator.');
            console.log('Error message: ' + error);
        });
        // End: Secondary user changes (Jun'23)

        fetchCDRSetting({ 
            cdrRecord: 'CDR Authorisation Settings'
        }).then(data => {
            if (data) {
                let cdrMngtSettings = JSON.parse(data);
                this.businessAccSharing = cdrMngtSettings.Business_Business_Trust_Accounts_Sharing__c;
                this.secondaryUserSharing = cdrMngtSettings.Secondary_Users_Accounts_Sharing__c;
                console.log('is business acc sharing on ' + this.businessAccSharing);
            } else {
                console.log('Error fetching CDR Authorisation Settings: ', error);
            }
        }).catch(error => {
            console.log('Error fetching CCDR Authorisation Settings: ', error);
        });
    }
    // get pageReference() {
    //     return this[NavigationMixin.CurrentPageReference];
    // }
    handleClick(event) {
		
        // Existing handleClick logic
        if (this.picklistValue == 'individual') {
            event.preventDefault();
            // Navigate to /authorisation-individual
            this[NavigationMixin.Navigate]({
                type: 'comm__namedPage',
                attributes: {
                    pageName: 'authorisation-individual'
                }
            });
        } else if (this.picklistValue.includes("secondaryUser")) {
            event.preventDefault();
            // Navigate to /authorisation-secondary
            this[NavigationMixin.Navigate]({
                type: 'comm__namedPage',
                attributes: {
                    pageName: 'authorisation-secondary'
                },
                state: {
                   'account': this.picklistValue.slice(14)
               }
            });
        } else if (this.picklistValue.includes("indivTrust")) {
            event.preventDefault();
            // Navigate to /authorisation-business
            this[NavigationMixin.Navigate]({
                type: 'comm__namedPage',
                attributes: {
                    pageName: 'authorisation-business'
                },
                state: {
                   'account': this.picklistValue.slice(11)
               }
            });
        } else {
            event.preventDefault();
            // Navigate to /authorisation-business
            this[NavigationMixin.Navigate]({
                type: 'comm__namedPage',
                attributes: {
                    pageName: 'authorisation-business'
                },
                state: {
                    'account': this.picklistValue
                }
            });
        }
    }

    handleChange(event) {
        this.picklistValue = event.detail.value;
    }

    get options() {
        var returnOptions = [];

        returnOptions.push({ label: this.loggedInUserName, value: 'individual' });

        if (this.businessAccSharing) {
            if (this.profileListWrapper) {
                for (let profile of this.profileListWrapper) {
                    if (profile.acc.Id) {
                        returnOptions.push({ label: profile.acc.Name, value: profile.acc.Id });
                    }
                }
            }
        }

        // Start: Secondary user changes (Jun'23)
        if (this.secondaryUserSharing) {
            if (this.profileListWrapperSecondaryUser) {
                for (let profile of this.profileListWrapperSecondaryUser) {
                    if (profile.acc.Id) {
                        if (profile.isIndividualTrust) {
                            returnOptions.push({ label: profile.acc.Name, value: 'indivTrust=' + profile.acc.Id });
                        } else {
                            returnOptions.push({ label: profile.acc.Name + ' - Secondary User', value: 'secondaryUser=' + profile.acc.Id });
                        }
                    }
                }
            }
        }
        // End: Secondary user changes (Jun'23)
        console.log(JSON.stringify(returnOptions));
        return returnOptions;
    }

    get disableContinue() {
        let disableContinue;
        console.log('picklistValue ', this.picklistValue);
        if (this.picklistValue == '') {
            disableContinue = true;
        } else {
            disableContinue = false;
        }

        return disableContinue;
    }

    showToast(toastTitle, toastVariant, toastMessage, toastMode) {
        this.dispatchEvent(
            new ShowToastEvent({
                title: toastTitle,
                variant: toastVariant,
                message: toastMessage,
                mode: toastMode
            }),
        );

        // if error occurs, we keep the spinner loading
        if (toastVariant === 'error') {
            // this.loaded = false;
        }
    }
}