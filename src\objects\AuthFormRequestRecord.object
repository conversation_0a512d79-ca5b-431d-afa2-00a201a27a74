<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>

    
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <compactLayoutAssignment>SYSTEM</compactLayoutAssignment>
    <enableFeeds>false</enableFeeds>
    <externalSharingModel>ControlledByParent</externalSharingModel>
    <fields>
        <fullName>Account_Removal_Reason__c</fullName> 
        <description>SFP-75265</description>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Account Removal Reason</label>
        <length>255</length>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Account_Removed_By__c</fullName>
        <deleteConstraint>SetNull</deleteConstraint>
        <externalId>false</externalId>
        <label>Account Removed By</label>
        <referenceTo>Contact</referenceTo>
        <relationshipLabel>Authorizable Form Request Records (Account Removed By)</relationshipLabel>
        <relationshipName>Authorizable_Form_Request_Records1</relationshipName>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>Client_Consented_Record__c</fullName>
        <deleteConstraint>SetNull</deleteConstraint>
        <externalId>false</externalId>
        <label>Client Consented Record</label>
        <referenceTo>Client_Consented_Record__c</referenceTo>
        <relationshipLabel>Authorizable Form Request Records</relationshipLabel>
        <relationshipName>Authorizable_Form_Request_Records</relationshipName>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>Contact__c</fullName>
        <deleteConstraint>SetNull</deleteConstraint>
        <description>SFP-61288 - Open Banking for May22ER</description>
        <externalId>false</externalId>
        <label>Contact</label>
        <referenceTo>Contact</referenceTo>
        <relationshipLabel>Authorizable Form Request Records</relationshipLabel>
        <relationshipName>Authorizable_Form_Request_Records</relationshipName>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>End_Date__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>End Date</label>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <type>DateTime</type>
    </fields>
    <fields>
        <fullName>InfoAuthRequestFormId</fullName>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>InfoAuthorizationRequestId</fullName>
        <trackHistory>false</trackHistory>
        <type>MasterDetail</type>
    </fields>
    <fields>
        <fullName>PPID__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>PPID</label>
        <length>255</length>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>RelatedRecordId</fullName>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>Start_Date__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Start Date</label>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <type>DateTime</type>
    </fields>
    <fields>
        <fullName>Consent_Arrangement_Record__c</fullName>
        <deleteConstraint>SetNull</deleteConstraint>
        <description>SFP-62414</description>
        <externalId>false</externalId>
        <label>Consent Arrangement Record</label>
        <referenceTo>Consent_Arrangement_Record__c</referenceTo>
        <relationshipLabel>Authorizable Form Request Records</relationshipLabel>
        <relationshipName>Authorizable_Form_Request_Records</relationshipName>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>Financial_Account_Role__c</fullName>
        <deleteConstraint>SetNull</deleteConstraint>
        <description>SFP-62414</description>
        <externalId>false</externalId>
        <label>Financial Account Role</label>
        <referenceTo>FinServ__FinancialAccountRole__c</referenceTo>
        <relationshipLabel>Authorizable Form Request Records</relationshipLabel>
        <relationshipName>Authorizable_Form_Request_Records</relationshipName>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>Financial_Account__c</fullName>
        <deleteConstraint>SetNull</deleteConstraint>
        <description>SFP-62414</description>
        <externalId>false</externalId>
        <label>Financial Account</label>
        <referenceTo>FinServ__FinancialAccount__c</referenceTo>
        <relationshipLabel>Authorizable Form Request Records</relationshipLabel>
        <relationshipName>Authorizable_Form_Request_Records</relationshipName>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>

    <searchLayouts>
        <customTabListAdditionalFields>InfoAuthorizationRequest</customTabListAdditionalFields>
        <customTabListAdditionalFields>RelatedRecord</customTabListAdditionalFields>
        <customTabListAdditionalFields>InfoAuthRequestForm</customTabListAdditionalFields>
        <searchResultsAdditionalFields>Financial_Account__c</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Start_Date__c</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>End_Date__c</searchResultsAdditionalFields>
    </searchLayouts>
    <sharingModel>ControlledByParent</sharingModel>
</CustomObject>
