<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <compactLayoutAssignment>SYSTEM</compactLayoutAssignment>
    <enableFeeds>false</enableFeeds>
    <externalSharingModel>Private</externalSharingModel>
    <fields>
        <fullName>CommunicationType</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>Consent_Arrangement_Record__c</fullName>
        <deleteConstraint>SetNull</deleteConstraint>
        <externalId>false</externalId>
        <label>Consent Arrangement Record</label>
        <referenceTo>Consent_Arrangement_Record__c</referenceTo>
        <relationshipLabel>Info Authorization Requests</relationshipLabel>
        <relationshipName>Info_Authorization_Requests</relationshipName>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>ConsenterId</fullName>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>DataUsePurposeId</fullName>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>ExpirationDateTime</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>Locale</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>Name</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>PertainsToId</fullName>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>RequestedDateTime</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>RequesterId</fullName>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>ResponseStatus</fullName>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>SourceRecordId</fullName>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>
    <listViews>
        <fullName>My_InfoAuthorizationRequests</fullName>
        <filterScope>Mine</filterScope>
        <label>My Info Authorization Requests</label>
    </listViews>
    <searchLayouts>
        <customTabListAdditionalFields>DataUsePurpose</customTabListAdditionalFields>
        <customTabListAdditionalFields>Locale</customTabListAdditionalFields>
        <customTabListAdditionalFields>RequestedDateTime</customTabListAdditionalFields>
        <customTabListAdditionalFields>ExpirationDateTime</customTabListAdditionalFields>
        <customTabListAdditionalFields>CommunicationType</customTabListAdditionalFields>
        <searchResultsAdditionalFields>DataUsePurpose</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Locale</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>RequestedDateTime</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>ExpirationDateTime</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>CommunicationType</searchResultsAdditionalFields>
    </searchLayouts>
    <sharingModel>Private</sharingModel>
</CustomObject>
