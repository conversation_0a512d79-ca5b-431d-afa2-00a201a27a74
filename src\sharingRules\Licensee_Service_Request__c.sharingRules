<?xml version="1.0" encoding="UTF-8"?>
<SharingRules xmlns="http://soap.sforce.com/2006/04/metadata">
    <sharingCriteriaRules>
        <fullName>AML_ACIP_Assessment_sharing_to_KiSS_Team</fullName>
        <accessLevel>Edit</accessLevel>
        <description>Sharing Rule for KiSS Team SFP-67233</description>
        <label>AML ACIP Assessment sharing to KiSS Team</label>
        <sharedTo>
            <group>KiSS_Team</group>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>ACIP Assessment</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Adtech_Sharing_Rule</fullName>
        <accessLevel>Edit</accessLevel>
        <label>Adtech Sharing Rule</label>
        <sharedTo>
            <group>Adtech_Public_Group</group>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>Adtech</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Advice_Review_Sharing_Rule_AR</fullName>
        <accessLevel>Edit</accessLevel>
        <label>Advice Review Sharing Rule_AR</label>
        <sharedTo>
            <group>Advice_Review_Public_Group</group>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>Advice Review</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Advice_Review_Sharing_Rule_CM</fullName>
        <accessLevel>Edit</accessLevel>
        <label>Advice Review Sharing Rule_CM</label>
        <sharedTo>
            <group>Central_Monitoring_Public_Group</group>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>Advice Review</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Adviser_Payments_Sharing_Rule</fullName>
        <accessLevel>Edit</accessLevel>
        <label>Adviser Payments Sharing Rule</label>
        <sharedTo>
            <group>Adviser_Payment_Public_Group</group>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>Adviser Payments</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>BSE_Sharing_Rule</fullName>
        <accessLevel>Edit</accessLevel>
        <label>BSE Sharing Rule</label>
        <sharedTo>
            <group>Business_Solutions_Enquiries_Group</group>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>Business Solutions Enquiry</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Ed_Ops_Sharing</fullName>
        <accessLevel>Edit</accessLevel>
        <label>Ed Ops Sharing</label>
        <sharedTo>
            <roleAndSubordinatesInternal>MLC_AMP_Educational_Operations</roleAndSubordinatesInternal>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>Learning &amp; Education</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Enablement_Sharing_Rule</fullName>
        <accessLevel>Edit</accessLevel>
        <label>Enablement Sharing Rule</label>
        <sharedTo>
            <group>Enablement_Public_Group</group>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>Enablement</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>FFS_Sharing_Rule</fullName>
        <accessLevel>Edit</accessLevel>
        <label>FFS Sharing Rule</label>
        <sharedTo>
            <group>Fees_for_Service_Controls_Public_Group</group>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>Fees for Service Controls</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>M_A_Sharing_Rule</fullName>
        <accessLevel>Edit</accessLevel>
        <label>M&amp;A Sharing Rule</label>
        <sharedTo>
            <group>M_A_Public_Group</group>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>M&amp;A</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>M_A_Sharing_Rule_AMP_Other</fullName>
        <accessLevel>Read</accessLevel>
        <label>M&amp;A Sharing Rule - AMP Other</label>
        <sharedTo>
            <group>M_A_Public_Group_AMP_Other</group>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>M&amp;A</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Register_Transfers_Other_Sharing_Rule</fullName>
        <accessLevel>Edit</accessLevel>
        <label>Register Transfers Other Sharing Rule</label>
        <sharedTo>
            <role>AMP_Other</role>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>Register Transfers</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Register_Transfers_Sharing_Rule</fullName>
        <accessLevel>Edit</accessLevel>
        <label>Register Transfers Sharing Rule</label>
        <sharedTo>
            <role>AMP_Client_Registers</role>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>Register Transfers</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Remediation_Sharing_Rule</fullName>
        <accessLevel>Edit</accessLevel>
        <label>Remediation Sharing Rule</label>
        <sharedTo>
            <group>Remediation_Public_Group</group>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>Remediation</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingOwnerRules>
        <fullName>ASD_Sharing_Rule</fullName>
        <accessLevel>Edit</accessLevel>
        <label>ASD Sharing Rule</label>
        <sharedTo>
            <roleAndSubordinatesInternal>AMP_Advice_Service_Desk</roleAndSubordinatesInternal>
        </sharedTo>
        <sharedFrom>
            <roleAndSubordinatesInternal>AMP_Advice_Service_Desk</roleAndSubordinatesInternal>
        </sharedFrom>
    </sharingOwnerRules>
    <sharingOwnerRules>
        <fullName>Ed_Ops_LSR_Sharing_Rule</fullName>
        <accessLevel>Edit</accessLevel>
        <label>Ed Ops LSR Sharing Rule</label>
        <sharedTo>
            <roleAndSubordinatesInternal>MLC_AMP_Educational_Operations</roleAndSubordinatesInternal>
        </sharedTo>
        <sharedFrom>
            <roleAndSubordinatesInternal>MLC_AMP_Educational_Operations</roleAndSubordinatesInternal>
        </sharedFrom>
    </sharingOwnerRules>
    <sharingOwnerRules>
        <fullName>Regional_Coaching_Sharing_Rule</fullName>
        <accessLevel>Edit</accessLevel>
        <label>Regional Coaching Sharing Rule</label>
        <sharedTo>
            <group>Regional_Coaching_Public_Group</group>
        </sharedTo>
        <sharedFrom>
            <group>Regional_Coaching_Public_Group</group>
        </sharedFrom>
    </sharingOwnerRules>
    <sharingOwnerRules>
        <fullName>Research_Sharing_Rule</fullName>
        <accessLevel>Edit</accessLevel>
        <label>Research Sharing Rule</label>
        <sharedTo>
            <group>Research_Public_Group</group>
        </sharedTo>
        <sharedFrom>
            <group>Research_Public_Group</group>
        </sharedFrom>
    </sharingOwnerRules>
    <sharingOwnerRules>
        <fullName>Tapin_LSR_Sharing_Rule</fullName>
        <accessLevel>Edit</accessLevel>
        <label>Tapin LSR Sharing Rule</label>
        <sharedTo>
            <roleAndSubordinatesInternal>AMP_Service_Desk_Tapin</roleAndSubordinatesInternal>
        </sharedTo>
        <sharedFrom>
            <roleAndSubordinatesInternal>AMP_Service_Desk_Tapin</roleAndSubordinatesInternal>
        </sharedFrom>
    </sharingOwnerRules>
</SharingRules>
