<template>
  <div class="slds-box slds-theme_default backgroundColor-grey">



    <template if:true={renderPageNumber}>
      <p class="subheader-style" style="padding-top: 33px;">Pending consents</p>
      <div class="formContainer">

        <template if:false={pendingConsentList}>
          <p class="pbody-style" style="margin-bottom: 24px;">You don't have any pending request to review.</p>
        </template>

        <template if:true={pendingConsentList}>
          <template iterator:it={pendingConsentList}>
            <div class="card-container-style" key={it.value.dfr.Id} id={it.value.dfr.Id}>
             
              <lightning-card class="card-style">
                
                <lightning-layout class="slds-grid_vertical-align-center">
                  <lightning-layout-item size="10" flexibility="auto" padding="horizontal-none"
                    class="slds-text-align_left slds-align-middle slds-grid slds-grid_vertical">
                    <p class="heading-style">{it.value.formName}</p>
                    <p class="request-number-label">{it.value.caseRec.Request_ID__c}</p>
                  </lightning-layout-item>
                  <lightning-layout-item flexibility="auto" padding="around-none"
                    class="slds-text-align_right slds-align-middle slds-shrink-none">
                    <button class="slds-button slds-float_right" data-id={it.value.dfr.Id} data-value={it.value} style="height:44px;" onclick={handleContinue}>
                      <p class="review-label">Review</p>
                    </button>
                  </lightning-layout-item>
                </lightning-layout>
              
              </lightning-card>

            </div>
          </template>
        </template>

        <div class="slds-col slds-p-top_small slds-p-right_small footer-button-style">
          <button type="button" class="close-button-style slds-button slds-button_brand" onclick={handleClose}>Close</button>
        </div>
      </div>
    </template>

    <template if:false={renderPageNumber}>
      <c-bank-forms-consent-details onshowpages={handlePages} dfr-id={dfrId} selected-consent={selectedConsent} redirect-url={redirectURL}>
      </c-bank-forms-consent-details>
    </template>





  </div>




</template>