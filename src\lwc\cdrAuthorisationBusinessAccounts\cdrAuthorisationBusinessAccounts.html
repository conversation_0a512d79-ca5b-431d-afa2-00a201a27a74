<!--  
-LWC for authorisation page
-Created by <PERSON> (Accenture) SFP-29055
--> 

<template>
    <div class="content__body-dashboard">           
        <template if:true={isRecordFound}>
            <template if:true={renderPageNumber}>
                <!-- loading -->
                <template if:false={loaded}>
                    <lightning-spinner class="loading slds-is-relative" alternative-text="Loading" size="large" style="position: fixed;"></lightning-spinner>
                </template>
                
                <template if:false={originatedFromArrangement}>
                    <!-- Header -->
                    <div slot="title" class="slds-grid slds-grid_vertical-align-center c-title-header">
                        <div class="slds-col"> 
                            <img src={ampLogo} class="c-title-image" aria-label="A M P Logo" alt="AMP Logo">
                        </div>
                        <div class="slds-col"> 
                            <img src={loginLogo} class="c-login-image slds-float_right slds-var-p-right_large" aria-label="Login Logo" alt="Login Logo">
                        </div>
                        
                    </div>
                </template>

                <!-- <div class="back-button">
                    <button type="button" class="slds-button icon-back-style-data-sharing" title="Back" onclick={handleBack}>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52" aria-label="back arrow" id="chevronleft2" class="icon-back-button-style">
                            <path d="M34.2 47.7L13.4 27.2c-.6-.6-.6-1.6 0-2.2L34.2 4.5c.6-.6 1.6-.6 2.2 0l2.2 2.2c.6.6.6 1.6 0 2.2L22.1 25c-.6.6-.6 1.6 0 2.2l16.3 16.1c.6.6.6 1.6 0 2.2l-2.2 2.2c-.5.5-1.4.5-2 0z"></path>
                        </svg>
                        Back
                    </button>
                </div> -->
                
                <template if:true={loaded}>
                    <!-- SFP-33122 Page body when scope id is not allowed by data recipient-->
                    
                    <template if:true={invalidSharingRequest}>
                        <div class="content__body-main">
                            <!--SFP-40020 by Eugene Ray Perfecio-->
                            <div class="slds-text-align_center"> 
                                <img src={authErrorIcon} class="c-auth-error-image slds-p-top_xx-large" alt="Error symbol">
                            </div>
                            <h2 class="slds-text-align_center slds-var-p-bottom_medium slds-var-p-top_xx-large">Sorry, something went wrong...</h2>
                            <!--SFP-40020 by Eugene Ray Perfecio-->
                            <!--Show different page body for respective errors-->
                            <template if:false={unauthorizedError}>
                                <div></div>
                                <p class="authorisation-text slds-var-p-left_small slds-var-p-bottom_medium slds-var-p-top_x-small">
                                     <b>{recipient}</b> has requested data they are not permitted to access. Please contact <b>{recipient}</b> to amend your request.
                                </p>
                            </template>
                            <!--SFP-40020-->
                            <template if:true={unauthorizedError}>
                                <div></div>
                                <p class="authorisation-text slds-var-p-left_small slds-var-p-bottom_medium slds-var-p-top_x-small">
                                    We are unable to process your request. For more information about Consumer Data Right, please refer to <a href={cdrFAQLink} target="_blank" class="ds-text-font-style_unset float_none"><u class="link-blue">Consumer Data Right FAQ</u></a> or contact AMP Bank on 13 30 30 between 8am-8pm Monday to Friday, or 9am-5pm Saturday and Sunday (AEST).
                               </p>
                            </template>
                        </div>
                    </template>
    
                    <template if:false={invalidSharingRequest}>
                        <template if:true={isBusiness}>
                            <div class="company-details">
                                <div class="content__body-main-dashboard-shadow">
                                    <div class="slds-border_bottom slds-media slds-var-p-around_medium dashboard-tile">
                                        <div class="slds-media__body slds-grid slds-grid_vertical-align-center slds-var-p-top_xx-small">
                                            <div class="slds-col slds-size_11-of-12">
                                                <div class="slds-grid slds-wrap">
                                                    <div class="slds-size_1-of-1 slds-medium-size_1-of-1 slds-large-size_1-of-1">
                                                        <h6 class="inline-text">{companyName}</h6>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                        <!--If isAuthSelectionRequired = true, shows message that user can share accounts SFP-29251-->
                        <template if:true={initialDataResponse.isAuthSelectionRequired}>
                                <h2 class="slds-text-align_left header-text hurme-text m-margin">Select account(s) to share</h2> <!-- SFP-62321 -->
                        </template>
                    
                        <!--Shows message if isAuthSelectionRequired = false SFP-29251-->
                        <template if:false={initialDataResponse.isAuthSelectionRequired}>
                                <h2 class="slds-text-align_left header-text hurme-text">Continue</h2>
                        </template>

                        <template if:true={isTypeCompany}>
                            <template if:false={isLoggedInUserNomRep}>
                                <div data-id="toastModal" class="slds-show slds-p-top_large ">
                                    <div  class="slds-notify_container slds-is-relative">
                                        <div class="content__body-main-dashboard slds-notify_toast slds-theme_warning slds-media slds-var-p-left_large slds-var-p-right_large toast-margin" role="status">
                                            <span class="slds-assistive-text">warning</span>
                                            <span class="slds-icon_container slds-icon-utility-warning slds-m-right_small slds-no-flex slds-align-top" title="Warning">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="svg-check" aria-label="yellow exclamation" alt="yellow exclamation">;
                                                    <use xlink:href={svgURLWarning}></use>
                                                </svg>
                                            </span>
                                            <div class="slds-notify__content">
                                                    <h6 class="toast-message slds-text-heading_small font-style_light-bold ds-text-font-style">This doesn't apply to you as you're not authorised to share data from this profile. To share data as a nominated representative, you'll need to download the <a href={labels.CDR_NOM_REP_FORM_URL}>{labels.CDR_NOM_REP_FORM_LABEL}</a>, complete and return it to us. It can take up to 2 business days to process your request. <br><br>
                                                        You can view details of data sharing arrangements made by nominated representatives. To do this, please use the My AMP app and go to settings > manage data sharing. Or log into My AMP at <a href='https://www.amp.com.au/'>amp.com.au</a> and under your name select manage data sharing.</h6>
                                            </div>
                                        </div>
                                    </div>
                                </div> 
                            </template>
                        </template>

                        <template if:false={isTypeCompany}>
                            <template if:false={isLoggedInUserIndividualTrustee}>
                                <template if:false={isLoggedInUserSecondaryUser}>
                                    <div data-id="toastModal" class="slds-show slds-p-top_large ">
                                        <div  class="slds-notify_container slds-is-relative">
                                            <div class="content__body-main-dashboard slds-notify_toast slds-theme_warning slds-media slds-var-p-left_large slds-var-p-right_large toast-margin" role="status">
                                                <span class="slds-assistive-text">warning</span>
                                                <span class="slds-icon_container slds-icon-utility-warning slds-m-right_small slds-no-flex slds-align-top" title="Warning">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="svg-check" aria-label="yellow exclamation" alt="yellow exclamation">;
                                                        <use xlink:href={svgURLWarning}></use>
                                                    </svg>
                                                </span>
                                                <div class="slds-notify__content">
                                                        <h6 class="toast-message slds-text-heading_small font-style_light-bold ds-text-font-style">This doesn't apply to you as you're not authorised to share data from this profile. To share data as a nominated representative, you'll need to download the <a href={labels.CDR_NOM_REP_FORM_URL}>{labels.CDR_NOM_REP_FORM_LABEL}</a>, complete and return it to us. It can take up to 2 business days to process your request. <br><br>
                                                            Directors and secretaries can view details of data sharing arrangements made by nominated representatives. To do this, please use the My AMP app and go to settings > manage data sharing. Or log into My AMP at <a href='https://www.amp.com.au/'>amp.com.au</a> and under your name select manage data sharing.</h6>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </template>
                            <!-- <template if:false={isLoggedInUserSecondaryUser}>
                                <div data-id="toastModal" class="slds-show slds-p-top_large ">
                                    <div  class="slds-notify_container slds-is-relative">
                                        <div class="content__body-main-dashboard slds-notify_toast slds-theme_warning slds-media slds-var-p-left_large slds-var-p-right_large toast-margin" role="status">
                                            <span class="slds-assistive-text">warning</span>
                                            <span class="slds-icon_container slds-icon-utility-warning slds-m-right_small slds-no-flex slds-align-top" title="Warning">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="svg-check" aria-label="yellow exclamation" alt="yellow exclamation">;
                                                    <use xlink:href={svgURLWarning}></use>
                                                </svg>
                                            </span>
                                            <div class="slds-notify__content">
                                                    <h6 class="toast-message slds-text-heading_small font-style_light-bold ds-text-font-style">This doesn't apply to you as you're not authorised to share data from this profile. To share data as a nominated representative, you'll need to download the <a href={labels.CDR_NOM_REP_FORM_URL}>{labels.CDR_NOM_REP_FORM_LABEL}</a>, complete and return it to us. It can take up to 2 business days to process your request. <br><br>
                                                        Directors and secretaries can view details of data sharing arrangements made by nominated representatives. To do this, please use the My AMP app and go to settings > manage data sharing. Or log into My AMP at <a href='https://www.amp.com.au/'>amp.com.au</a> and under your name select manage data sharing.</h6>
                                            </div>
                                        </div>
                                    </div>
                                </div> 
                            </template> -->
                        </template>
    
                        <div class="data-recipient-box">
                            <div></div>
                            <!-- Recipient Information -->
                            <div class="authorisation-box slds-var-p-around_x-small slds-var-m-top_medium slds-m-horizontal_none">
                                <lightning-layout multiple-rows>
                                    <lightning-layout-item size="12" small-device-size="2" flexibility="auto" padding="around-none" class="slds-grid_vertical-align-center slds-p-around_small">
                                        <img src={cdrLogo} class="cdr-logo-style slds-align-center" aria-label="C D R Logo" alt="CDR Logo">
                                    </lightning-layout-item>
                                    <lightning-layout-item size="12" small-device-size="10" flexibility="auto" padding="around-none" class="slds-text-align_left recipient-info-layout slds-var-p-left_small slds-grid slds-grid_vertical-align-center">
                                        <div>
                                            <h6>{recipient}</h6>
                                            <div class="authorisation-box-text">
                                                <span class="c-grouped-text">Accredited Data Recipient ID: <span class="recipient-number-text">{recipientNumber}</span></span>
                                                <br/><span class="c-grouped-text">Status: <span class="recipient-number-text">{recipientStatus}</span></span>
                                                <br/><span class="c-grouped-text">Software Product: <span class="recipient-number-text">{softwareProductName}</span></span>
                                                <br/><span class="c-grouped-text">Software Product Status: <span class="recipient-number-text">{softwareProductStatus}</span></span>
                                                <br/><span class="c-grouped-text">Request made on: <span class="recipient-number-text">{currentDate}</span></span>
                                            </div>
                                        </div>
                                    </lightning-layout-item>
                                </lightning-layout>
                            </div>
                        </div>

                        <!--Page body when isAuthSelectionRequired = true, allowing users to select and see their financial accounts if they have  SFP-29251-->
                        
                            <template if:true={initialDataResponse.isAuthSelectionRequired}>
                                <div class="content__body-main">
                                    <template if:false={originatedFromArrangement}> 
                                            <div style="max-width:35em;" class="authorisation-text slds-var-p-bottom_medium">
                                                <p class="inline-text"><b>{recipient}</b> is an accredited data recipient and is requesting data from your accounts. You can check their accreditation on the <a href={labels.CDR_ACCREDITATION_LINK} target="_blank" class="ds-text-font-style_unset float_none"><u class="link-blue">current provider register</u></a>.</p>
                                            </div>
                                    </template>
                                    <!--We only show this message if there are available financial accounts-->
                                    <template if:true={availableFinancialAccounts}>
                                        <div class="account-select slds-var-p-bottom_medium">
                                            <p class="authorisation-text">
                                                Please select the accounts you would like to share data from. 
                                            </p>
                                        </div>
                                        <div class="customer-info slds-p-bottom_none">
                                            <p class="authorisation-text">
                                                AMP Bank may not share data with {recipient} for certain reasons, including if your account is not available in My AMP, or if your account was closed more than 24 months ago. Refer to <a href={cdrFAQLink} target="_blank" class="ds-text-font-style_unset float_none"><u class="link-blue">Consumer Data Right FAQ</u></a> for more details.
                                            </p>
                                        </div>
                                    </template>
                                </div>  

                                <template if:true={isTypeCompany}>
                                    <template if:true={isLoggedInUserNomRep}>
                                        <!-- Account selection header -->
                                        <div>
                                            <lightning-layout class="slds-var-p-bottom_large table-header slds-grid_vertical-align-center">
                                                <lightning-layout-item size="7" flexibility="auto" padding="around-none" class="slds-text-align_left slds-align-middle section-header-pad">
                                                    <h3 class="hurme-text">Accounts available to share</h3>
                                                </lightning-layout-item>
                                                <template if:true={availableFinancialAccounts}>
                                                    <lightning-layout-item size="5" flexibility="auto"  padding="around-none" class="slds-text-align_right slds-var-p-right_large slds-float_right">
                                                        <h6 class="hurme-text">
                                                            <lightning-button-stateful  class="slds-button"
                                                                                        label-when-off="Select All"
                                                                                        label-when-on="Deselect All"
                                                                                        selected={deselectedAllAcc}
                                                                                        onclick={handleSelectAll}
                                                                                        variant="text">
                                                            </lightning-button-stateful>
                                                        </h6>
                                                    </lightning-layout-item>
                                                </template>
                                            </lightning-layout>
                                        </div>
        
                                        <!-- Error on financial account retrieval -->
                                        <template if:true={error}>
                                            <div class="slds-scoped-notification slds-media slds-media_center slds-scoped-notification_light" role="status">
                                                <div class="slds-media__figure">
                                                    <lightning-icon icon-name="utility:warning" variant="warning"></lightning-icon>
                                                </div>
                                                <div class="slds-media__body">
                                                    <p class="authorisation-text">Error retrieving Financial Accounts: {error}</p>
                                                </div>
                                            </div>
                                        </template>
        
                                        <!-- Available account selection -->
                                        <template if:true={availableFinancialAccounts}>
                                                <div class="accounts-list-box slds-border_left slds-border_bottom slds-border_right">
                                                    <template iterator:it={availableFinancialAccounts}>
                                                        <div key={it.value.finAcct.Id}>
                                                            <lightning-layout class="slds-grid_vertical-align-center fin-acct-details financial-accounts-padding">
                                                                <lightning-layout-item size="9" flexibility="auto" padding="horizontal-none" class="slds-text-align_left slds-align-middle slds-grid slds-grid_vertical">
                                                                    <div class="slds-col">
                                                                        <h6 class="acc-name-label">{it.value.finAcct.Name}
                                                                        </h6>
                                                                    </div>
                                                                    <div class="slds-col">
                                                                        <p class="text-gray fin-accts-number-text"> 
                                                                            <template if:true={it.value.finAcct.Bank_BSB__c}>
                                                                                BSB <span class="acc-bsb-label">{it.value.bsbLabel}</span> &nbsp;
                                                                            </template> 
                                                                            <template if:true={originatedFromArrangement}>
                                                                                ACC &nbsp;
                                                                            </template> 
                                                                            <span class="acc-bsb-label">{it.value.accLabel}</span>
                                                                        </p>
                                                                    </div>
                                                                    <div class="slds-col fa_status">
                                                                        <template if:true={it.value.isJointAccount}>
                                                                            <div class="slds-grid slds-grid_vertical-align-center">
                                                                                <div class="slds-col slds-grow-none " style="max-width: 50px;">
                                                                                    <svg xmlns="http://www.w3.org/2000/svg" class="svg-joint status_icon" aria-label="blue joint account">;
                                                                                        <use xlink:href={svgURLJointAccount}></use>
                                                                                    </svg>
                                                                                </div>
                                                                                <div class="slds-col slds-size_8-of-8">
                                                                                    <div class="slds-col slds-size_6-of-6">
                                                                                        <h6>Joint account</h6>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </template>
                                                                    </div>
                                                                    <div class="slds-col fa_status">
                                                                        <template if:true={it.value.closedAccount}>
                                                                            <div class="slds-grid slds-grid_vertical-align-center">
                                                                                 <!-- SFP-56156 - Remove cross symbol 
                                                                                <div class="slds-col slds-size_1-of-4 slds-align_absolute-center">
                                                                                    <svg xmlns="http://www.w3.org/2000/svg" class="slds-m-left_small svg-joint unavailable_icon" aria-label="gray cross">;
                                                                                        <use xlink:href={svgURL}></use>
                                                                                    </svg>
                                                                                </div>
                                                                                -->
                                                                                <div class="slds-col slds-size_8-of-8">
                                                                                    <div class="slds-col slds-size_6-of-6">
                                                                                        <h6 class="/*unavailable-reason-style*/ joint-account-style unavailable_text" >{it.value.unavailableReason}</h6>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </template>
                                                                    </div>
                                                                </lightning-layout-item>
                                                                <lightning-layout-item size="3" flexibility="auto" padding="around-none" class="slds-text-align_right slds-align-middle">                                                              
                                                                    <div class = "slds-float_right select-grid-container">
                                                                        <div class="select-grid-item" >
                                                                            <div class="slds-form-element">
                                                                                <label class="slds-checkbox_toggle slds-grid">
                                                                                  <span class="slds-form-element__label slds-m-bottom_none"></span>
                                                                                  <input
                                                                                        aria-checked={it.value.selected}
                                                                                        aria-label={it.value.finAcct.Name}
                                                                                        class="toggleSelection"
                                                                                        type="checkbox" 
                                                                                        onchange={handleSelect}
                                                                                        onkeypress={changeToggleValue}
                                                                                        name="selectFA" 
                                                                                        value={it.value.finAcct.Id}/>
                                                                                  <span aria-live="assertive" id={it.value.finAcct.Id} class="slds-checkbox_faux_container slds-align_absolute-center">
                                                                                    <span class="slds-checkbox_faux"></span>
                                                                                        <span class="slds-checkbox_on" aria-label="enabled"></span>
                                                                                        <span class="slds-checkbox_off" aria-label="disabled"></span>
                                                                                  </span>
                                                                                </label>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </lightning-layout-item>
                                                            </lightning-layout>
                                                            <div if:false={it.last} key={it.value.finAcct.Id} class="slds-border_bottom border-style "></div>
                                                        </div>
                                                    </template>
                                                </div>
                                            </template>
        
                                        <!--SFP-29331: Show this message when the user has no available Financial Accounts for selection-->
                                        <template if:false={availableFinancialAccounts}>
                                            <div class="no-fa-text accounts-list-box slds-text-align_center">
                                                <p class="authorisation-text slds-var-p-left_small slds-var-p-top_large slds-var-p-bottom_large">You do not have any available accounts to share.</p>
                                            </div>
                                        </template>
        
                                        <!-- Show this only if there are available Financial Accounts for selection -->
                                        <template if:true={availableFinancialAccounts}>
                                            <div class="slds-text-align_left num-selected-accounts">
                                                Number of selected account(s): {numberOfSelectedFas}
                                            </div>
                                        </template>
                                        
                                         <!-- Unavailable account selection -->
                                        <template if:true={IfUnavailableHide}>
                                            <div class="slds-var-m-top_large accounts-unavailable-list-box ">
                                                <lightning-layout>
                                                    <button class=" button__collapse slds-button_full-width slds-var-p-left_medium slds-var-p-right_medium slds-var-p-bottom_medium slds-var-p-top_medium slds-border_bottom border-style " onclick={handleUnavailableCollapse}>
                                                        <lightning-layout-item size="9" flexibility="auto" padding="around-none" class="slds-text-align_left">
                                                            <h6 style="color: rgb(0, 30, 65);">Accounts unavailable to share</h6>
                                                        </lightning-layout-item>
                                                            <div class="icons-chevron">
                                                                <!-- If collapsed -->
                                                                <template if:false={unavailableCollapsed}>
                                                                    <lightning-layout-item size="3" flexibility="auto" padding="around-none" class="slds-text-align_right slds-var-p-right_medium slds-float_right">
                                                                        <lightning-icon icon-name="utility:chevronup" class="slds-current-color" size="small"></lightning-icon>
                                                                    </lightning-layout-item>
                                                                </template>
                                                                
                                                                <!-- If expanded -->
                                                                <template if:true={unavailableCollapsed}>
                                                                    <lightning-layout-item size="3" flexibility="auto" padding="around-none" class="slds-text-align_right slds-var-p-right_medium slds-float_right">
                                                                        <lightning-icon icon-name="utility:chevrondown" class="slds-current-color" size="small"></lightning-icon>
                                                                    </lightning-layout-item>
                                                                </template>
                                                            </div>
                                                    </button>
                                                </lightning-layout>
                                            </div>
                                        </template>
        
            
                                        <!-- If expanded -->
                                        <template if:true={unavailableFinancialAccounts}>
                                            <template if:false={unavailableCollapsed}>
                                                <div class=" accounts-list-box">
                                                    <template iterator:it={unavailableFinancialAccounts}>
                                                        <template if:false={it.value.closedAccount}>  <!--SFP-54699 (Start)-->
                                                            <div key={it.value.finAcct.Id}>
                                                                <lightning-layout class="slds-var-p-right_medium fin-acct-details slds-grid_vertical-align-center financial-accounts-padding">
                                                                    <lightning-layout-item flexibility="auto" padding="horizontal-none" class="slds-text-align_left slds-align-middle unavailable-fa">
                                                                        <div>
                                                                            <h6>{it.value.finAcct.Name}</h6>
                                                                        </div>
                                                                        <p class="text-gray fin-accts-number-text"> 
                                                                            <template if:true={it.value.finAcct.Bank_BSB__c}>
                                                                                BSB <span class="acc-bsb-label">{it.value.bsbLabel}</span> &nbsp;
                                                                            </template> 
                                                                            <template if:true={originatedFromArrangement}>
                                                                                ACC &nbsp;
                                                                            </template> 
                                                                            <span class="acc-bsb-label">{it.value.accLabel}</span>
                                                                        </p>
                                                                    </lightning-layout-item>
                                                                    <lightning-layout-item flexibility="auto" padding="around-none" class="unavailable-fa-reason">
                                                                        <div>
                                                                            <!-- SFP-54699 (Start) temporary removal of closed / closed joint accounts past 24 months
                                                                            <template if:true={it.value.closedAccount}>
                                                                                <div class="slds-grid authorisation-alert-text slds-float_right unavailable_container">
                                                                                    <div class="slds-col slds-size_3-of-4 slds-align_absolute-center">
                                                                                        <h6 class="unavailable-reason-style unavailable_text">{it.value.unavailableReason}</h6>
                                                                                    </div>
                                                                                    <div class="slds-col slds-size_1-of-4 slds-align_absolute-center">
                                                                                        <svg xmlns="http://www.w3.org/2000/svg" class="slds-m-left_small svg-error unavailable_icon" aria-label="red cross">;
                                                                                            <use xlink:href={svgURL}></use>
                                                                                        </svg>
                                                                                    </div>
                                                                                </div>
                                                                            </template>  SFP-54699 (End)-->
                                                                            <template if:false={it.value.closedAccount}>
                                                                                <div class="slds-grid authorisation-alert-text slds-float_right unavailable_container">
                                                                                    <div class="slds-col slds-size_3-of-4 slds-align_absolute-center">
                                                                                        <h6 class="joint-account-style unavailable_text">{it.value.unavailableReason}</h6>
                                                                                    </div>
                                                                                    <div class="slds-col slds-size_1-of-4 slds-align_absolute-center">
                                                                                        <svg xmlns="http://www.w3.org/2000/svg" class="slds-m-left_small svg-joint unavailable_icon" aria-label="gray cross">;
                                                                                            <use xlink:href={svgURL}></use>
                                                                                        </svg>
                                                                                    </div>
                                                                                </div>
                                                                            </template>
                                                                        </div>
                                                                    </lightning-layout-item>
                                                                </lightning-layout>
                                                                <div if:false={it.last} key={it.value.finAcct.Id} class="slds-border_bottom border-style"></div>
                                                            </div>
                                                        </template> <!--SFP-54699 (End)-->
                                                    </template>
                                                </div>
                                            </template>
                                        </template>
                                        </template>
                                </template>

                                <template if:false={isTypeCompany}>
                                    <template if:true={isLoggedInUserIndividualTrustee}>
                                        <!-- Account selection header -->
                                        <div>
                                            <lightning-layout class="slds-var-p-bottom_large table-header slds-grid_vertical-align-center">
                                                <lightning-layout-item size="7" flexibility="auto" padding="around-none" class="slds-text-align_left slds-align-middle section-header-pad">
                                                    <h3 class="hurme-text">Accounts available to share</h3>
                                                </lightning-layout-item>
                                                <template if:true={availableFinancialAccounts}>
                                                    <lightning-layout-item size="5" flexibility="auto"  padding="around-none" class="slds-text-align_right slds-var-p-right_large slds-float_right">
                                                        <h6 class="hurme-text">
                                                            <lightning-button-stateful  class="slds-button"
                                                                                        label-when-off="Select All"
                                                                                        label-when-on="Deselect All"
                                                                                        selected={deselectedAllAcc}
                                                                                        onclick={handleSelectAll}
                                                                                        variant="text">
                                                            </lightning-button-stateful>
                                                        </h6>
                                                    </lightning-layout-item>
                                                </template>
                                            </lightning-layout>
                                        </div>
        
                                        <!-- Error on financial account retrieval -->
                                        <template if:true={error}>
                                            <div class="slds-scoped-notification slds-media slds-media_center slds-scoped-notification_light" role="status">
                                                <div class="slds-media__figure">
                                                    <lightning-icon icon-name="utility:warning" variant="warning"></lightning-icon>
                                                </div>
                                                <div class="slds-media__body">
                                                    <p class="authorisation-text">Error retrieving Financial Accounts: {error}</p>
                                                </div>
                                            </div>
                                        </template>
        
                                        <!-- Available account selection -->
                                        <template if:true={availableFinancialAccounts}>
                                                <div class="accounts-list-box slds-border_left slds-border_bottom slds-border_right">
                                                    <template iterator:it={availableFinancialAccounts}>
                                                        <div key={it.value.finAcct.Id}>
                                                            <lightning-layout class="slds-grid_vertical-align-center fin-acct-details financial-accounts-padding">
                                                                <lightning-layout-item size="9" flexibility="auto" padding="horizontal-none" class="slds-text-align_left slds-align-middle slds-grid slds-grid_vertical">
                                                                    <div class="slds-col">
                                                                        <h6 class="acc-name-label">{it.value.finAcct.Name}
                                                                        </h6>
                                                                    </div>
                                                                    <div class="slds-col">
                                                                        <p class="text-gray fin-accts-number-text"> 
                                                                            <template if:true={it.value.finAcct.Bank_BSB__c}>
                                                                                BSB <span class="acc-bsb-label">{it.value.bsbLabel}</span> &nbsp;
                                                                            </template> 
                                                                            <template if:true={originatedFromArrangement}>
                                                                                ACC &nbsp;
                                                                            </template> 
                                                                            <span class="acc-bsb-label">{it.value.accLabel}</span>
                                                                        </p>
                                                                    </div>
                                                                    <div class="slds-col fa_status">
                                                                        <template if:true={it.value.isJointAccount}>
                                                                            <div class="slds-grid slds-grid_vertical-align-center">
                                                                                <div class="slds-col slds-grow-none " style="max-width: 50px;">
                                                                                    <svg xmlns="http://www.w3.org/2000/svg" class="svg-joint status_icon" aria-label="blue joint account">;
                                                                                        <use xlink:href={svgURLJointAccount}></use>
                                                                                    </svg>
                                                                                </div>
                                                                                <div class="slds-col slds-size_8-of-8">
                                                                                    <div class="slds-col slds-size_6-of-6">
                                                                                        <h6>Joint account</h6>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </template>
                                                                    </div>
                                                                    <div class="slds-col fa_status">
                                                                        <template if:true={it.value.closedAccount}>
                                                                            <div class="slds-grid slds-grid_vertical-align-center">
                                                                                 <!-- SFP-56156 - Remove cross symbol 
                                                                                <div class="slds-col slds-size_1-of-4 slds-align_absolute-center">
                                                                                    <svg xmlns="http://www.w3.org/2000/svg" class="slds-m-left_small svg-joint unavailable_icon" aria-label="gray cross">;
                                                                                        <use xlink:href={svgURL}></use>
                                                                                    </svg>
                                                                                </div>
                                                                                -->
                                                                                <div class="slds-col slds-size_8-of-8">
                                                                                    <div class="slds-col slds-size_6-of-6">
                                                                                        <h6 class="/*unavailable-reason-style*/ joint-account-style unavailable_text" >{it.value.unavailableReason}</h6>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </template>
                                                                    </div>
                                                                </lightning-layout-item>
                                                                <lightning-layout-item size="3" flexibility="auto" padding="around-none" class="slds-text-align_right slds-align-middle">                                                              
                                                                    <div class = "slds-float_right select-grid-container">
                                                                        <div class="select-grid-item" >
                                                                            <div class="slds-form-element">
                                                                                <label class="slds-checkbox_toggle slds-grid">
                                                                                  <span class="slds-form-element__label slds-m-bottom_none"></span>
                                                                                  <input
                                                                                        aria-checked={it.value.selected}
                                                                                        aria-label={it.value.finAcct.Name}
                                                                                        class="toggleSelection"
                                                                                        type="checkbox" 
                                                                                        onchange={handleSelect}
                                                                                        onkeypress={changeToggleValue}
                                                                                        name="selectFA" 
                                                                                        value={it.value.finAcct.Id}/>
                                                                                  <span aria-live="assertive" id={it.value.finAcct.Id} class="slds-checkbox_faux_container slds-align_absolute-center">
                                                                                    <span class="slds-checkbox_faux"></span>
                                                                                        <span class="slds-checkbox_on" aria-label="enabled"></span>
                                                                                        <span class="slds-checkbox_off" aria-label="disabled"></span>
                                                                                  </span>
                                                                                </label>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </lightning-layout-item>
                                                            </lightning-layout>
                                                            <div if:false={it.last} key={it.value.finAcct.Id} class="slds-border_bottom border-style "></div>
                                                        </div>
                                                    </template>
                                                </div>
                                            </template>
        
                                        <!--SFP-29331: Show this message when the user has no available Financial Accounts for selection-->
                                        <template if:false={availableFinancialAccounts}>
                                            <div class="no-fa-text accounts-list-box slds-text-align_center">
                                                <p class="authorisation-text slds-var-p-left_small slds-var-p-top_large slds-var-p-bottom_large">You do not have any available accounts to share.</p>
                                            </div>
                                        </template>
        
                                        <!-- Show this only if there are available Financial Accounts for selection -->
                                        <template if:true={availableFinancialAccounts}>
                                            <div class="slds-text-align_left num-selected-accounts">
                                                Number of selected account(s): {numberOfSelectedFas}
                                            </div>
                                        </template>
                                        
                                         <!-- Unavailable account selection -->
                                        <template if:true={IfUnavailableHide}>
                                            <div class="slds-var-m-top_large accounts-unavailable-list-box ">
                                                <lightning-layout>
                                                    <button class=" button__collapse slds-button_full-width slds-var-p-left_medium slds-var-p-right_medium slds-var-p-bottom_medium slds-var-p-top_medium slds-border_bottom border-style " onclick={handleUnavailableCollapse}>
                                                        <lightning-layout-item size="9" flexibility="auto" padding="around-none" class="slds-text-align_left">
                                                            <h6 style="color: rgb(0, 30, 65);">Accounts unavailable to share</h6>
                                                        </lightning-layout-item>
                                                            <div class="icons-chevron">
                                                                <!-- If collapsed -->
                                                                <template if:false={unavailableCollapsed}>
                                                                    <lightning-layout-item size="3" flexibility="auto" padding="around-none" class="slds-text-align_right slds-var-p-right_medium slds-float_right">
                                                                        <lightning-icon icon-name="utility:chevronup" class="slds-current-color" size="small"></lightning-icon>
                                                                    </lightning-layout-item>
                                                                </template>
                                                                
                                                                <!-- If expanded -->
                                                                <template if:true={unavailableCollapsed}>
                                                                    <lightning-layout-item size="3" flexibility="auto" padding="around-none" class="slds-text-align_right slds-var-p-right_medium slds-float_right">
                                                                        <lightning-icon icon-name="utility:chevrondown" class="slds-current-color" size="small"></lightning-icon>
                                                                    </lightning-layout-item>
                                                                </template>
                                                            </div>
                                                    </button>
                                                </lightning-layout>
                                            </div>
                                        </template>
        
            
                                        <!-- If expanded -->
                                        <template if:true={unavailableFinancialAccounts}>
                                            <template if:false={unavailableCollapsed}>
                                                <div class=" accounts-list-box">
                                                    <template iterator:it={unavailableFinancialAccounts}>
                                                        <template if:false={it.value.closedAccount}>  <!--SFP-54699 (Start)-->
                                                            <div key={it.value.finAcct.Id}>
                                                                <lightning-layout class="slds-var-p-right_medium fin-acct-details slds-grid_vertical-align-center financial-accounts-padding">
                                                                    <lightning-layout-item flexibility="auto" padding="horizontal-none" class="slds-text-align_left slds-align-middle unavailable-fa">
                                                                        <div>
                                                                            <h6>{it.value.finAcct.Name}</h6>
                                                                        </div>
                                                                        <p class="text-gray fin-accts-number-text"> 
                                                                            <template if:true={it.value.finAcct.Bank_BSB__c}>
                                                                                BSB <span class="acc-bsb-label">{it.value.bsbLabel}</span> &nbsp;
                                                                            </template> 
                                                                            <template if:true={originatedFromArrangement}>
                                                                                ACC &nbsp;
                                                                            </template> 
                                                                            <span class="acc-bsb-label">{it.value.accLabel}</span>
                                                                        </p>
                                                                    </lightning-layout-item>
                                                                    <lightning-layout-item flexibility="auto" padding="around-none" class="unavailable-fa-reason">
                                                                        <div>
                                                                            <!-- SFP-54699 (Start) temporary removal of closed / closed joint accounts past 24 months
                                                                            <template if:true={it.value.closedAccount}>
                                                                                <div class="slds-grid authorisation-alert-text slds-float_right unavailable_container">
                                                                                    <div class="slds-col slds-size_3-of-4 slds-align_absolute-center">
                                                                                        <h6 class="unavailable-reason-style unavailable_text">{it.value.unavailableReason}</h6>
                                                                                    </div>
                                                                                    <div class="slds-col slds-size_1-of-4 slds-align_absolute-center">
                                                                                        <svg xmlns="http://www.w3.org/2000/svg" class="slds-m-left_small svg-error unavailable_icon" aria-label="red cross">;
                                                                                            <use xlink:href={svgURL}></use>
                                                                                        </svg>
                                                                                    </div>
                                                                                </div>
                                                                            </template>  SFP-54699 (End)-->
                                                                            <template if:false={it.value.closedAccount}>
                                                                                <div class="slds-grid authorisation-alert-text slds-float_right unavailable_container">
                                                                                    <div class="slds-col slds-size_3-of-4 slds-align_absolute-center">
                                                                                        <h6 class="joint-account-style unavailable_text">{it.value.unavailableReason}</h6>
                                                                                    </div>
                                                                                    <div class="slds-col slds-size_1-of-4 slds-align_absolute-center">
                                                                                        <svg xmlns="http://www.w3.org/2000/svg" class="slds-m-left_small svg-joint unavailable_icon" aria-label="gray cross">;
                                                                                            <use xlink:href={svgURL}></use>
                                                                                        </svg>
                                                                                    </div>
                                                                                </div>
                                                                            </template>
                                                                        </div>
                                                                    </lightning-layout-item>
                                                                </lightning-layout>
                                                                <div if:false={it.last} key={it.value.finAcct.Id} class="slds-border_bottom border-style"></div>
                                                            </div>
                                                        </template> <!--SFP-54699 (End)-->
                                                    </template>
                                                </div>
                                            </template>
                                        </template>
                                    </template>
                                    <template if:true={isLoggedInUserSecondaryUser}>
                                        <!-- Account selection header -->
                                        <div>
                                            <lightning-layout class="slds-var-p-bottom_large table-header slds-grid_vertical-align-center">
                                                <lightning-layout-item size="7" flexibility="auto" padding="around-none" class="slds-text-align_left slds-align-middle section-header-pad">
                                                    <h3 class="hurme-text">Accounts available to share</h3>
                                                </lightning-layout-item>
                                                <template if:true={availableFinancialAccounts}>
                                                    <lightning-layout-item size="5" flexibility="auto"  padding="around-none" class="slds-text-align_right slds-var-p-right_large slds-float_right">
                                                        <h6 class="hurme-text">
                                                            <lightning-button-stateful  class="slds-button"
                                                                                        label-when-off="Select All"
                                                                                        label-when-on="Deselect All"
                                                                                        selected={deselectedAllAcc}
                                                                                        onclick={handleSelectAll}
                                                                                        variant="text">
                                                            </lightning-button-stateful>
                                                        </h6>
                                                    </lightning-layout-item>
                                                </template>
                                            </lightning-layout>
                                        </div>
        
                                        <!-- Error on financial account retrieval -->
                                        <template if:true={error}>
                                            <div class="slds-scoped-notification slds-media slds-media_center slds-scoped-notification_light" role="status">
                                                <div class="slds-media__figure">
                                                    <lightning-icon icon-name="utility:warning" variant="warning"></lightning-icon>
                                                </div>
                                                <div class="slds-media__body">
                                                    <p class="authorisation-text">Error retrieving Financial Accounts: {error}</p>
                                                </div>
                                            </div>
                                        </template>
        
                                        <!-- Available account selection -->
                                        <template if:true={availableFinancialAccounts}>
                                                <div class="accounts-list-box slds-border_left slds-border_bottom slds-border_right">
                                                    <template iterator:it={availableFinancialAccounts}>
                                                        <div key={it.value.finAcct.Id}>
                                                            <lightning-layout class="slds-grid_vertical-align-center fin-acct-details financial-accounts-padding">
                                                                <lightning-layout-item size="9" flexibility="auto" padding="horizontal-none" class="slds-text-align_left slds-align-middle slds-grid slds-grid_vertical">
                                                                    <div class="slds-col">
                                                                        <h6 class="acc-name-label">{it.value.finAcct.Name}
                                                                        </h6>
                                                                    </div>
                                                                    <div class="slds-col">
                                                                        <p class="text-gray fin-accts-number-text"> 
                                                                            <template if:true={it.value.finAcct.Bank_BSB__c}>
                                                                                BSB <span class="acc-bsb-label">{it.value.bsbLabel}</span> &nbsp;
                                                                            </template> 
                                                                            <template if:true={originatedFromArrangement}>
                                                                                ACC &nbsp;
                                                                            </template> 
                                                                            <span class="acc-bsb-label">{it.value.accLabel}</span>
                                                                        </p>
                                                                    </div>
                                                                    <div class="slds-col fa_status">
                                                                        <template if:true={it.value.isJointAccount}>
                                                                            <div class="slds-grid slds-grid_vertical-align-center">
                                                                                <div class="slds-col slds-grow-none " style="max-width: 50px;">
                                                                                    <svg xmlns="http://www.w3.org/2000/svg" class="svg-joint status_icon" aria-label="blue joint account">;
                                                                                        <use xlink:href={svgURLJointAccount}></use>
                                                                                    </svg>
                                                                                </div>
                                                                                <div class="slds-col slds-size_8-of-8">
                                                                                    <div class="slds-col slds-size_6-of-6">
                                                                                        <h6>Joint account</h6>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </template>
                                                                    </div>
                                                                    <div class="slds-col fa_status">
                                                                        <template if:true={it.value.closedAccount}>
                                                                            <div class="slds-grid slds-grid_vertical-align-center">
                                                                                 <!-- SFP-56156 - Remove cross symbol 
                                                                                <div class="slds-col slds-size_1-of-4 slds-align_absolute-center">
                                                                                    <svg xmlns="http://www.w3.org/2000/svg" class="slds-m-left_small svg-joint unavailable_icon" aria-label="gray cross">;
                                                                                        <use xlink:href={svgURL}></use>
                                                                                    </svg>
                                                                                </div>
                                                                                -->
                                                                                <div class="slds-col slds-size_8-of-8">
                                                                                    <div class="slds-col slds-size_6-of-6">
                                                                                        <h6 class="/*unavailable-reason-style*/ joint-account-style unavailable_text" >{it.value.unavailableReason}</h6>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </template>
                                                                    </div>
                                                                </lightning-layout-item>
                                                                <lightning-layout-item size="3" flexibility="auto" padding="around-none" class="slds-text-align_right slds-align-middle">                                                              
                                                                    <div class = "slds-float_right select-grid-container">
                                                                        <div class="select-grid-item" >
                                                                            <div class="slds-form-element">
                                                                                <label class="slds-checkbox_toggle slds-grid">
                                                                                  <span class="slds-form-element__label slds-m-bottom_none"></span>
                                                                                  <input
                                                                                        aria-checked={it.value.selected}
                                                                                        aria-label={it.value.finAcct.Name}
                                                                                        class="toggleSelection"
                                                                                        type="checkbox" 
                                                                                        onchange={handleSelect}
                                                                                        onkeypress={changeToggleValue}
                                                                                        name="selectFA" 
                                                                                        value={it.value.finAcct.Id}/>
                                                                                  <span aria-live="assertive" id={it.value.finAcct.Id} class="slds-checkbox_faux_container slds-align_absolute-center">
                                                                                    <span class="slds-checkbox_faux"></span>
                                                                                        <span class="slds-checkbox_on" aria-label="enabled"></span>
                                                                                        <span class="slds-checkbox_off" aria-label="disabled"></span>
                                                                                  </span>
                                                                                </label>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </lightning-layout-item>
                                                            </lightning-layout>
                                                            <div if:false={it.last} key={it.value.finAcct.Id} class="slds-border_bottom border-style "></div>
                                                        </div>
                                                    </template>
                                                </div>
                                            </template>
        
                                        <!--SFP-29331: Show this message when the user has no available Financial Accounts for selection-->
                                        <template if:false={availableFinancialAccounts}>
                                            <div class="no-fa-text accounts-list-box slds-text-align_center">
                                                <p class="authorisation-text slds-var-p-left_small slds-var-p-top_large slds-var-p-bottom_large">You do not have any available accounts to share.</p>
                                            </div>
                                        </template>
        
                                        <!-- Show this only if there are available Financial Accounts for selection -->
                                        <template if:true={availableFinancialAccounts}>
                                            <div class="slds-text-align_left num-selected-accounts">
                                                Number of selected account(s): {numberOfSelectedFas}
                                            </div>
                                        </template>
                                        
                                         <!-- Unavailable account selection -->
                                        <template if:true={IfUnavailableHide}>
                                            <div class="slds-var-m-top_large accounts-unavailable-list-box ">
                                                <lightning-layout>
                                                    <button class=" button__collapse slds-button_full-width slds-var-p-left_medium slds-var-p-right_medium slds-var-p-bottom_medium slds-var-p-top_medium slds-border_bottom border-style " onclick={handleUnavailableCollapse}>
                                                        <lightning-layout-item size="9" flexibility="auto" padding="around-none" class="slds-text-align_left">
                                                            <h6 style="color: rgb(0, 30, 65);">Accounts unavailable to share</h6>
                                                        </lightning-layout-item>
                                                            <div class="icons-chevron">
                                                                <!-- If collapsed -->
                                                                <template if:false={unavailableCollapsed}>
                                                                    <lightning-layout-item size="3" flexibility="auto" padding="around-none" class="slds-text-align_right slds-var-p-right_medium slds-float_right">
                                                                        <lightning-icon icon-name="utility:chevronup" class="slds-current-color" size="small"></lightning-icon>
                                                                    </lightning-layout-item>
                                                                </template>
                                                                
                                                                <!-- If expanded -->
                                                                <template if:true={unavailableCollapsed}>
                                                                    <lightning-layout-item size="3" flexibility="auto" padding="around-none" class="slds-text-align_right slds-var-p-right_medium slds-float_right">
                                                                        <lightning-icon icon-name="utility:chevrondown" class="slds-current-color" size="small"></lightning-icon>
                                                                    </lightning-layout-item>
                                                                </template>
                                                            </div>
                                                    </button>
                                                </lightning-layout>
                                            </div>
                                        </template>
        
            
                                        <!-- If expanded -->
                                        <template if:true={unavailableFinancialAccounts}>
                                            <template if:false={unavailableCollapsed}>
                                                <div class=" accounts-list-box">
                                                    <template iterator:it={unavailableFinancialAccounts}>
                                                        <template if:false={it.value.closedAccount}>  <!--SFP-54699 (Start)-->
                                                            <div key={it.value.finAcct.Id}>
                                                                <lightning-layout class="slds-var-p-right_medium fin-acct-details slds-grid_vertical-align-center financial-accounts-padding">
                                                                    <lightning-layout-item flexibility="auto" padding="horizontal-none" class="slds-text-align_left slds-align-middle unavailable-fa">
                                                                        <div>
                                                                            <h6>{it.value.finAcct.Name}</h6>
                                                                        </div>
                                                                        <p class="text-gray fin-accts-number-text"> 
                                                                            <template if:true={it.value.finAcct.Bank_BSB__c}>
                                                                                BSB <span class="acc-bsb-label">{it.value.bsbLabel}</span> &nbsp;
                                                                            </template> 
                                                                            <template if:true={originatedFromArrangement}>
                                                                                ACC &nbsp;
                                                                            </template> 
                                                                            <span class="acc-bsb-label">{it.value.accLabel}</span>
                                                                        </p>
                                                                    </lightning-layout-item>
                                                                    <lightning-layout-item flexibility="auto" padding="around-none" class="unavailable-fa-reason">
                                                                        <div>
                                                                            <!-- SFP-54699 (Start) temporary removal of closed / closed joint accounts past 24 months
                                                                            <template if:true={it.value.closedAccount}>
                                                                                <div class="slds-grid authorisation-alert-text slds-float_right unavailable_container">
                                                                                    <div class="slds-col slds-size_3-of-4 slds-align_absolute-center">
                                                                                        <h6 class="unavailable-reason-style unavailable_text">{it.value.unavailableReason}</h6>
                                                                                    </div>
                                                                                    <div class="slds-col slds-size_1-of-4 slds-align_absolute-center">
                                                                                        <svg xmlns="http://www.w3.org/2000/svg" class="slds-m-left_small svg-error unavailable_icon" aria-label="red cross">;
                                                                                            <use xlink:href={svgURL}></use>
                                                                                        </svg>
                                                                                    </div>
                                                                                </div>
                                                                            </template>  SFP-54699 (End)-->
                                                                            <template if:false={it.value.closedAccount}>
                                                                                <div class="slds-grid authorisation-alert-text slds-float_right unavailable_container">
                                                                                    <div class="slds-col slds-size_3-of-4 slds-align_absolute-center">
                                                                                        <h6 class="joint-account-style unavailable_text">{it.value.unavailableReason}</h6>
                                                                                    </div>
                                                                                    <div class="slds-col slds-size_1-of-4 slds-align_absolute-center">
                                                                                        <svg xmlns="http://www.w3.org/2000/svg" class="slds-m-left_small svg-joint unavailable_icon" aria-label="gray cross">;
                                                                                            <use xlink:href={svgURL}></use>
                                                                                        </svg>
                                                                                    </div>
                                                                                </div>
                                                                            </template>
                                                                        </div>
                                                                    </lightning-layout-item>
                                                                </lightning-layout>
                                                                <div if:false={it.last} key={it.value.finAcct.Id} class="slds-border_bottom border-style"></div>
                                                            </div>
                                                        </template> <!--SFP-54699 (End)-->
                                                    </template>
                                                </div>
                                            </template>
                                        </template>
                                    </template>
                                </template>
                            </template>

                        <!--Page body when isAuthSelectionRequired is false SFP-29251-->
                        <template if:false={initialDataResponse.isAuthSelectionRequired}>
                            <div class="content__body-main slds-text-align_left authorisation-text">
                                <!--h6 class="inline-text">{recipient}</h6> is requesting your data.-->
                                <!--SFP-41725-->
                                <h6 class="inline-text">{recipient}</h6>  is an accredited data recipient and is requesting data from your accounts. You can check their accreditation on the <a href="https://www.cdr.gov.au/find-a-provider" target="_blank"><u class="link-blue">current provider register</u></a>. <br/><br/>

                                AMP Bank may not share data with {recipient} for certain reasons, including if your account is not available in MY AMP. Refer to the <a href={cdrFAQLink} target="_blank" class="ds-text-font-style_unset float_none"><u class="link-blue">Consumer Data Right FAQ</u></a> for more details.
                                <!--SFP-41725-->
                            </div>
                        </template>
                        <template if:true={IfUnavailableHide}>
                            <template if:true={initialDataResponse.isAuthSelectionRequired}>
                                <div class="slds-var-p-top_medium slds-var-p-bottom_medium section-header-pad">
                                    <div style="position:relative" class=" authorisation-hover-text">
                                        <h6><a aria-describedby="dialog-body-id-98" tabindex="0" onmouseover={showHelpText} onclick={showHelpText} onkeypress={showHelpTextViaKey} class="help-text">
                                            Why can't I share these?
                                        </a></h6>
                                        <template if:true={helpText}>   
                                            <div class="slds-popover slds-nubbin_bottom-left popover-style">
                                                <button onclick={hideHelpText} class="slds-button slds-button_icon slds-button_icon-small slds-float_right slds-popover__close" title="Close dialog">
                                                    <lightning-icon icon-name="utility:close" class="slds-current-color" size="small"></lightning-icon>
                                                    <span aria-hidden="true" class="slds-assistive-text">Close dialog</span>
                                                </button>
                                                <div class="slds-popover__body" id="dialog-body-id-98"  role="dialog">
                                                    <div class="slds-media__body">
                                                        These accounts are unavailable because either:
                                                        <ul class="slds-list_dotted">
                                                            <template for:each={unavailableFaReasonsArray} for:item="unavailableFaReason">
                                                                <li key={unavailableFaReason}>{unavailableFaReason}</li>
                                                            </template>
                                                        </ul>
                                                        <br/>
                                                        For further assistance, please {labels.AMP_CONTACT_INFO}
                                                    </div>
                                                </div>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                            </template>
                        </template>    
                    </template>

                    <!-- Footer containing the buttons and message if required to be rendered -->
                    <div class="continue-cancel-footer slds-var-p-bottom_large  slds-grid_vertical-align-center slds-grid_horizontal-align-center">
                        <!-- SFP-33122 Only show Continue button-->
                        <template if:true={invalidSharingRequest}>
                            <div>
                                <h5 class="hurme-text">
                                    <!-- SFP-29240 added onclick -->
                                    <b><button type="button" class="slds-button button__large button-width_medium slds-button_brand slds-float_right" onclick={handleCancel}>Cancel</button></b>
                                </h5>
                            </div>
                        </template>
    
                        <template if:false={invalidSharingRequest}>
                            <!--SFP-29852-->
                            <template if:false={availableFinancialAccounts}>
                                
                                <!--Message is displayed when user is required to have available financial accounts to continue-->
                                <template if:false={initialDataResponse.hasOnlyConfirmationScope}>
                                    <p class="footer_consent-message slds-var-p-bottom_large">
                                        You are unable to proceed with this request because you do not have any accounts available for data sharing.
                                    </p>
                                </template>
                            </template>
                            <template if:true={originatedFromArrangement}>
                                <template if:true={disableContinue}>
                                    <p class="footer_consent-message slds-var-p-bottom_large">
                                        Please select at least one account for your consent. If you would like to withdraw your consent, select 'Cancel' to navigate back to the data sharing arrangement page and press 'Stop sharing'.                             
                                    </p>
                                </template>
                            </template>
                            <template if:true={isTypeCompany}>
                                <template if:true={isLoggedInUserNomRep}>
                                    <div >
                                        <h5 class="hurme-text">
                                            <button type="button" class="slds-button slds-button_brand button__large button-width_medium ss-button-cont" disabled={disableContinue} onclick={handleContinue}>Continue</button>
                                            <button type="button" class="slds-button slds-button_border button__large  button-width_medium ss-button-cancel slds-align_absolute-center" onclick={handleCancel}>Cancel</button>
                                        </h5>
                                    </div>
                                </template>
                            </template>
                            <template if:false={isTypeCompany}>
                                <template if:true={isLoggedInUserIndividualTrustee}>
                                    <div >
                                        <h5 class="hurme-text">
                                            <button type="button" class="slds-button slds-button_brand button__large button-width_medium ss-button-cont" disabled={disableContinue} onclick={handleContinue}>Continue</button>
                                            <button type="button" class="slds-button slds-button_border button__large  button-width_medium ss-button-cancel slds-align_absolute-center" onclick={handleCancel}>Cancel</button>
                                        </h5>
                                    </div>
                                </template>
                                <template if:true={isLoggedInUserSecondaryUser}>
                                    <div >
                                        <h5 class="hurme-text">
                                            <button type="button" class="slds-button slds-button_brand button__large button-width_medium ss-button-cont" disabled={disableContinue} onclick={handleContinue}>Continue</button>
                                            <button type="button" class="slds-button slds-button_border button__large  button-width_medium ss-button-cancel slds-align_absolute-center" onclick={handleCancel}>Cancel</button>
                                        </h5>
                                    </div>
                                </template>
                            </template>
                        </template>
                    </div>
                </template>
            </template>
            <!--To do: find a way to redirect to another lwc component-->
            <template if:false={renderPageNumber}>
                <template if:true={isLoggedInUserSecondaryUser}>
                    <c-cdr-confirmation  onshowpages={handlePages} selected-fin-accts={selectedFaSet} number-of-selected-fas={numberOfSelectedFas} initial-data-response={initialDataResponse} originated-from-arrangement={originatedFromArrangement} fin-acct-details-wrapper={finAcctDetailsWrapper} owner-acc-id={businessAccId}></c-cdr-confirmation>
                </template>
                <template if:false={isLoggedInUserSecondaryUser}>
                    <c-cdr-confirmation  onshowpages={handlePages} selected-fin-accts={selectedFaSet} number-of-selected-fas={numberOfSelectedFas} initial-data-response={initialDataResponse} originated-from-arrangement={originatedFromArrangement} fin-acct-details-wrapper={finAcctDetailsWrapper} business-acc-id={businessAccId}></c-cdr-confirmation>
                </template>
            </template>
        </template>
        <template if:false={isRecordFound}>
            <h2 class=" slds-text-align_left header-spacing font-style_light-bold hurme-text">Page can not be found.</h2>
        </template>
    </div>
</template>