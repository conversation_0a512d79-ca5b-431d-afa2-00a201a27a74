// SFP-51661, b<PERSON><PERSON> 

import { LightningElement, api } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { FlowNavigationNextEvent } from 'lightning/flowSupport';
import getMobileWebServiceInterface from '@salesforce/apex/ContactDetailsSectionController.getMobileWebServiceInterface';

const STR_SHOW = 'SHOW';
const STR_STARTED = 'STARTED';
const STR_FINISHED = 'FINISHED';
const STR_YES = 'YES';

export default class FormsDigiExperianPhoneInput extends LightningElement {
	isWarning = false;
	isError = false;
	globalSwitch = false;
	isDisabled = false;
	timeout = 10000;
	statusCode;
	authToken = '';
	url = '';
	mobileNumberClean;
	sessionStorageCheckInterval;
	experianMessage;
	@api successful;
	@api isRequired = false;
	@api validationStatus;
	@api validationMessage = 'Please enter a valid mobile number. Only numbers, single spaces and the plus sign + are allowed.';
	@api mobileNumber;
	@api errorMessage;
	@api warningMessage = 'Please check that your mobile number is correct before continuing.';

	connectedCallback(){
		// console.log('bn FormsDigiExperianPhoneInput connectedCallback 1');
		// console.log('bn FormsDigiExperianPhoneInput connectedCallback experianCheck',sessionStorage.getItem('formsDigiExperianPhoneInput.experianCheck'));
		
		this.mobileNumberClean = this.mobileNumber;

		getMobileWebServiceInterface().then(result => {
			this.globalSwitch = result.globalSwitch;
			this.url = result.url;
			this.authToken = result.authToken;
			this.timeout = result.timeout;
		}).catch(error => {
			console.error('bn connectedCallback getMobileWebServiceInterface error', error);
			clearInterval(this.sessionStorageCheckInterval);
			let event = new ShowToastEvent({
				title: 'Error!',
				variant: 'error',
				mode: 'dismissable'
			});
			this.dispatchEvent(event);
		});

		if(sessionStorage.getItem('formsDigiExperianPhoneInput.experianCheck') == STR_STARTED){
			this.sessionStorageCheckInterval = setInterval(() => {
				this.checkSessionStorage();
			}, 500);
		}
	}

	checkSessionStorage(){
		try {
			// console.log('bn FormsDigiExperianPhoneInput checkSessionStorage');
			let experianCheck = sessionStorage.getItem('formsDigiExperianPhoneInput.experianCheck');
			// console.log('bn FormsDigiExperianPhoneInput checkSessionStorage experianCheck',experianCheck);
			
			if(experianCheck == STR_FINISHED){
				// console.log('bn FormsDigiExperianPhoneInput clearInterval');
				clearInterval(this.sessionStorageCheckInterval);

				this.isDisabled = sessionStorage.getItem('formsDigiExperianPhoneInput.isDisabled') == STR_YES;
				this.isWarning = sessionStorage.getItem('formsDigiExperianPhoneInput.warningSection') == STR_SHOW;
				let emailIsWarning = sessionStorage.getItem('formsDigiExperianEmailInput.warningSection') == STR_SHOW;
				let emailExperianCheck = sessionStorage.getItem('formsDigiExperianEmailInput.experianCheck') == STR_FINISHED;
				
				// console.log('bn formsDigiExperianPhoneInput checkSessionStorage isWarning',this.isWarning);
				// console.log('bn formsDigiExperianPhoneInput checkSessionStorage emailIsWarning',emailIsWarning);
				// console.log('bn formsDigiExperianPhoneInput checkSessionStorage emailExperianCheck',emailExperianCheck);
				// console.log('bn formsDigiExperianPhoneInput checkSessionStorage isDisabled',this.isDisabled);


				if(!this.isWarning && (!emailIsWarning || (emailIsWarning && emailExperianCheck))){
					this.handleGoNext();
				}
			}
		} catch (error) {
			console.error('bn checkSessionStorage error',error);
		}
	}

	checkInput(e){
		try{
			let newVal = e.target.value;
			// console.log('bn FormsDigiExperianPhoneInput checkInput newVal',newVal);
			// console.log('bn FormsDigiExperianPhoneInput checkInput mobileNumber',this.mobileNumber);
			
			if(newVal && (!this.mobileNumber || newVal.toUpperCase() != this.mobileNumber.toUpperCase())){
				// console.log('bn FormsDigiExperianPhoneInput checkInput 2');
				this.mobileNumber = newVal;
				this.isWarning = false;
				sessionStorage.setItem('formsDigiExperianPhoneInput.warningSection', '');

				//validate using SF
				const inputValidation = this.template.querySelector('lightning-input')
				inputValidation.reportValidity();
				let valid = inputValidation.checkValidity();
				// console.log('bn FormsDigiExperianPhoneInput checkInput valid1',valid);

				//AU
				if(newVal.length == 10 && newVal.startsWith('04')){
					// console.log('bn FormsDigiExperianPhoneInput checkInput AU');
					this.mobileNumberClean = newVal.replace('0', '61');
					// console.log('bn FormsDigiExperianPhoneInput checkInput mobileNumberClean',this.mobileNumberClean);
				}
				//NZ
				else if(newVal.length >= 9 && newVal.length <= 11 && newVal.startsWith('02')){
					// console.log('bn FormsDigiExperianPhoneInput checkInput NZ');
					this.mobileNumberClean = newVal.replace('0', '64');
					// console.log('bn FormsDigiExperianPhoneInput checkInput mobileNumberClean',this.mobileNumberClean);
				}
				else{
					this.mobileNumberClean = newVal;
				}
				
				//validate using API callout
				if(valid){
					this.doExperianCheck();
				}
			}else{
				this.mobileNumberClean = newVal;
			}
		}catch(error){
			console.error('bn error', error);
		}
	}
	
	@api
	validate(){
		try{
			// console.log('bn FormsDigiExperianPhoneInput validate');
			const inputValidation = this.template.querySelector('lightning-input')
			inputValidation.reportValidity();
			let valid = inputValidation.checkValidity();
			
			// console.log('bn FormsDigiExperianPhoneInput validate valid',valid);
			
			if(valid){
				// console.log('bn FormsDigiExperianPhoneInput validate valid2');
				let experianCheck = sessionStorage.getItem('formsDigiExperianPhoneInput.experianCheck');
				let emailExperianCheck = sessionStorage.getItem('formsDigiExperianEmailInput.experianCheck');

				if(experianCheck != STR_STARTED && emailExperianCheck != STR_STARTED){
					// console.log('bn FormsDigiExperianPhoneInput validate valid = true');
					this.successful = true; 
					return {isValid : true};
				}

				return {isValid : false, errorMessage: ''};
			}
		}catch(error){
			console.error('bn validate error',error);
		}
		
		return {isValid : false, errorMessage: this.validationMessage};
	}

	doExperianCheck(){
		// console.log('bn FormsDigiExperianPhoneInput doExperianCheck');
		sessionStorage.setItem('formsDigiExperianPhoneInput.experianCheck', STR_STARTED);
		sessionStorage.setItem('formsDigiExperianPhoneInput.isDisabled', STR_YES);
				
		this.isDisabled = true;

		// check if global switch isn't disabled
		if(!this.globalSwitch){
			// replace starting mobile numbers for AU and NZ with proper country codes
			this.mobileNumberClean = (this.mobileNumber).replace(/[+() ]/g, '');
			if(this.mobileNumberClean.startsWith('04') && this.mobileNumberClean.length == 10){ // AU
				this.mobileNumberClean = (this.mobileNumber).replace('04','614');
			} else if(this.mobileNumberClean.startsWith('02') && this.mobileNumberClean.length <= 11 && this.mobileNumberClean.length >= 9){ // NZ
				this.mobileNumberClean = (this.mobileNumber).replace('02','642');
			}
			// console.log('bn FormsDigiExperianPhoneInput doExperianCheck mobileNumberClean',this.mobileNumberClean);

			const params = {
				method: 'POST',
				headers: {
				"Content-Type" : "application/json",
				"Auth-Token": this.authToken,
				"Timeout-Seconds" : this.timeout
				},
				body: '{"number":"'+ this.mobileNumberClean+'"}'
			};

			fetch(this.url, params).then(response => {
				this.statusCode = response.status;
				// console.log('bn FormsDigiExperianPhoneInput doExperianCheck statusCode', this.statusCode);
				return response.json(); // returning the response in the form of JSON
			})
			.then((jsonResponse) => {
				// console.log('bn FormsDigiExperianPhoneInput jsonResponse',jsonResponse);
				let objData = {
					resultConfidence : '',
				};

				this.successful = false;
				if(this.statusCode == 200 && jsonResponse.result){
					// retriving the response data
					let exchangeData = jsonResponse.result;

					// adding data object
					objData.resultConfidence = exchangeData.confidence;
					if(objData.resultConfidence){
						//get response message and corresponding icons, styling and status
						this.isWarning = false;
						switch(objData.resultConfidence){
							case 'Verified':
								this.successful = true;
								this.experianMessage = 'Successfully verified as reachable';
								this.validationStatus = 'Verified';
								break;
							case 'Unverified':
								this.experianMessage = 'Mobile is unreachable';
								this.validationStatus = 'Unverified';
								this.isWarning = true;
								break;
							case 'Unknown':
								this.experianMessage = 'Unable to verify mobile but it may be correct';
								this.validationStatus = 'Unknown';
								this.isWarning = true;
								break;
							case 'Absent':
								this.successful = true;
								this.experianMessage = 'Successfully verified but not currently reachable';
								this.validationStatus = 'Absent';
								break;
							case 'Teleservice not provisioned':
								this.experianMessage = 'Mobile is unreachable';
								this.validationStatus = 'Teleservice not provisioned';
								this.isWarning = true;
								break;
							case 'No coverage':
								this.experianMessage = 'Mobile is unreachable';
								this.validationStatus = 'No coverage';
								this.isWarning = true;
								break;
							case 'Dead':
								this.experianMessage = 'Mobile is unreachable';
								this.validationStatus = 'Dead';
								this.isWarning = true;
								break;
						}
					}
				} else {
					this.experianMessage = 'Verification Service unavailable';
					this.validationStatus = 'Unavailable';
					this.isWarning = true;
				}

				// console.log('bn FormsDigiExperianPhoneInput doExperianCheck experianMessage',this.experianMessage)
				// console.log('bn FormsDigiExperianPhoneInput doExperianCheck validationStatus',this.validationStatus)
				// console.log('bn FormsDigiExperianPhoneInput doExperianCheck isWarning',this.isWarning)

				sessionStorage.setItem('formsDigiExperianPhoneInput.experianCheck', STR_FINISHED);
				sessionStorage.setItem('formsDigiExperianPhoneInput.isDisabled', '');

				if(this.isWarning){
					sessionStorage.setItem('formsDigiExperianPhoneInput.warningSection', STR_SHOW);
				}else{
					sessionStorage.setItem('formsDigiExperianPhoneInput.warningSection', '');
				}

				this.isDisabled = false;
			})
			.catch(error => {
				console.error('bn doExperianCheck error',error);
				clearInterval(this.sessionStorageCheckInterval);
			})
		}
		// else global switch is disabled
		else {
			this.experianMessage = 'Verification Service is switched off';
			this.validationStatus = 'Switched off';
		}
	}

	handleGoNext() {
		// console.log('bn FormsDigiExperianPhoneInput handleGoNext');
		sessionStorage.setItem('formsDigiExperianPhoneInput.experianCheck','');
		sessionStorage.setItem('formsDigiExperianPhoneInput.warningSection', '');

		this.dispatchEvent(new FlowNavigationNextEvent());
    }
}