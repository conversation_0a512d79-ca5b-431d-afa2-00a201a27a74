/**
 * Created by jerry.poon on 18/06/2021. 
 */

import {LightningElement, api} from 'lwc';
import getGoals from '@salesforce/apex/ClientCommXplanService.getXplanGoals';
import {loadStyle} from "lightning/platformResourceLoader";
import css from '@salesforce/resourceUrl/clientCommXplanStyle';

export default class ClientCommXplanGoals extends LightningElement {
    goalDetails;
    loaded = false;

    @api
    get loadSuccess() {
        return this.loaded && this.goalDetails;
    }
    @api
    get loadFail() {
        return this.loaded && !this.goalDetails;
    }

    //Load css
    constructor() {
        super();
        Promise.all([
            loadStyle(this, css)
        ]).then(() => {
            console.log("Styling loaded");
        }).catch(error => {
            console.error('Error loading static resources', error);
        });
    }

    connectedCallback() {
        getGoals()
            .then(result => {
                this.processXplanData(JSON.parse(result));
            })
            .catch(error => {
                this.errorMsg = 'Error: ' + error.message;
                this.loaded = true;
            });
    }

    processXplanData(response) {
        if(response && response.isSuccess) {
            if(response.result) {
                this.goalDetails = JSON.parse(response.result);
            }
            this.loaded = true;
        } else {
            this.errorMsg = response.errorMessage;
            this.loaded = true
        }
    }
}