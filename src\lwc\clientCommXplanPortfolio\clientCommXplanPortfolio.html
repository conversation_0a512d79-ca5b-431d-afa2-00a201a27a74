<!--
 - Created by jerry.poon on 18/06/2021.
 -->
 
<!-- Client Comm Xplan Portfolio -->
<template>
    <!-- Spinner -->
    <template if:false={loaded}>
        <div class="slds-is-relative">
            <lightning-spinner alternative-text="Loading" size="large"></lightning-spinner>
        </div>
    </template>

    <!--Portfolio Summary-->
    <template if:true={loadSuccess}>
        <lightning-datatable
                key-field="index"
                columns={columns}
                data={tableData}
                column-widths-mode="auto"
                hide-checkbox-column>
        </lightning-datatable>
    </template>

    <!-- Error handling -->
    <template if:true={loadFail}>
        {errorMsg}
    </template>
</template>