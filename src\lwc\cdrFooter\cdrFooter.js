/*
 * Created for SFP-48972 claudine.ignacio/nikolai.datan
 */ 

import { LightningElement, api } from 'lwc';
import { loadStyle } from "lightning/platformResourceLoader";

//Social Logos from static resource
import FB_LOGO from '@salesforce/resourceUrl/AMP_social_facebook_icon_svg';
import TW_LOGO from '@salesforce/resourceUrl/AMP_social_twitter_icon_svg';
import LI_LOGO from '@salesforce/resourceUrl/AMP_social_linkedin_icon_svg';
import YT_LOGO from '@salesforce/resourceUrl/AMP_social_youtube_icon_svg';

// Custom labels for My AMP Footer Links
import HELP_AND_EDUC from '@salesforce/label/c.My_AMP_Footer_Help_and_education_Link';
import CONTACT_US from '@salesforce/label/c.My_AMP_Footer_Contact_us_Link';
import PRIVACY_POLICY from '@salesforce/label/c.My_AMP_Footer_Privacy_policy_Link';
import TERMS_CONDITIONS from '@salesforce/label/c.My_AMP_Footer_Terms_and_conditions_Link';
import FS_GUIDE from '@salesforce/label/c.My_AMP_Footer_Financial_services_guide_Link';
import SOCIAL_FACEBOOK from '@salesforce/label/c.My_AMP_Footer_Facebook_Link';
import SOCIAL_TWITTER from '@salesforce/label/c.My_AMP_Footer_Twitter_Link';
import SOCIAL_LINKEDIN from '@salesforce/label/c.My_AMP_Footer_Linkedin_Link';
import SOCIAL_YOUTUBE from '@salesforce/label/c.My_AMP_Footer_Youtube_Link';

//CSS from static resource
import cdrCss from '@salesforce/resourceUrl/CDR_Styles';

export default class cdrFooter extends LightningElement {

    fbUrl = FB_LOGO;
    twUrl = TW_LOGO;
    liUrl = LI_LOGO;
    ytUrl = YT_LOGO;

    loaded = true;

    labels = {       
        HELP_AND_EDUC,
        CONTACT_US,
        PRIVACY_POLICY,
        TERMS_CONDITIONS,
        FS_GUIDE,
        SOCIAL_FACEBOOK,
        SOCIAL_TWITTER,
        SOCIAL_LINKEDIN,
        SOCIAL_YOUTUBE
    };

    currentYear = new Date().getFullYear();

    constructor() {
        super();
        Promise.all([
            loadStyle(this, cdrCss)
        ]).then(() => {
            // console.log("Loaded css");
        }).catch(error => {
            console.error('Error loading static resources', error);
        });
    }
}