ul{
    padding: 0.5rem 0;
}
li{
    display: flex;
    flex-flow: row wrap;
    align-items: center;
    border-left: 3px solid;
}
li.earliest{
    border-left-width: 5px;
}
li div{
    padding: 0 0 1rem 0;
}
li div.date{
    font-size: 0.65rem;
    padding-left: 0.75rem;
    color: rgb(115, 118, 110);
}
li.earliest div.date{
    color: rgb(var(--main-text-color-rgb));
}

li div.date strong{
    font-size: 2rem;
    font-weight: 600;
    display: block;
    line-height: 1;
}
li div.description{
    font-size: 0.7rem;
    line-height: 1.3;
    color: rgb(115, 118, 110);
}
li div.description strong.rowHeading{
    font-size: 1rem;
    text-transform: uppercase;
    font-weight: 600;
    display: block;
    line-height: 1.8;
    color: rgb(var(--main-text-color-rgb));
}
h2{
    text-transform: uppercase;
    font-weight: 400;
}