<template>
    <template if:true={success}>
        <div class="slds-scoped-notification slds-media slds-media_center slds-theme_success" role="status">
            <div class="slds-media__figure">
                <lightning-icon icon-name="utility:success" size="small" variant="inverse">
                </lightning-icon>
            </div>
            <div class="slds-media__body">
                <lightning-formatted-rich-text value={message}></lightning-formatted-rich-text>
            </div>
        </div>
    </template>
    <template if:true={warning}>
        <div class="slds-scoped-notification slds-media slds-media_center slds-theme_warning" role="status">
            <div class="slds-media__figure">
                <lightning-icon icon-name="utility:warning" size="small">
                </lightning-icon>
            </div>
            <div class="slds-media__body">
                <lightning-formatted-rich-text value={message}></lightning-formatted-rich-text>
            </div>
        </div>
    </template>
    <template if:true={error}>
        <div class="slds-scoped-notification slds-media slds-media_center slds-theme_error" role="status">
            <div class="slds-media__figure">
                <lightning-icon icon-name="utility:error" size="small" variant="inverse">
                </lightning-icon>
            </div>
            <div class="slds-media__body">
                <lightning-formatted-rich-text value={message}></lightning-formatted-rich-text>
            </div>
        </div>
    </template>
    <template if:true={infoLight}>
        <div class="slds-scoped-notification slds-media slds-media_center slds-scoped-notification_light" role="status">
            <div class="slds-media__figure">
                <lightning-icon icon-name="utility:info" size="small">
                </lightning-icon>
            </div>
            <div class="slds-media__body">
                <lightning-formatted-rich-text value={message}></lightning-formatted-rich-text>
            </div>
        </div>
    </template>
    <template if:true={infoDark}>
        <div class="slds-scoped-notification slds-media slds-media_center slds-theme_info" role="status">
            <div class="slds-media__figure">
                <lightning-icon icon-name="utility:info" size="small" variant="inverse">
                </lightning-icon>
            </div>
            <div class="slds-media__body">
                <lightning-formatted-rich-text value={message}></lightning-formatted-rich-text>
            </div>
        </div>
    </template>
</template>