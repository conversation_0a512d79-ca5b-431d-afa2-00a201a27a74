<?xml version="1.0" encoding="UTF-8"?>
<PermissionSet xmlns="http://soap.sforce.com/2006/04/metadata">
    <description>This permission set gives Users an edit access to Secondary User Instruction field in Financial Account Role object</description>
    <fieldPermissions>
        <editable>false</editable>
        <field>FinServ__FinancialAccountRole__c.FinServ__RelatedAccount__c</field>
        <readable>true</readable>
    </fieldPermissions>
    <fieldPermissions>
        <editable>false</editable>
        <field>FinServ__FinancialAccountRole__c.FinServ__RelatedContact__c</field>
        <readable>true</readable>
    </fieldPermissions>
    <fieldPermissions>
        <editable>false</editable>
        <field>FinServ__FinancialAccountRole__c.FinServ__Role__c</field>
        <readable>true</readable>
    </fieldPermissions>
    <fieldPermissions>
        <editable>true</editable>
        <field>FinServ__FinancialAccountRole__c.Note__c</field>
        <readable>true</readable>
    </fieldPermissions>
    <fieldPermissions>
        <editable>false</editable>
        <field>FinServ__FinancialAccountRole__c.SCV_RelatedAccount__c</field>
        <readable>true</readable>
    </fieldPermissions>
    <fieldPermissions>
        <editable>true</editable>
        <field>FinServ__FinancialAccountRole__c.Secondary_User_Instruction__c</field>
        <readable>true</readable>
    </fieldPermissions>
    <hasActivationRequired>false</hasActivationRequired>
    <label>AMP Bank Assisted channel</label>
    <objectPermissions>
        <allowCreate>false</allowCreate>
        <allowDelete>false</allowDelete>
        <allowEdit>true</allowEdit>
        <allowRead>true</allowRead>
        <modifyAllRecords>false</modifyAllRecords>
        <object>FinServ__FinancialAccountRole__c</object>
        <viewAllRecords>false</viewAllRecords>
    </objectPermissions>
    <objectPermissions>
        <allowCreate>false</allowCreate>
        <allowDelete>false</allowDelete>
        <allowEdit>false</allowEdit>
        <allowRead>true</allowRead>
        <modifyAllRecords>false</modifyAllRecords>
        <object>FinServ__FinancialAccount__c</object>
        <viewAllRecords>false</viewAllRecords>
    </objectPermissions>
    <recordTypeVisibilities>
        <recordType>FinServ__FinancialAccountRole__c.FinServ__AccountRole</recordType>
        <visible>true</visible>
    </recordTypeVisibilities>
    <recordTypeVisibilities>
        <recordType>FinServ__FinancialAccountRole__c.FinServ__ContactRole</recordType>
        <visible>true</visible>
    </recordTypeVisibilities>
</PermissionSet>
