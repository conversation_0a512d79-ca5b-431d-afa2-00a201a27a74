import { LightningElement, api } from 'lwc'; 

const constDate = 'date';
const constNumber = 'number';
const constPicklist = 'picklist';
const constCurrency = 'currency';//SFP-59426, burhan
// const constEmail = 'email';






export default class FlowTableRow extends LightningElement {

	@api picklistOptions = [];
	@api rowIndex = 0;
	@api isEditMode = false;
	@api required = false;
	@api disabled = false;
	@api readonly = false;
	@api colType = 'text';
	@api colIndex = 0;
	@api label = '';
	@api inputKeys = [];
	@api item = {};
	@api minValue;//SFP-59427, burhan
	@api maxValue;//SFP-59427, burhan
	@api step;//SFP-59427, burhan
	localData = {};
	localPicklistOptionsArr = [];
	localPicklistOptions = [];
	// isText;
	// isNumber;
	isPicklist;
	// isEmail;
	// isDate;
	itemArr = [];
	itemId;
	inputName = '';
	rowValue = '';
	// minValue = '';//SFP-59427, burhan, commented-out
	colTypeToUse;//SFP-59427, burhan

	connectedCallback(){
		try {
			this.localData = JSON.parse(JSON.stringify(this.item));//convert proxy into local object

		this.itemArr = this.localData.map(e => {
			return e.value;
		});

		if(this.localData !=""){
		
		this.itemId = this.localData[0].id;
		}



		let inputKeyArr = this.inputKeys.split(',');

		this.inputName = inputKeyArr[this.colIndex+1];
		
		if(this.isEditMode){
			this.rowValue = this.itemArr[this.colIndex];

			if(this.colType == constDate && this.rowValue == 'undefined'){
				this.rowValue = '';
			}

			if(this.colType == constPicklist && this.rowValue == 'undefined'){
				this.rowValue = '';
			}

			if(this.rowValue != undefined && this.rowValue != ''){
				this.rowValue = this.rowValue.trim();
			}
		}else{
			this.rowValue = this.itemArr[this.colIndex] || String.fromCharCode(160);
		}

		if(this.colType == constPicklist){
			this.isPicklist = true;
			this.colTypeToUse = constPicklist;//SFP-59427, burhan

			if(this.isEditMode){
				this.localPicklistOptionsArr = JSON.parse(JSON.stringify(this.picklistOptions));
				let optionsArr = this.localPicklistOptionsArr.filter(e => e.field == this.inputName);
				if(optionsArr && Array.isArray(optionsArr)){
					this.localPicklistOptions = optionsArr[0].value;
				}
			}
		}else{
			this.isPicklist = false;

			if(this.colType == constDate){
				this.colTypeToUse = constDate;//SFP-59427, burhan
				// this.minValue = '1900-01-01';//SFP-59427, burhan, commented-out

				//SFP-59427, burhan, added this to avoid the need to edit the nominated beneficiary and pension payment flows, and prevent regression
				if(!this.minValue){
					this.minValue = '1900-01-01';
				}

				if(this.rowValue){
					if(this.isEditMode){
						let tempArr2 = this.rowValue.split('/');
						if(tempArr2[0] && tempArr2[1] && tempArr2[2]){
							this.rowValue = tempArr2[2] + '-' + tempArr2[1] + '-' + tempArr2[0];
						}
					}
						//SFP-60758, burhan
						else{
							let tempArr2 = this.rowValue.split('-');
							if(tempArr2[0] && tempArr2[1] && tempArr2[2]){
								this.rowValue = tempArr2[2] + '/' + tempArr2[1] + '/' + tempArr2[0];
							}
						}
					}
			} else if(this.colType == constNumber){
				this.colTypeToUse = constNumber;//SFP-59427, burhan
				// this.minValue = '1';//SFP-59427, burhan, commented-out

					//SFP-59427, burhan, added this to avoid the need to edit the nominated beneficiary and pension payment flows, and prevent regression
					if(!this.minValue){
						this.minValue = '1';
					}
				} 
				//SFP-59426, burhan
				else if(this.colType == constCurrency){
					this.colTypeToUse = constNumber;//SFP-59427, burhan
					//console.log('bn FlowTableRow connectedCallback this.rowValue',this.rowValue);
					if(!this.isEditMode){
						if(this.rowValue && this.rowValue != '' && this.rowValue != String.fromCharCode(160)){
							let isNegative = false;

						//trim
						this.rowValue = this.rowValue.toString().trim();

						//remove (-)
						if(this.rowValue.indexOf('-') == 0){
							this.rowValue = this.rowValue.slice(1, this.rowValue.length);
							isNegative = true;
						}

						//remove $
						if(this.rowValue.includes('$')){
							this.rowValue = this.rowValue.slice(1, this.rowValue.length);
						}

							//SFP-49254, burhan, use with (Input) Table delimiter JSON, total computation is not yet supported for numbers with comma in the parent component flowFormTable
							//remove (,)
							if(this.rowValue.includes(',')){
								// console.log('bn view rowValue w/ comma',this.rowValue);
								this.rowValue = this.rowValue.replace(/,/g, '');
								// console.log('bn view rowValue w/o comma',this.rowValue);
							}

							//set decimal places to fixed 2
							this.rowValue = parseFloat(this.rowValue).toFixed(2);

						//add comma for thousands
						this.rowValue = this.numberWithCommas(this.rowValue);

						//add $
						this.rowValue = '$'.concat(this.rowValue);

							//add (-)
							if(isNegative){
								this.rowValue = '-'.concat(this.rowValue);
							}
						}
					}
				}else{
					this.colTypeToUse = this.colType;//SFP-59427, burhan
				}
			}
			//console.log('bn FlowTableRow connectedCallback this.colTypeToUse',this.colTypeToUse);
			//console.log('bn FlowTableRow connectedCallback this.rowValue2',this.rowValue);
		} catch (error) {
			console.error('bn FlowTableRow connectedCallback error',error);
		}
	}

	// @api
	// isInputValid() {
	// 	let isValid = false;
	// 	let inputValidation = this.isPicklist ? this.template.querySelector('lightning-combobox') : this.template.querySelector('lightning-input');
	// 	if(inputValidation){
	// 		inputValidation.reportValidity();
	// 		isValid = inputValidation.checkValidity();
	// 	}

	// 	// if(!this.required){
	// 	// 	isValid = true;
	// 	// }
	// 	return isValid
	// }
  
	// @api
	// setCustomValidation(message) {
	//   const inputValidation = this.template.querySelector('lightning-input')
	//   inputValidation.setCustomValidity(message);
	//   inputValidation.reportValidity();
	// }

	handleOnChangeInput(event) {
		let id = event.target ? event.target.dataset.id : event.detail.dataset.id;
		let name = event.target ? event.target.name : event.detail.name;
		let value = event.target ? event.target.value: event.detail.value;
        let element = this.localData.find(e => e.id == id && e.key == name);
		if(element){
			this.dispatchEvent(new CustomEvent('inputchange', {
				detail: {
					id: id,
					key: name,
					value: value
				}
			}))
		}
    }

	numberWithCommas(value) {
		//return value.toString().replace(/\B(?<!\.\d*)(?=(\d{3})+(?!\d))/g, ",");//SFP-60543, burhan, commented-out
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");//SFP-60543, burhan
	}
}