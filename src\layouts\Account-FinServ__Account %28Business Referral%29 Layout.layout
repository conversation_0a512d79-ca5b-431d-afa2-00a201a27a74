<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Account Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ParentId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AccountNumber</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Site</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Type</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Industry</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AnnualRevenue</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FinServ__Notes__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Principal_Bus_addr_same_as_Reg_Office__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Principal_Place_of_Business_Address__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Company_s_Australian_Address__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Australian_Address__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>RecordTypeId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Rating</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Phone</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Fax</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Website</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TickerSymbol</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Ownership</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>NumberOfEmployees</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Financial Summary</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FinServ__TotalFinancialAccounts__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FinServ__TotalBankDeposits__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FinServ__AUM__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FinServ__TotalInvestments__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FinServ__TotalInsurance__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FinServ__WalletShare__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Address Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BillingAddress</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ShippingAddress</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Referral Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FinServ__ReferredByUser__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FinServ__ReferredByContact__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Description Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
        <platformActionListItems>
            <actionName>FinServ__NewEvent</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>0</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__NewEventAdvisor</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>1</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__NewEventClientAssociate</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>2</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__NewTask</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>3</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__NewTaskAdvisor</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>4</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__NewTaskClientAssociate</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>5</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__LogACall</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>6</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__LogACallAdvisor</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>7</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__LogACallClientAssociate</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>8</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>SendEmail</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>9</sortOrder>
        </platformActionListItems>
    </platformActionList>
    <relatedLists>
        <fields>FULL_NAME</fields>
        <fields>CONTACT.TITLE</fields>
        <fields>CONTACT.EMAIL</fields>
        <fields>CONTACT.PHONE1</fields>
        <relatedList>RelatedContactList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>FinServ__RelatedAccount__c</fields>
        <fields>FinServ__Role__c</fields>
        <fields>FinServ__StartDate__c</fields>
        <fields>FinServ__EndDate__c</fields>
        <fields>FinServ__Active__c</fields>
        <relatedList>FinServ__AccountAccountRelation__c.FinServ__Account__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>CONTACT.FULL_NAME</fields>
        <fields>ACCCONRELATION.ROLES</fields>
        <fields>ACCCONRELATION.START_DATE</fields>
        <fields>ACCCONRELATION.END_DATE</fields>
        <fields>ACCCONRELATION.IS_ACTIVE</fields>
        <relatedList>RelatedAccountContactRelationList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>FinServ__FinancialHolding__c.FinServ__PrimaryOwner__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>FinServ__Status__c</fields>
        <fields>FinServ__FinancialAccountNumber__c</fields>
        <fields>FinServ__Balance__c</fields>
        <fields>FinServ__OpenDate__c</fields>
        <relatedList>FinServ__FinancialAccount__c.FinServ__PrimaryOwner__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>FinServ__Status__c</fields>
        <fields>FinServ__FinancialAccountNumber__c</fields>
        <fields>FinServ__Balance__c</fields>
        <fields>FinServ__OpenDate__c</fields>
        <relatedList>FinServ__FinancialAccount__c.FinServ__JointOwner__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>FinServ__FinancialAccount__c</fields>
        <fields>FinServ__Role__c</fields>
        <fields>FinServ__Active__c</fields>
        <relatedList>FinServ__FinancialAccountRole__c.FinServ__RelatedAccount__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>FinServ__Description__c</fields>
        <fields>FinServ__AssetsAndLiabilitiesType__c</fields>
        <fields>FinServ__Amount__c</fields>
        <relatedList>FinServ__AssetsAndLiabilities__c.FinServ__PrimaryOwner__c</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedNoteList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>OPPORTUNITY.NAME</fields>
        <fields>OPPORTUNITY.STAGE_NAME</fields>
        <fields>OPPORTUNITY.AMOUNT</fields>
        <fields>OPPORTUNITY.CLOSE_DATE</fields>
        <relatedList>RelatedOpportunityList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>CASES.CASE_NUMBER</fields>
        <fields>NAME</fields>
        <fields>CASES.SUBJECT</fields>
        <fields>CASES.PRIORITY</fields>
        <fields>CASES.CREATED_DATE_DATE_ONLY</fields>
        <fields>CASES.STATUS</fields>
        <fields>OWNER_NAME</fields>
        <relatedList>RelatedCaseList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>FinServ__Type__c</fields>
        <fields>FinServ__SubType__c</fields>
        <fields>FinServ__Amount__c</fields>
        <fields>FinServ__Date__c</fields>
        <relatedList>FinServ__Revenue__c.FinServ__Account__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>FULL_NAME</fields>
        <fields>LEAD.COMPANY</fields>
        <fields>LEAD.PHONE</fields>
        <relatedList>Lead.FinServ__RelatedAccount__c</relatedList>
    </relatedLists>
    <relatedObjects>ParentId</relatedObjects>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>true</showHighlightsPanel>
    <showInteractionLogPanel>true</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
