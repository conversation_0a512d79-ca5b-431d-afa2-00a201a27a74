#greenid-container {
  font-family: "Open Sans", sans-serif;
  color: #525252;
  font-size: 16px;
  line-height: 24px;
}
#greenid-container #greenid-source-content {
    max-width: 97%;
}
#greenid-container h1, #greenid-container .h1 {
  font-family: "Open Sans", sans-serif;
  font-weight: normal;
  font-size: 24px;
  line-height: 36px;
  color: #001E41;
  margin-top: 0px;
}
@media only screen and (max-width: 63.9em) {
   font-size: 20px;
   line-height: 30px;
}
#greenid-container .lead {
  font-family: "Open Sans", sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #525252;
}
#greenid-container h2, #greenid-container .h2 {
  font-family: "Open Sans", sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #001E41;
}
#greenid-container #greenid-source-header #greenid-source-title h2 {
  margin-right: 0;
}
#greenid-container h4, #greenid-container .h4 {
  color: #001E41;
}
#greenid-source-title {
  display: block;
}
#greenid-container a, #greenid-container .greenid-helpicon, #greenid-container .greenid-helpicon-alt {
  color: #3B4E98;
}
#greenid-container #greenid-source-header #greenid-source-title .glyphicon {
  margin-right: inherit;
  top: 0;
}
#greenid-container label {
  font-weight: normal;
  font-size: 14px;
  line-height: 21px;
}
#greenid-container .form-control {
  font-size: 16px;
  height: 44px;
  padding: 8px 12px;
  color: #525252;
  border-radius: 0px;
}
#greenid-container .btn {
  font-size: 16px;
  border-radius: 0px;
  padding: 10px 12px;
  font-weight: 600;
}
#greenid-container .btn-default {
  color: #525252;
}
#greenid-container .btn-primary {
  background-color: #3B4E98;
  border-color: #3B4E98;
  color: #fff;
}
#greenid-container .btn-primary:hover, #greenid-container .btn-primary:focus, #greenid-container .btn-primary:active, #greenid-container .btn-primary.active {
    background-color: #3B4E98;
    border-color: #3B4E98;
}
#greenid-container .btn:focus, #greenid-container .btn:active:focus, #greenid-container .btn.active:focus {
  box-shadow: 0 0 0 4px rgb(0 125 250 / 60%);
  outline: none;
}
.greenid-datepicker-container.datepicker table tr td.active:hover, .greenid-datepicker-container.datepicker table tr td.active:hover:hover, .greenid-datepicker-container.datepicker table tr td.active.disabled:hover, .greenid-datepicker-container.datepicker table tr td.active.disabled:hover:hover, .greenid-datepicker-container.datepicker table tr td.active:focus, .greenid-datepicker-container.datepicker table tr td.active:hover:focus, .greenid-datepicker-container.datepicker table tr td.active.disabled:focus, .greenid-datepicker-container.datepicker table tr td.active.disabled:hover:focus, .greenid-datepicker-container.datepicker table tr td.active:active, .greenid-datepicker-container.datepicker table tr td.active:hover:active, .greenid-datepicker-container.datepicker table tr td.active.disabled:active, .greenid-datepicker-container.datepicker table tr td.active.disabled:hover:active, .greenid-datepicker-container.datepicker table tr td.active.active, .greenid-datepicker-container.datepicker table tr td.active:hover.active, .greenid-datepicker-container.datepicker table tr td.active.disabled.active, .greenid-datepicker-container.datepicker table tr td.active.disabled:hover.active {
  background-color: #3B4E98;
  border-color: #3B4E98;
}
#greenid-container .greenid-has-error .help-block, #greenid-container .greenid-has-error .control-label, #greenid-container .greenid-has-error .radio, #greenid-container .greenid-has-error .checkbox, #greenid-container .greenid-has-error .radio-inline, #greenid-container .greenid-has-error .checkbox-inline {
  color: #b50217;
}
#greenid-container .greenid-has-error .form-control {
  border-color: #b50217;
  border-width: 2px;
}
a:focus, a:focus-visible {
  outline: 4px solid rgba(0,125,250,0.6);
  outline-offset: 1px;
}
#greenid-container .greenid-u-reset:focus, #greenid-container .greenid-u-reset:active:focus, #greenid-container .greenid-u-reset.active:focus {
  box-shadow: 0 0 0 4px rgb(0 125 250 / 60%);
  outline: none;
}
a:focus {
  outline: 4px solid rgba(0,125,250,0.6);
  outline-offset: 1px;
}
:focus-visible {
    outline-color: #1589EE;
}
#greenid-container .form-control:focus {
    border-color: #1589EE;
    border-width: 2px;
    outline: 0;
}
#greenid-container a:focus {
  outline: 5px auto #1589EE;
}
#greenid-container input[type="file"]:focus, #greenid-container input[type="radio"]:focus, #greenid-container input[type="checkbox"]:focus {
  outline: 5px auto #1589EE;
}
#greenid-container #greenid-source-header #greenid-source-list-container #greenid-option-list .dropdown-menu .list-group-item a {
  font-size: 16px;
}
#greenid-container b, #greenid-container strong {
  font-weight: 600;;
}
#greenid-container .greenid-u-reset {
  background-color: #3B4E98;
  border-color: #3B4E98;
  font-size: 14px;
  text-transform: capitalize;
}
#greenid-container #greenid-source-content #greenid-medicaredvs-multiline-name-toggle {
  text-decoration: underline;
}
.dd-desc {
  color: #3B4E98;
}
.slds-scope:not(html), .slds-scope body {
  font-family: "Open Sans", sans-serif;
  color: #525252;
  font-size: 16px;
  line-height: 24px;
}
#greenid-container .greenid-alert {
  font-size: 14px;
  line-height: 21px;
  border-radius: 0px;
}
#greenid-container .greenid-alert-danger {
    background-color: #C23935;
    border-color: #C23935;
    color: white;
}
#greenid-container .greenid-alert-glyphicon {
  font-size: 21px;
}
#greenid-container .greenid-alert.alert-dismissable .close {
  color: white;
  opacity: 1;
  margin-top: 0.2em;
}
#greenid-container .input-group-addon{
  cursor: pointer;
}

#greenid-container #greenid-source-header #greenid-source-list-container{
  display: block;
}

.slds-box {
  padding: var(--lwc-spacingMedium,1rem);
  border-radius: var(--lwc-borderRadiusMedium,0.25rem);
  border: none;
}