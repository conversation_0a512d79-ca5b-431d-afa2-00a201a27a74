.content__body-dashboard p{
    max-width: 37em;
}

.joint-account-sharing{
    padding-top: 40px;
} 

.joint-account-sharing_desc{
    float: left;
}

.back-button{
    padding-top: 20px;
    padding-bottom: 30px;
}


.add-left
{
   padding-left: 20px;
}

.add-top{
    margin-top: -20px;
}

.clsLegend {    
    padding-left: 2px;
    padding-right: 2px;
    border: none;
    display: block;
 }

 .margintop{
    margin-top: 20px;
 }

 .acc-bsb-label{
    letter-spacing: 0px;
    white-space: nowrap;
 }

 .fin-acct-details {
    padding-left: 30px;
    padding-right: 0px;
}

.absolute{
    position: absolute;
    margin-top: -37px;
}

.header_left{
    padding-left: 50px;
}


@media only screen and (max-width: 1199px) {

    .sbutton{
               margin-right: 10px;
    }
}

@media only screen and (max-width: 767px) {
    .header_left{
        padding-left: 27px;
    }
    
    .absolute{
        margin-top: -60px;
    }

   
  }

.error-icon {
    width: 40px;
    height: 40px;
}