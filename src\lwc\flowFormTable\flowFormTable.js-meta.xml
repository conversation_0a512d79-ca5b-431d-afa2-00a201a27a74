<?xml version="1.0" encoding="UTF-8"?>  
<LightningComponentBundle xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>52.0</apiVersion>
    <isExposed>true</isExposed>
    <targets>
        <target>lightning__AppPage</target>
        <target>lightning__FlowScreen</target>
    </targets>
    
    
  
    <targetConfigs>
        <targetConfig targets="lightning__FlowScreen">
            <property name="isEditMode" type="Boolean" required="true" label="(Input) Is in edit mode"/>
            <property name="useDefaultEmptyRow" type="Boolean" required="true" label="Use default empty row" default="false"/>
            <property name="showTotalPercentage" type="Boolean" required="false" label="(Input) Total is visible" default="true"/><!-- //SFP-59427, burhan change "(Input) Show Total Percentage" to "(Input) Total is visible" -->
            <property name="inputColumns" type="String" required="true" label="(Input) Columns JSON"/>
            <property name="inputRowKeys" type="String" required="true" label="(Input) Data Keys CSV" default=""/>
            <property name="inputPicklistOptions" type="String" required="false" label="(Input) Picklist Options JSON"/>
            <property name="defaultValuesForNewRow" type="String" required="false" label="(Input) Default values for new row JSON"/>
            <property name="btnAddLabel" type="String" required="false" label="(Input) Add button label"/>
            <property name="btnDeleteLabel" type="String" required="false" label="(Input) Delete button label"/>
            <property name="rowHeaderLabel" type="String" required="false" label="(Input) Row Header Label"/>
            <property name="fieldForPercentage" type="String" required="false" label="(Input) Total field to compute"/><!-- //SFP-59427, burhan change "(Input) Field to compute for percentage" to "(Input) Total field to compute"-->
            <property name="dateFieldToFormat" type="String" required="false" label="(Input) Date field to format"/>
            <property name="totalPercentageTooltip" type="String" required="false" label="(Input) Total tooltip"/><!-- //SFP-59427, burhan change "(Input) Total percentage tooltip" to "(Input) Total tooltip"-->
            <property name="totalPercentage" type="Integer" required="false" label="(Output) Total"/><!-- //SFP-59427, burhan change "(Output) Total percentage" to "(Output) Total"-->
			<property name="hasMandatoryFieldsErrorMsg" type="Boolean" required="false" label="(Output) Has mandatory field error message"/>
			<property name="totalRowCount" type="Integer" required="false" label="(Input) Max row count"/><!-- //SFP-59427, burhan change "(Input) Total new row count" to "(Input) Max row count"-->

			<property name="estateText" type="String" required="false" label="(Input) Estate text"/>
            <property name="estateSubText" type="String" required="false" label="(Input) Estate sub text"/>
			<property name="estateToolTip" type="String" required="false" label="(Input) Estate tooltip"/>
			<property name="estatePercentage" type="Integer" required="false" label="Estate Percentage"/>
			<property name="benefitProportionLabel" type="String" required="false" label="(Input) Benefit proportion text"/>
			<property name="benefitProportionTooltip" type="String" required="false" label="(Input) Benefit proportion tooltip"/>
			<property name="showRowBenefitPortionErrorMsg" type="Boolean" required="false" label="(Input) Benefit proportion row show error"/><!-- //SFP-59427, burhan -->

			<!-- //SFP-56277, burhan -->
            <property name="inputRowValue" type="String" required="true" label="Data Values CSV" default=""/>
			<!-- //SFP-57496, burhan -->
			<property name="getCurrentRowCount" type="Integer" required="false" label="(Output) Get current row count"/>
			<!-- //SFP-59427, burhan -->
			<property name="totalSetup" type="String" required="false" label="(Input) Total setup JSON"/> 
			<property name="deleteModalText" type="String" required="false" label="(Input) Delete modal text JSON"/>
			<property name="tableDelimiter" type="String" required="false" label="(Input) Table delimiter JSON"/>
			<property name="maxRowErrorMsg" type="String" required="false" label="(Input) Max row error message"/>
			<property name="columnFieldSetup" type="String" required="false" label="(Input) Column field setup JSON"/>
            <property name="headerSetup" type="String" required="false" label="(Input) Row Header setup JSON"/>

            <property name="inputSecondRowValue" type="String" required="false" label="Data SecondValues CSV" default=""/>
        </targetConfig>
    </targetConfigs>
</LightningComponentBundle>