/** 
 * Created by Nutan Chougule, Added as part of consent History requriments. SFP-78754. 
 */
import { LightningElement, track, wire, api } from 'lwc';
import { loadStyle } from "lightning/platformResourceLoader";
//For navigation
import { NavigationMixin } from 'lightning/navigation';
// CSS from static resource
import cdrCss from '@salesforce/resourceUrl/CDR_Styles';
import CDR_LOGO from '@salesforce/resourceUrl/CDR_Logo';
import CHECK_LOGO from '@salesforce/resourceUrl/check_logo';
import WARNING_LOGO from '@salesforce/resourceUrl/warning_logo';
// Toast events
import {ShowToastEvent} from "lightning/platformShowToastEvent"
// Apex controller
import getScopeRecordsWithDataShare from '@salesforce/apex/CdrLandingDashboardController.getScopeRecordsWithDataShare';
// Custom labels
import CDR_ACCREDITATION_LINK from '@salesforce/label/c.CDR_Accreditation_Link';
/**
 * New changes from here
 */

export default class cdrDataSharingArrangement extends NavigationMixin(LightningElement) {
    @api recordId;
    @track parameters = {};
    @track dataClusterToPermLangMap = [];
    status;
    errorMessage;
    recipientLogo;
    recipientStatus;
    recipientEntityId;
    softwareProductName;
    softwareProductStatus;
    isRecordFound = false;
    legend_color;
    revocationDate;
    consentOwner;
    loaded = false;
    expired = false;
    withdrawn = false;
    active = false;
    onceoff = false;

    @track isAmended = false;
    @track isKeyDatesAmended = false;
    @track scopeRecords = [];
    @track currentScopeRecord = {};
    previousScopeRecord;
    

    constructor() {
        super();
        Promise.all([
            loadStyle(this, cdrCss)
        ]).then(() => {
            // console.log("Loaded css");
        }).catch(error => {
            console.error('Error loading static resources', error);
        });
    }
    svgURL = `${CHECK_LOGO}#check_logo`;

    connectedCallback(){
        // Get all scoped records to compare the amended
        this.parameters = this.getQueryParameters();
        this.fetchScopeRecordsWithDataShare(this.parameters.arrangement, this.parameters.scopeId);
    }

    // fetch all scope records 
    fetchScopeRecordsWithDataShare(arrangementId, currentScopeId) {
        getScopeRecordsWithDataShare({ consentArrangementId: arrangementId, currentScopeId: currentScopeId})
            .then(result => {
                let scopeList = this.bindScopeRecordDetails(result);
                this.findCurrentAndPreviousScopeRecord(scopeList);
                this.findScopeAmended();
                this.loaded = true;
            })
            .catch(error => {
                console.error('Error fetching records:', error);
            });
    }
   
    bindScopeRecordDetails(scopeData){
        let scopeWithDataList = [];
        scopeData.forEach((wrapper) => {
            if (wrapper.isCurrentScope || wrapper.isPreviousScope){
                let scopeWithData = {};
                scopeWithData.scopeRecord = {};
                scopeWithData.sharedScopeDetailsMap = {};
                let scopeRecord = wrapper.scopeWithParent;
                let updatedScopeRecord = {};
                updatedScopeRecord.id = scopeRecord.Id;
                //updatedScopeRecord.startDate = this.formatDate(new Date(scopeRecord.Start_Date__c));
                //updatedScopeRecord.endDate = this.formatDate(new Date(scopeRecord.End_Date__c));
                //updatedScopeRecord.originalEndDate = this.formatDate(new Date(scopeRecord.Original_Scope_End_Date__c));

                updatedScopeRecord.startDate = scopeRecord.Start_Date__c ? this.formatDate(new Date(scopeRecord.Start_Date__c)) : '';
                updatedScopeRecord.endDate = scopeRecord.End_Date__c ? this.formatDate(new Date(scopeRecord.End_Date__c)) : '';
                updatedScopeRecord.originalEndDate = scopeRecord.Original_Scope_End_Date__c? this.formatDate(new Date(scopeRecord.Original_Scope_End_Date__c)) : '';
                updatedScopeRecord.dataShared = scopeRecord.Data_Shared__c;

                let consentRecord = scopeRecord.Consent_Arrangement_Record__r;
                if (consentRecord) {
                    updatedScopeRecord.consent = {};
                    //updatedScopeRecord.consent.startDate = this.formatDate(new Date(consentRecord.Start_Date__c));
                    //updatedScopeRecord.consent.endDate = this.formatDate(new Date(consentRecord.End_Date__c));
                    updatedScopeRecord.consent.startDate = consentRecord.Start_Date__c? this.formatDate(new Date(consentRecord.Start_Date__c)) : '';
                    updatedScopeRecord.consent.endDate = consentRecord.End_Date__c? this.formatDate(new Date(consentRecord.End_Date__c)) : '';
                    updatedScopeRecord.consent.softwareProductStatus = consentRecord.Data_Recipient_Software_Product_Status__c;
                    updatedScopeRecord.consent.consentOwner = consentRecord.CreatedBy.FirstName + ' ' + consentRecord.CreatedBy.LastName;
                    updatedScopeRecord.consent.revocationDate = consentRecord.Revocation_Date__c? this.formatDate(new Date(consentRecord.Revocation_Date__c)) : '';
                    
                 
                    let status = consentRecord.Status__c;
                    let legend_color = 'status-style_border ';                  
                    switch(status){
                        case 'Active':
                            legend_color += 'status-style_green-color';
                            this.active = true;
                            break;
                        case 'Expired':
                            legend_color += 'status-style_grey-color';
                            this.expired = true;
                            break;
                        case 'Withdrawn':
                            legend_color += 'status-style_grey-color';
                            this.withdrawn = true;
                            break;
                        case 'Once-Off':
                            legend_color += 'status-style_grey-color';
                            this.onceoff = true;
                            break;
                    }
                    updatedScopeRecord.consent.legend_color = legend_color;
                    updatedScopeRecord.consent.status = status;

                    let softwareProduct = consentRecord.Data_Recipient_Software_Product__r;
                    if(softwareProduct){
                        if(softwareProduct.Software_Product_Name__c){
                            updatedScopeRecord.consent.softwareProductName = softwareProduct.Software_Product_Name__c;
                        }
                        if(softwareProduct.Data_Recipient_Brand__r 
                                && softwareProduct.Data_Recipient_Brand__r.Data_Recipient__r){
                            let recipient = softwareProduct.Data_Recipient_Brand__r.Data_Recipient__r;
                            if(recipient.Legal_Name__c){
                                updatedScopeRecord.consent.recipientName = recipient.Legal_Name__c;
                            }
                            if(recipient.Logo_URI__c){
                                updatedScopeRecord.consent.recipientLogo = softwareProduct.Data_Recipient_Brand__r.Data_Recipient__r.Logo_URI__c;
                            }
                            if(recipient.Status__c){
                                updatedScopeRecord.consent.recipientStatus = recipient.Status__c;
                            }
                            if(recipient.Legal_Entity_ID__c){
                                updatedScopeRecord.consent.recipientEntityId = recipient.Legal_Entity_ID__c;
                            }
                        }
                    }
                }
                scopeWithData.isCurrentScope = wrapper.isCurrentScope;
                scopeWithData.isKeyDatesAmended = !wrapper.isLastScope;
                scopeWithData.isPreviousScope = wrapper.isPreviousScope;
                scopeWithData.scopeRecord = JSON.parse(JSON.stringify(updatedScopeRecord));
                scopeWithData.sharedScopeDetailsMap = wrapper.sharedScopeDetailsMap;
                scopeWithData.footnoteBody = wrapper.footnoteBody;
                scopeWithData.footnoteHeader = wrapper.footnoteHeader;
                scopeWithData.scopeToAuthSelReqMap = wrapper.scopeToAuthSelReqMap;
                scopeWithDataList.push(scopeWithData);
            }
        });
        this.scopeRecords = scopeWithDataList;
        return scopeWithDataList;
    }

    findCurrentAndPreviousScopeRecord(scopeWithDataList){
        if (scopeWithDataList) {
            scopeWithDataList.forEach((wrap) => {
                let scope = wrap;
                if (scope.isCurrentScope) {
                    this.currentScopeRecord = scope.scopeRecord;
                    
                    let dataRequest = scope.sharedScopeDetailsMap;
                    let footnoteHeader = scope.footnoteHeader;
                    let footnoteBody = scope.footnoteBody;
                    // get data cluster details
                    for(let key in scope.sharedScopeDetailsMap){
                        for(let scopeKey in scope.scopeToAuthSelReqMap){
                            if(scopeKey === key && dataRequest.hasOwnProperty(key)){
                                this.dataClusterToPermLangMap.push({key: key, value: dataRequest[key], header: footnoteHeader[key], body:footnoteBody[key]});
                            }
                        }
                    }
                    this.isKeyDatesAmended = scope.isKeyDatesAmended;
                    this.isRecordFound = true;
                }else if (scope.isPreviousScope) {
                    this.previousScopeRecord = scope.scopeRecord;
                }
            });
        }
    }

    findScopeAmended(){
        if (this.currentScopeRecord && this.previousScopeRecord) {
            const dataSharedCurrent = this.currentScopeRecord?.dataShared ? this.currentScopeRecord.dataShared.split(';').map(item => item.trim().toLowerCase()).sort() : [];
            const dataSharedPrevious = this.previousScopeRecord?.dataShared ? this.previousScopeRecord.dataShared.split(';').map(item => item.trim().toLowerCase()).sort() : [];

            const currentJoined = dataSharedCurrent.join(';'); 
            const previousJoined = dataSharedPrevious.join(';');

            // Compare both arrays by converting them to strings
            this.isAmended = currentJoined !== previousJoined;
        }
        else
        this.isAmended =false;
    }

   
    getQueryParameters() {
        var params = {};
        var search = location.search.substring(1);
        if (search) {
            params = JSON.parse('{"' + search.replace(/&/g, '","').replace(/=/g, '":"') + '"}', (key, value) => {
                return key === "" ? value : decodeURIComponent(value)
            });
        }
        return params;
    }

    // Format Date
    formatDate(date) {
        if (!date) return '';
        const day = date.getDate();
        const month = date.toLocaleString('default', { month: 'long' });
        const year = date.getFullYear();
        return `${day} ${month} ${year}`;
    }

}