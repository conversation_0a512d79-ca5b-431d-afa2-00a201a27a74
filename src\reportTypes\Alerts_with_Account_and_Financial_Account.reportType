<?xml version="1.0" encoding="UTF-8"?>
<ReportType xmlns="http://soap.sforce.com/2006/04/metadata">
    <baseObject>FinServ__Alert__c</baseObject>
    <category>accounts</category>
    <deployed>true</deployed>
    <description>Report type to get alert records filtered by both account and financial account</description>
    <label>Alerts with Account and Financial Account</label>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Account__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Active__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Active__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Alert_Details__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Id</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>true</checkedByDefault>
            <field>Name</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Alert_Type__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>AMP_Product__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>API__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Applies_to_all_Financial_Accounts__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>AUM__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Category__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Claim__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>COB_Alert_c__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedBy</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedDate</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>End_Date__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>EX1_unique_ID__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__FinancialAccount__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Insurance_Policy__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>IsContactCentreAlert__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedBy</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedDate</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Message__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__MessageDescription__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Opportunity__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Owner</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Priority__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>RecordType</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Servicing_Practice__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Severity__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__SourceSystemId__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SourceSystemId__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Start_Date__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Status__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Stop_Notification__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Sub_Category__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>TECH_Bypass_Validation_Rule__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__FinancialAccount__c.Product_System_Code__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__FinancialAccount__c.Product_System__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__FinancialAccount__c.Product__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__FinancialAccount__c.Product__c.IsActive</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__FinancialAccount__c.Product__c.CreatedDate</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__FinancialAccount__c.Product__c.ProductCode</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__FinancialAccount__c.Product__c.Description</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Account__c.AccountNumber</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Account__c.Account_Owner__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Account__c.Owner</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Account__c.Party_Type__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Account__c.Has_Active_AMP_Product__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Account__c.Has_AMP_Product__c</field>
            <table>FinServ__Alert__c</table>
        </columns>
        <masterLabel>Alerts</masterLabel>
    </sections>
</ReportType>
