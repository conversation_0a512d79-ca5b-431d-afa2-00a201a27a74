<!--
	SFP-62603, burhan
-->
<apex:page showHeader="false" controller="PreChatCaptchaController">
	<!-- This script takes the endpoint URL parameter passed from the deployment page and makes it the action for the form -->
	<style type="text/css">
		*, *::before, *::after {
			box-sizing: inherit;
		}

		#waitingMessage {
			height: 100%;
			width: 100%;
			vertical-align: middle;
			background-color: #ffffff;
			position: absolute;
			top: 0;
			align-items: center;
			justify-content: center;
			z-index: 10000;
		}
	</style>

	<!-- Form that gathers information from the chat visitor and sets the values to Chat Custom Details used later in the example -->
	<div id="waitingMessage" cellpadding="0" cellspacing="0" style="display: none">
		<div>
			<!-- <img src="https://www.amp.com.au/etc/designs/amp/assets/img/forms/loading_transparent.gif" alt="Refreshing" class="amp-table-transactions__refreshing" /> -->
			<img src="{!$Resource.loading_transparent}" alt="Refreshing" class="amp-table-transactions__refreshing" />
			<p>
				<strong>Please wait</strong>
			</p>
			<p>You're being connected to an available agent.</p>
		</div>
	</div>
	<div>
		<apex:outputPanel id="chatformSubmissionContainer">
			<form method='post' id='prechatForm'>
				<input id="firstName" type="hidden" name="liveagent.prechat:FirstName" />
				<input id="lastName" type="hidden" name="liveagent.prechat:LastName" />
				<input id="email" type="hidden" name="liveagent.prechat:Email" />

				<!-- Hidden fields used to set Preform Fields on LiveChatTranscript -->
				<input type="hidden" name="liveagent.prechat.save:FirstName" value="First_Name__c" />
				<input type="hidden" name="liveagent.prechat.save:LastName" value="Last_Name__c" />
				<input type="hidden" name="liveagent.prechat.save:Email" value="Email__c" />
				<input type="hidden" id="isLoggedInHidden" name="liveagent.prechat:isLoggedIn" value="isLoggedIn__c" />
				<input type="hidden" name="liveagent.prechat.save:isLoggedIn" value="isLoggedIn__c" />
				<input type="hidden" id="authUser" name="liveagent.prechat:user" value="isLoggedIn__c" />
				<input type="hidden" name="liveagent.prechat.save:user" value="Authenticated_User__c" />

				<!-- Hidden Fields for Chat Bot / SFP-56246 by Jeffry G.-->
				<input type="hidden" name="liveagent.prechat:formName" id="formName" />
				<input type="hidden" name="liveagent.prechat.save:formName" value="param_formName__c" />
				<input type="hidden" name="liveagent.prechat:contractID" id="contractID" />
				<input type="hidden" name="liveagent.prechat.save:contractID" value="param_contractID__c" />

				<!-- Hidden fields used to set additional custom details -->
				<input type="hidden" name="liveagent.prechat:CaseStatus" value="New" />

				<!-- This example assumes that "Chat" was added as picklist value to the Case Origin field -->
				<input type="hidden" name="liveagent.prechat:CaseOrigin" value="Webchat" />

				<!-- This example assumes that "Chat" was added as picklist value to the Case Origin field -->
				<input type="hidden" name="liveagent.prechat:CaseType" value="Click to Chat" />

				<!-- This example will set the Case Record Type to a specific value for the record type configured on the org. Lookup the case record type's id on your org and set it here -->
				<input type="hidden" name="liveagent.prechat:CaseRecordType" value="0122P0000004IObQAM" />

				<!-- Used to set the visitor's name for the agent in the Console -->
				<input type="hidden" name="liveagent.prechat.name" id="prechat_field_name" />

				<!-- map: Use the data from prechat form to map it to the Salesforce record's fields -->
				<input type="hidden" name="liveagent.prechat.findorcreate.map:Contact" value="Email,Email" />

				<input type="hidden" name="liveagent.prechat.findorcreate.map:Case" value="Subject,CaseSubject;Status,CaseStatus;Origin,CaseOrigin;Type,CaseType;RecordTypeId,CaseRecordType;First_Name__c,FirstName;Last_Name__c,LastName;REM_Bussiness_Email__c,Email" />

				<!-- doFind, doCreate and isExactMatch example for a Contact:
				Find a contact whose Email exactly matches the value provided by the customer in the form
				If there's no match, then create a Contact record and set it's First Name, Last Name, Email, and Phone to the values provided by the customer -->
				<input type="hidden" name="liveagent.prechat.findorcreate.map.doFind:Contact" value="Email,true" />
				<input type="hidden" name="liveagent.prechat.findorcreate.map.isExactMatch:Contact" value="Email,true" />

				<!-- doCreate example for a Case: create a case to attach to the chat, set the Case Subject to the value provided by the customer and set the case's Status and Origin fields -->
				<input type="hidden" name="liveagent.prechat.findorcreate.map.doCreate:Case" value="Subject,true;Status,true;Origin,true;Type,true;RecordTypeId,true;First_Name__c,true;Last_Name__c,true;REM_Bussiness_Email__c,true" />

				<!-- linkToEntity: Set the record Contact record, found/created above, as the Contact on the Case that's created -->
				<input type="hidden" name="liveagent.prechat.findorcreate.linkToEntity:Contact" value="Case,ContactId" />

				<!-- saveToTranscript: Associates the records found / created, i.e. Contact and Case, to the Live Chat Transcript record. -->
				<input type="hidden" name="liveagent.prechat.findorcreate.saveToTranscript:Case" value="CaseId" />
				<input type="hidden" name="liveagent.prechat.findorcreate.saveToTranscript:Contact" value="ContactId" />

				<!-- displayToAgent: Hides the case record type from the agent -->
				<input type="hidden" name="liveagent.prechat.findorcreate.displayToAgent:CaseRecordType" value="false" />

				<!-- searchKnowledge: Searches knowledge article based on the text, this assumes that Knowledge is setup -->
				<input type="hidden" name="liveagent.prechat.knowledgeSearch:CaseSubject" value="true" />
			</form>
		</apex:outputPanel>
	</div>

	<!-- SFP-56246 by Jeffry G. -->
	<script type='text/javascript' src='{!$Setup.Chat_Button_Settings__c.PreChat_URL__c}'></script>
	<script type="text/javascript">
		function getQueryParameters() {
			var params = {};
			var search = location.search.substring(1);
			if (search) {
				params = JSON.parse('{"' + search.replace(/&/g, '","').replace(/=/g, '":"') + '"}', (key, value) => {
					return key === "" ? value : decodeURIComponent(value)
				});
			}
			return params;
		}

		var detailCallback = function (details) {
			if (details.customDetails.length > 0) {
				if (details.customDetails[0].label == 'user') {
					controllerValidation(details.customDetails[0].value);
				}
			}

			let hasFirstName = false;
			let hasLastName = false;
			let hasEmail = false;
			let hasAuthUser = false;
			let isLoggedIn = false;

			for (var i = 0; i < details.customDetails.length; i++) {
				if (details.customDetails[i].label == 'formName') {
					document.getElementById('formName').value = details.customDetails[i].value;
				}

				if (details.customDetails[i].label == 'contractID') {
					document.getElementById('contractID').value = details.customDetails[i].value;
				}

				if (details.customDetails[i].label == 'firstName') {
					// console.log('bn firstName', details.customDetails[i].value);
					hasFirstName = true;
					document.getElementById('firstName').value = details.customDetails[i].value;
				}
				if (details.customDetails[i].label == 'lastName') {
					// console.log('bn lastName', details.customDetails[i].value);
					hasLastName = true;
					document.getElementById('lastName').value = details.customDetails[i].value;
				}
				if (details.customDetails[i].label == 'email') {
					// console.log('bn email', details.customDetails[i].value);
					hasEmail = true;
					document.getElementById('email').value = details.customDetails[i].value;
				}
				if (details.customDetails[i].label == 'isLoggedIn') {
					// console.log('bn isLoggedIn', details.customDetails[i].value);
					isLoggedIn = details.customDetails[i].value;
					document.getElementById('isLoggedInHidden').value = details.customDetails[i].value;
				}
				if (details.customDetails[i].label == 'authUser') {
					// console.log('bn authUser', details.customDetails[i].value);
					hasAuthUser = true;
					document.getElementById('authUser').value = details.customDetails[i].value;
				}
			}

			if (hasFirstName && hasLastName && hasEmail && hasAuthUser && isLoggedIn) {
				reCaptchaStatus = 'COMPLETED';

				// Set the visitor's name for the agent in the Console to first and last name provided by the customer
				document.getElementById("prechat_field_name").value = document.getElementById("firstName").value + " " + document.getElementById("lastName").value;
				
				let parameters = getQueryParameters();
				let form = document.getElementById("prechatForm");
				form.setAttribute('action', decodeURIComponent(parameters['endpoint']));
				document.getElementById("prechatForm").submit();
			}
		};
		//First parameter is Chat URL. This is same as generated in Live Chat deployment code and can be used here
		//This might be different for different

		//For example, in my Live chat deployment, the chat URL was 'https://d.la1c1.salesforceliveagent.com/chat' as defined in liveagent.init('https://d.la1c1.salesforceliveagent.com/chat', '5724000000000XX', '00De000000XXXXX');
		liveagent.details.preChatInit('{!$Setup.Chat_Button_Settings__c.Deployment_URL__c}', 'detailCallback');
	</script>
</apex:page>