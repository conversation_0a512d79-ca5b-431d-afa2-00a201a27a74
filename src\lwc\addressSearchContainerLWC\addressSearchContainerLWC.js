// SFP-51400, burhan   

import { LightningElement, api, track } from 'lwc';
import {FlowAttributeChangeEvent, FlowNavigationNextEvent} from 'lightning/flowSupport';
import fetchCountriesList from '@salesforce/apex/WebCaseController.fetchCountriesList';
import getStateList from '@salesforce/apex/WebCaseController.getStateList';

// const inputFilteredTextArr = ['PO BOX','P.O.BOX','GPO BOX','GP.O.BOX'];//SFP-60408, burhan; SFP-60802, commented-out
const inputFilteredTextArr = ['PO BOX','P.O. BOX','GPO BOX','G.P.O. BOX'];//SFP-60802, burhan

export default class addressSearchContainerLWC extends LightningElement {

	@track address = ''
	@track countryList = []
	addressRequest = {}
	@track suburb
	@track street
	@track postcode
	@track city
	@track state
	@track stateNonAus
	@track country = 'Australia'
	@track addressManually = false
	hasInvalidInput = false;//SFP-60408, burhan
	isEmptyAddress = false; //SFP-60672, burhan

	stateList = [
		{ label: 'ACT', value: 'ACT' },
		{ label: 'NSW', value: 'NSW' },
		{ label: 'NT', value: 'Northan Territory' },
		{ label: 'QLD', value: 'Queensland' },
		{ label: 'SA', value: 'South Australia' },
		{ label: 'TAS', value: 'Tasmania' },
		{ label: 'VIC', value: 'VIC' },
		{ label: 'WA', value: 'Western Australia' },
	];

	@api
	get txtAddressManually(){
		return this.addressManually;
	}

	set txtAddressManually(value){
		this.addressManually = value;
	}

	@api
	get txtAddress(){
		return this.address;
	}

	set txtAddress(value){
		this.address = value;
	}

	@api
	get txtLocality(){
		return this.addressManually ? this.suburb : this.addressRequest.locality;
	}

	set txtLocality(value){
		this.suburb = value;
		this.addressRequest.locality = value;
	}

	@api
	get txtAddressLine1(){
		return this.addressManually ? this.street : this.addressRequest.addressLine1;
	}

	set txtAddressLine1(value){
		this.street = value;
		this.addressRequest.addressLine1 = value;
	}

	@api
	get txtAddressLine2(){
		return this.addressManually ? '' : this.addressRequest.addressLine2;
	}

	set txtAddressLine2(value){
		this.addressRequest.addressLine2 = value;
	}

	@api
	get txtAddressLine3(){
		return this.addressManually ? '' : this.addressRequest.addressLine3;
	}

	set txtAddressLine3(value){
		this.addressRequest.addressLine3 = value;
	}

	@api
	get txtPostalCode(){
		return this.addressManually ? this.postcode : this.addressRequest.postalCode;
	}

	set txtPostalCode(value){
		this.postcode = value;
		this.addressRequest.postalCode = value;
	}

	@api
	get txtProvince(){
		return this.addressManually ? (this.state || this.stateNonAus) : this.addressRequest.province;
	}

	set txtProvince(value){
		this.state = value;
		this.stateNonAus = value;
		this.addressRequest.province = value;
	}

	@api
	get txtCity(){
		return this.addressManually ? (this.city || '') : (this.addressRequest.city || '');
	}

	set txtCity(value){
		this.city = value;
		this.addressRequest.city = value;
	}

	@api
	get txtCountry(){
		return this.addressManually ? this.country : this.addressRequest.country;
	}

	set txtCountry(value){
		this.country = value;
		this.addressRequest.country = value;
	}

	get countrySelection() {
		const isAustralia = this.country === 'Australia';
		return isAustralia
	}

	get manualAddressFieldsRequired() {
		return this.countrySelection ? true : false
	}

	connectedCallback(){
		this.isEmptyAddress = sessionStorage.getItem('addressSearchContainerLWC.isEmptyAddress');//SFP-60672, burhan
		// console.log('bn stateList1',this.stateList);
		getStateList().then(response => {
			// console.log(response);
			// this.stateList = JSON.parse(response);
			if(response){
				this.stateList = response;
				// console.log('bn stateList2',this.stateList);
			}
		});

		//retrigger apex call for countryList
		if(this.addressManually) {
			this.populateCountryList();
		}


	}
	//SFP-63999, Erik Padua - Added this method to show the validation message after clicking 'Next'
	renderedCallback(){ 	
		let trueAddress = sessionStorage.getItem('addressSearchContainerLWC.isEmptyAddress');
		if(trueAddress){
			if(this.addressManually){
				if(this.isEmptyAddress === "true"){
					const customeInputs = this.template.querySelectorAll('c-ccd-form-input-l-w-c');
					if(customeInputs){
						customeInputs.forEach((a)=> a.showValidity());
						//console.log('***TEST2');
					}
					const customeCombo = this.template.querySelectorAll('lightning-combobox');
					if(customeCombo){
						customeCombo.forEach((a)=> a.reportValidity());
						//console.log('***TEST2');
					}
				}
			}
			else if(this.isEmptyAddress === false){					
				const addressInput = this.template.querySelectorAll('c-ccd-form-input-l-w-c[data-input-name=address]');
				if(addressInput){
					addressInput.forEach((a)=> a.showValidity());
					//console.log('***TEST2');
				}
			}
		}
	}

	handleaddressSearchResult(e) {
		this.addressRequest = e.detail.addressDetail;
		this.address = e.detail.fullAddress;
	}
	
	handleAddressManually() {
		this.addressManually = !this.addressManually
		this.address = ''
		if(this.addressManually) {
			this.populateCountryList();
		}
	}

	populateCountryList(){
		fetchCountriesList()
		.then(response => (this.countryList = response.sort((a,b) => {
		if (a.label < b.label) {
			return -1
		}
		if (a.label > b.label) {
			return 1
		}
		return 0
		})))
		.catch(e => (console.log(e)))
	}

	addressValidation(e) {
		// SFP-60408, burhan
		let val = e.detail.value;
		this.hasInvalidInput = false;//SFP-60802, burhan
		for(let i = 0; i < inputFilteredTextArr.length; i++){
			if(val && val.toUpperCase().includes(inputFilteredTextArr[i])){
				//console.log('bn addressValidation found', inputFilteredTextArr[i]);
				this.hasInvalidInput = true;
				// this.template.querySelector('c-address-search-l-w-c').stopSearch();
				//return;
				break;
			}
		}
		//SFP-60802, burhan, commented-out
		// this.address = e.detail.value
		// this.template.querySelector('c-address-search-l-w-c').callAddressValidationApi()

		//SFP-60802, burhan
		this.address = val;
		if(!this.hasInvalidInput){
			this.template.querySelector('c-address-search-l-w-c').callAddressValidationApi()
		}
	}

	handleAddressValueCheck() {
		if(!this.addressRequest) {
			this.address = ''
		}
	}

	handleWrapperClick(event) {
		this.template.querySelector('c-address-search-l-w-c').handleClickOut(event)
	}

	handleChange(e) {
		if (e.detail) {
			this[e.detail.name] = e.detail.value
		}
		if (e.target.name) {
			this[e.target.name] = e.target.value
		}
	}

	//SFP-53116, burhan
	@api
	validate() {
		this.isEmptyAddress = false;//SFP-60672, burhan
		let isInputFiltered = false;//SFP-60408, burhan
		let isStateBlank = false; //SFP-63999, Erik Padua
		const formState = []
		//const customeInputs = this.template.querySelectorAll('c-ccd-form-input-l-w-c');
		let customeInputs = this.template.querySelectorAll('c-ccd-form-input-l-w-c');
		if(this.addressManually){
			const customeCombo = this.template.querySelectorAll('lightning-combobox');
			if(customeCombo){
				customeCombo.forEach((a)=>{
					if(!a.value){
						isStateBlank = true;
					}
				});
				//console.log('***TEST2');
			}
		}	
		sessionStorage.setItem('addressSearchContainerLWC.isEmptyAddress', false);//SFP-60672, burhan

		customeInputs.forEach(element => {
			// const check = element.isInputValid()//SFP-60408, burhan, commented-out
			let check = element.isInputValid()//SFP-60408, burhan
			
			//SFP-60408, burhan
			if(this.addressManually || this.address){
				// console.log('bn validate addressManually');
				let val = element.getInputValue();

				for(let i = 0; i < inputFilteredTextArr.length; i++){
					if(val && val.toUpperCase().includes(inputFilteredTextArr[i])){
						check = false;
						isInputFiltered = true;
						break;
					}
				}
				//SFP-63999, Erik Padua
				if(isStateBlank){ 
					check = false;
				}
			}else{
				if(this.hasInvalidInput){
					check = false;
					isInputFiltered = true;
					// this.hasInvalidInput//SFP-60802, burhan, commented-out
				}
			}

			formState.push(check)
		});

		this.isEmptyAddress = true;//SFP-60672, burhan
		sessionStorage.setItem('addressSearchContainerLWC.isEmptyAddress', true);//SFP-60672, burhan

		if(formState.every(x => Boolean(x))) {
			return { isValid: true }; 
		} 
		else { 
			// If the component is invalid, return the isValid parameter 
			// as false and return an error message. 
			return { 
				isValid: false, 
				// errorMessage: 'Complete this field.' //SFP-60408, burhan, commented-out
				errorMessage: isInputFiltered ? 'PO Box input is invalid!' : 'Complete this field.' //SFP-60408, burhan			
			}; 
		}
	}
}