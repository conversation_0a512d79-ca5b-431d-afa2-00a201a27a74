import { LightningElement, api, wire } from 'lwc';
import getPicklistOptions from '@salesforce/apex/SelfServicePicklistDataService.getPicklistOptions';

export default class SelfServicePicklistDualListBox extends LightningElement {
    @api recordId;
    @api fieldName;
    @api objectName;
    @api fieldLabel;
    @api picklistRT;
    @api picklistObject;
    @api picklistField;
    @api showNoneOption;

    _readOnly = false;
    _placeholderText;
    _picklistOptions = [];
    _initialPicklistValue;
    _picklistValue = [];
    _controllingRecordId;
    
    initPicklistVal='';
    initValueArr=[];
     
    @api
    get controllingRecordId(){
        return this._controllingRecordId;
    }
    set controllingRecordId(recId){
        this._controllingRecordId = recId;
    }

    @api
    get initialPicklistValue(){
        return this._initialPicklistValue;
    }
    set initialPicklistValue(_initialPicklistValue){
        if(_initialPicklistValue){
            this.initPicklistVal = _initialPicklistValue;
            if(this.initPicklistVal.split(';')){//Converts the String to an Array then push to the Inital value of Dual List Box
                this.initValueArr = this.initPicklistVal.split(';');
                for (let i = 0; i < this.initValueArr.length; i++){
                    this._picklistValue.push(this.initValueArr[i]);
                }
            }
            else{
                this._picklistValue.push(_initialPicklistValue);
            }
        }
        
    }

    @api 
    get readOnly(){
        return this._readOnly;
    }
    set readOnly(_readOnly){
        this._readOnly = _readOnly;
    }

    @api
    reset(){
        this._picklistValue = this._initialPicklistValue;
    }

    @api
    clear(){
        this._picklistOptions = [];
        this._picklistValue = [];
    }

    @api
    getValue(){
        return this._picklistValue;
    }
    get picklistValue() {
        return this._picklistValue;
    }

    @wire(getPicklistOptions, { fieldName: '$fieldName', objectName: '$objectName', controllingRecordId:'$_controllingRecordId' })
    handleGetPicklistOptions({ error, data }) {
        if (data) {
            if(Array.isArray(data)){
                this._picklistOptions =  data.filter(o => o.Controlling_Picklist__c === this._controllingRecordId).map(o => {return {label : o.Name, value: o.Name}});
            }

        } else if (error) {
            console.error(error);
        }
    }

    /** Handlers */
    _handleChange(event){
        this._picklistValue = event.detail.value;
    }
}