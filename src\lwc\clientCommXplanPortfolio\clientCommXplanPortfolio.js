/**
 * Created by jerry.poon on 18/06/2021.
 */ 

import {LightningElement, api} from 'lwc';
import getPortfolio from '@salesforce/apex/ClientCommXplanService.getXplanPortfolio';
import getPortfolioDetail from '@salesforce/apex/ClientCommXplanService.getXplanPortfolioDetail';

export default class ClientCommXplanPortfolio extends LightningElement {
    loaded = false;
    portfolio = [];
    portfolioDetails = [];
    tableData = [];
    portfolioDetailsMap = new Map();

    //datatable columns
    columns = [
        { label: 'Portfolio', fieldName: 'fullName', type: 'text', editable: false,
            cellAttributes: {class: {fieldName: 'format'}}
        },
        { label: 'Holding Name', fieldName: 'sec_name', type: 'text', editable: false,
            cellAttributes: {class: {fieldName: 'format'}}
        },
        { label: 'IRR (1 Year)', fieldName: 'irr_period1y', type:'percent', editable: false,
            typeAttributes: {
                minimumFractionDigits: '2',
                maximumFractionDigits: '2'
            },
            cellAttributes: {class: {fieldName: 'format'}}
        },
        { label: 'IRR (3 Year)', fieldName: 'irr_period3y', type:'percent', editable: false,
            typeAttributes: {
                minimumFractionDigits: '2',
                maximumFractionDigits: '2'
            },
            cellAttributes: {class: {fieldName: 'format'}}
        },
        { label: 'IRR (5 Year)', fieldName: 'irr_period5y', type:'percent', editable: false,
            typeAttributes: {
                minimumFractionDigits: '2',
                maximumFractionDigits: '2'
            },
            cellAttributes: {class: {fieldName: 'format'}}
        },
        { label: 'Market Value at End', fieldName: 'market_value_end', type: 'currency', editable: false,
            typeAttributes: {
                minimumFractionDigits: '4',
                maximumFractionDigits: '4'
            },
            cellAttributes: {class: {fieldName: 'format'}}
        }
    ];

    @api
    get loadSuccess() {
        return this.loaded && this.portfolio;
    }
    @api
    get loadFail() {
        return this.loaded && !this.portfolio;
    }

    connectedCallback() {
        getPortfolio()
            .then(response => {
                this.portfolio = this.processData(JSON.parse(response));
                if(this.portfolio) {
                    this.retrievePortfolioDetail(this.portfolio);
                } else {
                    this.loaded = true;
                }
            })
            .catch(error => {
                this.errorMsg = 'Error: ' + error.message;
                this.loaded = true;
            });
    }

    retrievePortfolioDetail(portfolioList) {
        getPortfolioDetail({portfolioListJSON : JSON.stringify(portfolioList)})
            .then(response => {
                this.portfolioDetails = this.processData(JSON.parse(response));
                if(this.portfolioDetails) {
                    this.portfolioDetails.forEach((detail) => {
                        if(!this.portfolioDetailsMap.get(detail.Parent_Xplan_Data__c)) {
                            this.portfolioDetailsMap.set(detail.Parent_Xplan_Data__c, []);
                        }
                        this.portfolioDetailsMap.get(detail.Parent_Xplan_Data__c).push(detail);
                    });
                }
                this.buildTable();
                this.loaded = true;
            })
            .catch(error => {
                this.errorMsg = 'Error: ' + error.message;
                this.loaded = true;
            });
    }

    //Build table based on portfolio and detial
    buildTable() {
        this.portfolio.forEach((portfolio) => {
            let row = {};
            row.format = 'slds-text-title_bold';
            row.fullName = portfolio.Portfolio__c;
            row.index = portfolio.External__c;
            row.market_value_end = portfolio.Market_Value_at_End__c;
            row.irr_period1y = portfolio.IRR_1_Year__c == null ? null : portfolio.IRR_1_Year__c/100;
            row.irr_period3y = portfolio.IRR_3_Year__c == null ? null : portfolio.IRR_3_Year__c/100;
            row.irr_period5y = portfolio.IRR_5_Year__c == null ? null : portfolio.IRR_5_Year__c/100;
            this.tableData.push(row);
            if(this.portfolioDetailsMap && this.portfolioDetailsMap.get(portfolio.Id)) {
                this.portfolioDetailsMap.get(portfolio.Id).forEach((portfolio) => {
                    let row = {};
                    row.index = portfolio.External__c;
                    row.market_value_end = portfolio.Market_Value_at_End__c;
                    row.sec_name = portfolio.Holding_Name__c;
                    this.tableData.push(row);
                });
            }
        });
    }

    processData(response) {
        if(response && response.isSuccess) {
            if(response.result) {
                return JSON.parse(response.result);
            } else {
                return [];
            }
        } else {
            this.errorMsg = response.errorMessage;
            this.loaded = true
        }
    }
}