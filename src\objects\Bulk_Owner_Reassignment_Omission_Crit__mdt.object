<?xml version="1.0" encoding="UTF-8"?> 
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <description>Stores exclusion criteria for bulk ownership reassignment, e.g., exclusion of Statuses from the record query for reassignment</description>
    <fields>
        <fullName>Field_API_Name__c</fullName>
        <description>API name of the field</description>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>Field API Name</label>
        <length>255</length>
        <required>false</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Field_Label__c</fullName>
        <description>Label of the field</description>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>Field Label</label>
        <length>255</length>
        <required>false</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Field_Values_for_Omission__c</fullName>
        <description>Field values for omission</description>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <inlineHelpText>Separated by semicolon (;)</inlineHelpText>
        <label>Field Values for Omission</label>
        <length>1000</length>
        <type>LongTextArea</type>
        <visibleLines>3</visibleLines>
    </fields>
    <fields>
        <fullName>Object_API_Name__c</fullName>
        <description>API name of the object</description>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>Object API Name</label>
        <length>255</length>
        <required>false</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <label>Bulk Owner Reassignment Omission Crit</label>
    <pluralLabel>Bulk Owner Reassignment Omission Crit</pluralLabel>
    <visibility>Public</visibility>
</CustomObject>
