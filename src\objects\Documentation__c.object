<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>Accept</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Accept</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Accept</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Documentation_Record_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Documentation_Record_Page</content>
        <formFactor>Small</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <type>Default</type>
    </actionOverrides>
    <allowInChatterGroups>false</allowInChatterGroups>
    <compactLayoutAssignment>SYSTEM</compactLayoutAssignment>
    <deploymentStatus>Deployed</deploymentStatus>
    <enableActivities>true</enableActivities>
    <enableBulkApi>true</enableBulkApi>
    <enableFeeds>true</enableFeeds>
    <enableHistory>true</enableHistory>
    <enableLicensing>false</enableLicensing>
    <enableReports>true</enableReports>
    <enableSearch>true</enableSearch>
    <enableSharing>true</enableSharing>
    <enableStreamingApi>true</enableStreamingApi>
    <externalSharingModel>Private</externalSharingModel>
    <fields>
        <fullName>Document_Type__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Document Type</label>
        <length>255</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>File_Name__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>File Name</label>
        <length>150</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>File_Size__c</fullName>
        <externalId>false</externalId>
        <label>File Size</label>
        <precision>18</precision>
        <required>false</required>
        <scale>0</scale>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Number</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>File_Type__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>File Type</label>
        <length>255</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Is_Active__c</fullName>
        <defaultValue>true</defaultValue>
        <externalId>false</externalId>
        <label>Is Active</label>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>Is_Transferred__c</fullName>
        <defaultValue>false</defaultValue>
        <externalId>false</externalId>
        <label>Is Transferred</label>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>Related_Object_Name__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Related Object Name</label>
        <length>255</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Related_Record_Id__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Related Record Id</label>
        <length>18</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Relates_to_Calender_Year__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Relates to (Calender Year)</label>
        <length>4</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Relates_to_Month__c</fullName>
        <externalId>false</externalId>
        <label>Relates to (Month)</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Picklist</type>
        <valueSet>
            <restricted>true</restricted>
            <valueSetDefinition>
                <sorted>false</sorted>
                <value>
                    <fullName>January</fullName>
                    <default>false</default>
                    <label>January</label>
                </value>
                <value>
                    <fullName>February</fullName>
                    <default>false</default>
                    <label>February</label>
                </value>
                <value>
                    <fullName>March</fullName>
                    <default>false</default>
                    <label>March</label>
                </value>
                <value>
                    <fullName>April</fullName>
                    <default>false</default>
                    <label>April</label>
                </value>
                <value>
                    <fullName>May</fullName>
                    <default>false</default>
                    <label>May</label>
                </value>
                <value>
                    <fullName>June</fullName>
                    <default>false</default>
                    <label>June</label>
                </value>
                <value>
                    <fullName>July</fullName>
                    <default>false</default>
                    <label>July</label>
                </value>
                <value>
                    <fullName>August</fullName>
                    <default>false</default>
                    <label>August</label>
                </value>
                <value>
                    <fullName>September</fullName>
                    <default>false</default>
                    <label>September</label>
                </value>
                <value>
                    <fullName>October</fullName>
                    <default>false</default>
                    <label>October</label>
                </value>
                <value>
                    <fullName>November</fullName>
                    <default>false</default>
                    <label>November</label>
                </value>
                <value>
                    <fullName>December</fullName>
                    <default>false</default>
                    <label>December</label>
                </value>
            </valueSetDefinition>
        </valueSet>
    </fields>
    <fields>
        <fullName>Remote_Upload_Path__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Remote Upload Path</label>
        <length>255</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Restricted_Practice__c</fullName>
        <defaultValue>false</defaultValue>
        <externalId>false</externalId>
        <label>Restricted - Practice</label>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>Servicing_Practice__c</fullName>
        <deleteConstraint>SetNull</deleteConstraint>
        <externalId>false</externalId>
        <label>Servicing Practice</label>
        <lookupFilter>
            <active>true</active>
            <filterItems>
                <field>Account.RecordTypeId</field>
                <operation>equals</operation>
                <value>Practice</value>
            </filterItems>
            <isOptional>false</isOptional>
        </lookupFilter>
        <referenceTo>Account</referenceTo>
        <relationshipLabel>Documentation</relationshipLabel>
        <relationshipName>Documentation</relationshipName>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>Sharepoint_File_Id__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Sharepoint File Id</label>
        <length>255</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Transfer_Date__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Transfer Date</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <trackTrending>false</trackTrending>
        <type>DateTime</type>
    </fields>
    <fields>
        <fullName>Transferred_By__c</fullName>
        <deleteConstraint>SetNull</deleteConstraint>
        <externalId>false</externalId>
        <label>Transferred By</label>
        <referenceTo>User</referenceTo>
        <relationshipName>Documentation</relationshipName>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>Transferred_From_Id__c</fullName>
        <deleteConstraint>SetNull</deleteConstraint>
        <externalId>false</externalId>
        <label>Transferred From</label>
        <referenceTo>Documentation__c</referenceTo>
        <relationshipLabel>Documentation</relationshipLabel>
        <relationshipName>Documentation</relationshipName>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Lookup</type>
    </fields>
    <label>Documentation</label>
    <listViews>
        <fullName>Security_Doc_Group_ABD_Documentation</fullName>
        <filterScope>Queue</filterScope>
        <label>Security Doc Group - ABD</label>
        <queue>Security_Doc_Group_ABD</queue>
        <sharedTo>
            <allInternalUsers></allInternalUsers>
        </sharedTo>
    </listViews>
    <listViews>
        <fullName>Security_Doc_Group_AMLComplexity_Documentation</fullName>
        <filterScope>Queue</filterScope>
        <label>Security Doc Group - AMLComplexity</label>
        <queue>Security_Doc_Group_AMLComplexity</queue>
        <sharedTo>
            <allInternalUsers></allInternalUsers>
        </sharedTo>
    </listViews>
    <listViews>
        <fullName>Security_Doc_Group_AML_Documentation</fullName>
        <filterScope>Queue</filterScope>
        <label>Security Doc Group - AML</label>
        <queue>Security_Doc_Group_AML</queue>
        <sharedTo>
            <allInternalUsers></allInternalUsers>
        </sharedTo>
    </listViews>
    <listViews>
        <fullName>Security_Doc_Group_Backoffice_Documentation</fullName>
        <filterScope>Queue</filterScope>
        <label>Security Doc Group - Backoffice</label>
        <queue>Security_Doc_Group_Backoffice</queue>
        <sharedTo>
            <allInternalUsers></allInternalUsers>
        </sharedTo>
    </listViews>
    <listViews>
        <fullName>Security_Doc_Group_Bank_Documentation</fullName>
        <filterScope>Queue</filterScope>
        <label>Security Doc Group - Bank</label>
        <queue>Security_Doc_Group_Bank</queue>
        <sharedTo>
            <allInternalUsers></allInternalUsers>
        </sharedTo>
    </listViews>
    <listViews>
        <fullName>Security_Doc_Group_Claims_Documentation</fullName>
        <filterScope>Queue</filterScope>
        <label>Security Doc Group - Claims</label>
        <queue>Security_Doc_Group_Claims</queue>
        <sharedTo>
            <allInternalUsers></allInternalUsers>
        </sharedTo>
    </listViews>
    <listViews>
        <fullName>Security_Doc_Group_DSS_Documentation</fullName>
        <filterScope>Queue</filterScope>
        <label>Security Doc Group - DSS</label>
        <queue>Security_Doc_Group_DSS</queue>
        <sharedTo>
            <allInternalUsers></allInternalUsers>
        </sharedTo>
    </listViews>
    <listViews>
        <fullName>Security_Doc_Group_Familylaw_Documentation</fullName>
        <filterScope>Queue</filterScope>
        <label>Security Doc Group - Familylaw</label>
        <queue>Security_Doc_Group_Familylaw</queue>
        <sharedTo>
            <allInternalUsers></allInternalUsers>
        </sharedTo>
    </listViews>
    <listViews>
        <fullName>Security_Doc_Group_General_Documentation</fullName>
        <filterScope>Queue</filterScope>
        <label>Security Doc Group - General</label>
        <queue>Security_Doc_Group_General</queue>
        <sharedTo>
            <allInternalUsers></allInternalUsers>
        </sharedTo>
    </listViews>
    <listViews>
        <fullName>Security_Doc_Group_OPF_Documentation</fullName>
        <filterScope>Queue</filterScope>
        <label>Security Doc Group - OPF</label>
        <queue>Security_Doc_Group_OPF</queue>
        <sharedTo>
            <allInternalUsers></allInternalUsers>
        </sharedTo>
    </listViews>
    <listViews>
        <fullName>Security_Doc_Group_StaffGrpIns_Documentation</fullName>
        <filterScope>Queue</filterScope>
        <label>Security Doc Group - StaffGrpIns</label>
        <queue>Security_Doc_Group_StaffGrpIns</queue>
        <sharedTo>
            <allInternalUsers></allInternalUsers>
        </sharedTo>
    </listViews>
    <listViews>
        <fullName>Security_Doc_Group_UWRestricted_Documentation</fullName>
        <filterScope>Queue</filterScope>
        <label>Security Doc Group - UWRestricted</label>
        <queue>Security_Doc_Group_UWRestricted</queue>
        <sharedTo>
            <allInternalUsers></allInternalUsers>
        </sharedTo>
    </listViews>
    <listViews>
        <fullName>Security_Doc_Group_Underwriting_Documentation</fullName>
        <filterScope>Queue</filterScope>
        <label>Security Doc Group - Underwriting</label>
        <queue>Security_Doc_Group_Underwriting</queue>
        <sharedTo>
            <allInternalUsers></allInternalUsers>
        </sharedTo>
    </listViews>
    <listViews>
        <fullName>Security_Doc_Group_advice_Documentation</fullName>
        <filterScope>Queue</filterScope>
        <label>Security Doc Group - advice</label>
        <queue>Security_Doc_Group_advice</queue>
        <sharedTo>
            <allInternalUsers></allInternalUsers>
        </sharedTo>
    </listViews>
    <listViews>
        <fullName>Security_Doc_Group_internal_Documentation</fullName>
        <filterScope>Queue</filterScope>
        <label>Security Doc Group - internal</label>
        <queue>Security_Doc_Group_internal</queue>
        <sharedTo>
            <allInternalUsers></allInternalUsers>
        </sharedTo>
    </listViews>
    <nameField>
        <displayFormat>Doc-{0000}</displayFormat>
        <label>Documentation Name</label>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>AutoNumber</type>
    </nameField>
    <pluralLabel>Documentation</pluralLabel>
    <searchLayouts/>
    <sharingModel>Private</sharingModel>
    <validationRules>
        <fullName>Cannot_Edit_Transferred_Record</fullName>
        <active>true</active>
        <description>The record has been transferred. You cannot edit a transferred record.</description>
        <errorConditionFormula>IF( 
     $Setup.Bypass__c.Validation_Rules__c ,
     false,
     AND(
      NOT(ISCHANGED(Is_Transferred__c)),
      Is_Transferred__c  = true
     )
)</errorConditionFormula>
        <errorMessage>The record has been transferred. You cannot edit a transferred record.</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>Year_cannot_be_blank_Account</fullName>
        <active>false</active>
        <errorConditionFormula>ISBLANK( Relates_to_Calender_Year__c) &amp;&amp;  Related_Object_Name__c =&quot;Account&quot; &amp;&amp; NOT(ISNEW())</errorConditionFormula>
        <errorMessage>The Year field cannot be blank</errorMessage>
    </validationRules>
    <visibility>Public</visibility>
</CustomObject>
