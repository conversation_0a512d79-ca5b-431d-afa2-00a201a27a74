import { LightningElement, api, wire } from 'lwc';
/** Apex methods from CustomLookUpDataService for object - APXTConga4__Conga_Template__c*/
import search from '@salesforce/apex/CustomLookupDataService.searchCongaTemplate';
import searchInitTemplate from '@salesforce/apex/CustomLookupDataService.getInitialCongaTemplate';

export default class OutcomeToCongaTemplateInputs extends LightningElement {
    @api outcome; 

    initialSelection = [];
    errors = [];
    recentlyViewed = [];

    connectedCallback() {
        this.handleSearchInit();
    }

    get _outcomeStyle(){
        return (this.outcome.outcome === "none" || this.outcome.outcome === "any") ? "font-weight: bold;" : "";
    }

    /**
     * Handles the lookup search event.
     * Calls the server to perform the search and returns the resuls to the lookup.
     * @param {event} event `search` event emmitted by the lookup
     */
    handleLookupSearch(event) {
        const lookupElement = event.target;
        // Call Apex endpoint to search for records and pass results to the lookup
        search({searchTerm: event.detail.searchTerm, congaGrpSetString: JSON.stringify(['PracticeTemplate'])})
            .then((results) => {
                lookupElement.setSearchResults(results);
            })
            .catch((error) => {
                // eslint-disable-next-line no-console
                console.error('Lookup error', JSON.stringify(error));
                this.errors = [error];
            });
    }

    /**
     * Handles the lookup selection change
     * @param {event} event `selectionchange` event emmitted by the lookup.
     * The event contains the list of selected ids.
     */
    // eslint-disable-next-line no-unused-vars
    handleLookupSelectionChange(event) {
        if(this.checkForErrors()){
            const selectEvent = new CustomEvent('templatechange', {
                detail:{
                    outcome: this.outcome.outcome,
                    congatemplates: event.detail
                }
            });
            this.dispatchEvent(selectEvent);
        }
    }

    handleClear() {
        this.initialSelection = [];
        this.errors = [];
    }

    checkForErrors() {
        this.errors = [];
        const selection = this.template.querySelector('c-lookup').getSelection();

        // Enforcing required field
        if (selection.length === 0) {
            this.errors.push({ message: 'Please make a selection.' });
        }

        if(this.errors.length > 0){
            return false;
        }else{
            return true;
        }
    }

    handleRemoveRow() {
        const selectEvent = new CustomEvent('removerowchange', {
            detail:{
                outcome: this.outcome
            }
        });
        this.dispatchEvent(selectEvent);
    }

    handleSearchInit() {
        searchInitTemplate({ initIds: this.outcome.congatemplates })
            .then((result) => {
                this.initialSelection = result;
                this.error = undefined;
            })
            .catch((error) => {
                this.error = error;
                this.initialSelection = undefined;
            });
    }
}