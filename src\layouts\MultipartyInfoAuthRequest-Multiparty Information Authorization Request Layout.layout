<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Consent_Arrangement_Record__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SourceRecordId</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>DataUsePurposeId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AuthorizationType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ResponseStatus</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <relatedLists>
        <fields>Name</fields>
        <fields>PertainsTo</fields>
        <fields>Consenter</fields>
        <fields>RequestedDateTime</fields>
        <fields>ExpirationDateTime</fields>
        <fields>ResponseStatus</fields>
        <relatedList>InfoAuthRequestMultipartyRequest</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00h5P000000dQ1D</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
