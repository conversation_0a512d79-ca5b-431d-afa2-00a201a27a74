<?xml version="1.0" encoding="UTF-8"?>
<CustomObjectTranslation xmlns="http://soap.sforce.com/2006/04/metadata">
    <caseValues>
        <plural>false</plural>
        <value>Document d’identification</value>
    </caseValues>
    <caseValues>
        <plural>true</plural>
        <value>Documents d’identification</value>
    </caseValues>
    <fields>
        <label><!-- Contact --></label>
        <name>Contact__c</name>
        <relationshipLabel><!-- Identification Documents --></relationshipLabel>
    </fields>
    <fields>
        <label><!-- Document Version --></label>
        <name>Document_Version__c</name>
    </fields>
    <fields>
        <label><!-- Account --></label>
        <name>FinServ__Account__c</name>
        <relationshipLabel><!-- Identification Document --></relationshipLabel>
    </fields>
    <fields>
        <label><!-- Document Number --></label>
        <name>FinServ__DocumentNumber__c</name>
    </fields>
    <fields>
        <label><!-- Document Type --></label>
        <name>FinServ__DocumentType__c</name>
        <picklistValues>
            <masterLabel>ATO Debt Pay Notice</masterLabel>
            <translation><!-- ATO Debt Pay Notice --></translation>
        </picklistValues>
        <picklistValues>
            <masterLabel>Birth Certificate</masterLabel>
            <translation><!-- Birth Certificate --></translation>
        </picklistValues>
        <picklistValues>
            <masterLabel>Citizenship Certificate</masterLabel>
            <translation><!-- Citizenship Certificate --></translation>
        </picklistValues>
        <picklistValues>
            <masterLabel>Driver&apos;s License</masterLabel>
            <translation><!-- Driver&apos;s License --></translation>
        </picklistValues>
        <picklistValues>
            <masterLabel>Financial Benefits Notice</masterLabel>
            <translation><!-- Financial Benefits Notice --></translation>
        </picklistValues>
        <picklistValues>
            <masterLabel>Health Card</masterLabel>
            <translation><!-- Health Card --></translation>
        </picklistValues>
        <picklistValues>
            <masterLabel>National Identity Card</masterLabel>
            <translation><!-- National Identity Card --></translation>
        </picklistValues>
        <picklistValues>
            <masterLabel>Other</masterLabel>
            <translation>Autre</translation>
        </picklistValues>
        <picklistValues>
            <masterLabel>Passport</masterLabel>
            <translation>Passeport</translation>
        </picklistValues>
        <picklistValues>
            <masterLabel>Pension Card</masterLabel>
            <translation><!-- Pension Card --></translation>
        </picklistValues>
        <picklistValues>
            <masterLabel>Provision of Services Notice</masterLabel>
            <translation><!-- Provision of Services Notice --></translation>
        </picklistValues>
        <picklistValues>
            <masterLabel>School Attendance Notice</masterLabel>
            <translation><!-- School Attendance Notice --></translation>
        </picklistValues>
        <picklistValues>
            <masterLabel>Visa</masterLabel>
            <translation>Visa</translation>
        </picklistValues>
    </fields>
    <fields>
        <label><!-- Expiration Date --></label>
        <name>FinServ__ExpirationDate__c</name>
    </fields>
    <fields>
        <label><!-- Issue Date --></label>
        <name>FinServ__IssueDate__c</name>
    </fields>
    <fields>
        <label><!-- Issuing Country --></label>
        <name>FinServ__IssuingCountry__c</name>
    </fields>
    <fields>
        <label><!-- Source System ID --></label>
        <name>FinServ__SourceSystemId__c</name>
    </fields>
    <fields>
        <label><!-- Verified By --></label>
        <name>FinServ__VerifiedBy__c</name>
        <relationshipLabel><!-- Identification Documents --></relationshipLabel>
    </fields>
    <fields>
        <label><!-- Verified On --></label>
        <name>FinServ__VerifiedOn__c</name>
    </fields>
    <fields>
        <label><!-- Identity Verification --></label>
        <name>Identity_Verification__c</name>
        <relationshipLabel><!-- Identification Documents --></relationshipLabel>
    </fields>
    <fields>
        <label><!-- Removed TFN from document copies? --></label>
        <name>Removed_TFN_from_document_copies__c</name>
    </fields>
    <fields>
        <help><!-- ABN issued to customer (if any) --></help>
        <label><!-- ABN --></label>
        <name>Tech_ABN__c</name>
    </fields>
    <fields>
        <label><!-- Business Address --></label>
        <name>Tech_Business_Address__c</name>
    </fields>
    <fields>
        <label><!-- Business Name --></label>
        <name>Tech_Business_Name__c</name>
    </fields>
    <fields>
        <label><!-- Customer Name --></label>
        <name>Tech_Customer_Name__c</name>
    </fields>
    <fields>
        <label><!-- Date of Birth --></label>
        <name>Tech_Date_of_Birth__c</name>
    </fields>
    <fields>
        <label><!-- Residential Address --></label>
        <name>Tech_Residential_Address__c</name>
    </fields>
    <gender>Masculine</gender>
    <nameFieldLabel>Document</nameFieldLabel>
    <startsWith>Consonant</startsWith>
    <validationRules>
        <errorMessage><!-- The expiration date can&apos;t be earlier than the issue date. --></errorMessage>
        <name>FinServ__ExpDateCannotBeLaterThanStart</name>
    </validationRules>
    <validationRules>
        <errorMessage><!-- The issue date can&apos;t be in the future. --></errorMessage>
        <name>FinServ__IssueDateCannotBeInFuture</name>
    </validationRules>
    <validationRules>
        <errorMessage><!-- Verification date and time can&apos;t be in the future. --></errorMessage>
        <name>FinServ__VerificationDateTimeCannotBeInFuture</name>
    </validationRules>
    <validationRules>
        <errorMessage><!-- Please check to ensure you have entered the correct dates. Verified date must be after the Issue date. Neither dates can be in the future. --></errorMessage>
        <name>Please_Enter_Correct_Dates</name>
    </validationRules>
    <validationRules>
        <errorMessage><!-- You have entered an invalid Tax File Number --></errorMessage>
        <name>Tax_File_Number</name>
    </validationRules>
</CustomObjectTranslation>
