import { LightningElement, track, wire, api } from 'lwc'; 
import { loadStyle } from "lightning/platformResourceLoader";
//For navigation
import { NavigationMixin } from 'lightning/navigation';
// CSS from static resource
import cdrCss from '@salesforce/resourceUrl/CDR_Styles';
import CDR_LOGO from '@salesforce/resourceUrl/CDR_Logo';
// Toast events
import {ShowToastEvent} from "lightning/platformShowToastEvent";
//Added for SFP-41089
import USER_ID from '@salesforce/user/Id';
// Apex controller
import getConsentArragementRecord from '@salesforce/apex/CdrLandingDashboardController.getConsentArragementRecordForSecondaryUsers';
import updateClientConsentRecord from "@salesforce/apex/CdrLandingDashboardController.updateClientConsentRecord";//Added for SFP-41089
import getFinancialAccountDetails from '@salesforce/apex/CdrAuthSecondaryUserController.getFinancialAccountDetails'; //Joint changes
// Custom labels
import SERVICE_IMPACT_BODY from '@salesforce/label/c.CDR_Service_Impact_Body';
import SERVICE_IMPACT_LABEL from '@salesforce/label/c.CDR_Service_Impact_Label';
import SHARED_DATA_LABEL from '@salesforce/label/c.CDR_Shared_Data_Label';
import SHARED_DATA_INFO2 from '@salesforce/label/c.CDR_Shared_Data_Info_P2';
import SHARED_DATA_INFO1 from '@salesforce/label/c.CDR_Shared_Data_Info_P1';
import stopShareAtleast1Joint from '@salesforce/label/c.CDR_Stop_Sharing_atleast_1_Joint';

export default class cdrSecondaryUserStopSharing extends NavigationMixin(LightningElement) {

    @api recordId;
    @api consentRecord;
    @api recipientName;
    @api startDate;
    @api endDate;

    @track parameters = {};
    @track initialDataResponse;
    @track dataClusterToPermLangMap = [];
    @track sectionCollapsed = true;
    //@track availableJointFinancialAccounts = [];

    cdrLogo = CDR_LOGO;
    status;
    errorMessage;
    isRecordFound;
    consentRecordDetails;
    consentRecordId;
    isFirstPage;
    isContainsActiveJoint = false; // Joint changes
    ownerAccId;
    
    //Added for SFP-41089 (Doms)
    revokedBy;
    revocationDate;
    revocationRequestedBy;

    //toast
    initializeToast = '';
    
    labels = {
        SERVICE_IMPACT_LABEL,
        SERVICE_IMPACT_BODY,
        SHARED_DATA_LABEL, 
        SHARED_DATA_INFO1,
        SHARED_DATA_INFO2,
        stopShareAtleast1Joint
    };
    loaded = false;
    
    constructor() {
        super();
        Promise.all([
            loadStyle(this, cdrCss)
        ]).then(() => {
            // console.log("Loaded css");
        }).catch(error => {
            console.error('Error loading static resources', error);
        });
    }

    connectedCallback(){
        this.ownerAccId = this.getownerAccId();
        this.parameters = this.getQueryParameters();
        this.isFirstPage = true;

            getConsentArragementRecord({
                queryParameter : this.parameters,
                ownerAccountId: this.ownerAccId
            }).then(result=>{
                if(this.consentRecordDetails){
                    return;
                }
    
                this.consentRecordDetails = result;
                let consent = JSON.parse(JSON.stringify(this.consentRecordDetails));
    
                if(!consent.errorMessage && consent.isOwnedByLoggedInUser){
                    this.isRecordFound = true;
                    this.consentRecordId = (consent.consentRecord).Id;
                    //Added for SFP-41089
                    this.revokedBy = USER_ID;
                    let client = (consent.consentRecord).CreatedBy.FirstName + (consent.consentRecord).CreatedBy.LastName;
                    if(client){
                        this.revocationRequestedBy = client;
                    }//Added for SFP-41089 - END
    
                    let softwareProduct = (consent.consentRecord).Data_Recipient_Software_Product__r;
                    if(softwareProduct && softwareProduct.Data_Recipient_Brand__r && softwareProduct.Data_Recipient_Brand__r.Data_Recipient__r && softwareProduct.Data_Recipient_Brand__r.Data_Recipient__r.Legal_Name__c){
                        this.recipientName = softwareProduct.Data_Recipient_Brand__r.Data_Recipient__r.Legal_Name__c;
                    }
    
                    if((consent.consentRecord).Start_Date__c){
                        this.startDate = new Date((consent.consentRecord).Start_Date__c).toLocaleDateString('en-GB', { day: 'numeric', month: 'long', year: 'numeric' }).replace(/ /g, ' ');
                    }
                    if((consent.consentRecord).End_Date__c){
                        this.endDate =  new Date((consent.consentRecord).End_Date__c).toLocaleDateString('en-GB', { day: 'numeric', month: 'long', year: 'numeric' }).replace(/ /g, ' ');
                    }
        
                    var dataRequest = consent.dataClusterToPermLang;
                    var footnoteHeader = consent.footnoteHeader;
                    var footnoteBody = consent.footnoteBody;
        
                    // get data cluster details
                    for(let key in consent.dataClusterToPermLang){
                        for(let scopeKey in consent.scopeToAuthSelReqMap){
                            if(scopeKey === key && dataRequest.hasOwnProperty(key)){
                                this.dataClusterToPermLangMap.push({key: key, value: dataRequest[key], header: footnoteHeader[key], body:footnoteBody[key]});
                            }
                        }
                    }
                } else {
                    this.errorMessage = 'Page can not be found.'; //consent.errorMessage;
                    this.isRecordFound = false;
                }
                this.loaded = true;
                
            }).catch(error=>{
                console.log('Error fetching initial data response: ', error);
                this.loaded = true;
                this.isRecordFound = false;
                let event = new ShowToastEvent({
                    title: 'Error!',
                    message: error,
                    variant: 'error',
                    mode: 'dismissable'
                });
                this.dispatchEvent(event);
            });
    
            getFinancialAccountDetails({ //SFP-34910 by Heither Ann Ballero
                originatedFromArrangement: true,
                queryParameter: this.parameters,
                ownerAccountId: this.ownerAccId
            }).then(data => {
                if(data){
                    //this.finAcctDetailsWrapper = data;
    
                    //iterate through list of wrappers passed from APEX controller
                    for(let faData of data){
                        //key-> FA number and value is the each wrapper containing financial account details, availability, unavailability reasons and badge styling
                        if(faData.isFinAcctAvailable && faData.isJointAccount && faData.selected == true){
                            //this.availableJointFinancialAccounts.push(faData);
                            this.isContainsActiveJoint = true;
                        }
                    }
                    //console.log('availableJointFinancialAccounts', this.availableJointFinancialAccounts);
                } else {
                    console.log('Error fetching Financial Account information: ', error);S
                    this.showToast('An unexpected error occurred when retrieving financial accounts', 'error', 'Please contact AMP Bank.');
                }
            }).catch(error => {
                if(this.isRecordFound == true){
                    console.log('Error fetching Financial Account information: ', error);
                    this.showToast('An unexpected error occurred when retrieving financial accounts', 'error', 'Please contact AMP Bank.');
                }
            });
    }

    getQueryParameters() {
        var params = {};
        var search = location.search.substring(1);
        if (search) {
            params = JSON.parse('{"' + search.replace(/&/g, '","').replace(/=/g, '":"') + '"}', (key, value) => {
                return key === "" ? value : decodeURIComponent(value)
            });
        }
        return params;
    }

    // navigate to data sharing arrangement page (e.g. /data-sharing-arrangement?arrangement=********)
    handleCancel(event) {
        event.preventDefault();
        this[NavigationMixin.Navigate]({
            type: 'comm__namedPage',
            attributes: {
                pageName: 'data-sharing-arrangement'
            },
            state: {
                'arrangement': event.currentTarget.dataset.id
            }
        });
    }

    // navigate to second screen
    handleNext(){
        this.isFirstPage = false;
    }

    //Added for SFP-41089 to Handle Yes, stop sharing button
    stopSharing(){
        this.loaded = false;
        
        const date = new Date();
        this.revocationDate = date.toISOString();
        //Call Apex to update Consent Arrangement and Client Consented record 
        //Revocation Date, Client_Consented_Record__r,End_Date__c and End Date should be the same
        updateClientConsentRecord({
            consentRecordId : this.consentRecordId,
            revocationDate : this.revocationDate,
            revocationRequestedBy : this.revocationRequestedBy,
            revokedBy : this.revokedBy,
        }).then(() => {
            this.initializeToast = this.stringtoHex('Withdraw'); //Added by Johnroy - SFP-43357
            /*this.dispatchEvent(
                new ShowToastEvent({
                    title: 'Success!',
                    //message: 'You have successfully stopped sharing the data below with ' + this.recipientName + '.',
                    variant: 'success', 
                    mode: 'sticky',
                })
            );*/
            //redirect to data sharing arrangement
                this[NavigationMixin.Navigate]({
                    type: 'comm__namedPage',
                    attributes: {
                        pageName: 'secondaryuser-data-sharing-arrangement'
                    },
                    state: {
                        'arrangement': this.consentRecordId,
                        'account': this.ownerAccId,
                        'notif': this.initializeToast
                    }
                });
            
            
            this.loaded = true;
        }).catch(error =>{
            console.log('Error on Update Client Consented Record ',error);
            this.loaded = true;
            this.dispatchEvent(
                new ShowToastEvent({
                    title: 'Error!',
                    message: error.body.message,
                    variant: 'error', 
                    mode: 'sticky',
                })
            )
        })
    }
    //Added by Johnroy - SFP-43357
    stringtoHex(str){
        var result = '';
        for (var i=0; i<str.length; i++) {
          result += str.charCodeAt(i).toString(16);
        }
        //console.log("@@@ String to Hex Result: ", result);
        return result;
    }

    //handle toast events
    showToast(toastTitle, toastVariant, toastMessage){
        this.dispatchEvent(
            new ShowToastEvent({
                title: toastTitle,
                variant: toastVariant,
                message: toastMessage
            }),
        );

        //if error occurs, we keep the spinner loading
        if(toastVariant === 'error'){
            //this.loaded = false;
        }
    }

    getownerAccId() {
        const queryString = window.location.search;
        
        const urlParams = new URLSearchParams(queryString);

        const account = urlParams.get('account');

        //console.log('account Id: ' + account);

        return account;
        
    }
}