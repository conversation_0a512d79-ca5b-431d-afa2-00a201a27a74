body {   
	font-family: "Open Sans", sans-serif; 
	line-height: 1.25;
} 

 

table {
	border: 1px solid #ccc;
	border-collapse: collapse;
	margin: 0;
	padding: 0;
	width: 100%;
	table-layout: fixed;
}


table caption {
	font-size: 1.5em;
	margin: .5em 0 .75em;
}

table tr {
	background-color: #FFFFFF;
	/**border: 1px solid #ddd;**/
	padding-left: 0;
	padding-right: 0;
	/**padding-top: .35em;
	padding-bottom: .35em; -commented by Steph SFP-63279**/
}

table th, table td {
	padding-left: 0;
	padding-right: 0;
	padding-top: 0; /**SFP-63279, Steph**/
	padding-bottom: 0; /**SFP-63279, Steph**/
	text-align: center;
}

table th {
	font-size: .85em;
	letter-spacing: .1em;
	text-transform: uppercase;
}


table {
	border: 0;
}

table caption {
	font-size: 1.3em;
}

table thead {
	border: none;
	clip: rect(0 0 0 0);
	height: 1px;
	margin: -1px;
	overflow: hidden;
	padding: 0;
	position: absolute;
	width: 1px; 
}

table tr {
	/**border-bottom: 3px solid #ddd;**/
	display: block;
	/**margin-bottom: .625em;**/
}

table td {
	/**border-bottom: 1px solid #ddd;**/
	border-top: 0;
	display: block;
	font-size: 14px;
	line-height: 21px;
	text-align: right;

	/*SFP-60119, xylene*/
	float: left;
	width: 100%;
}

table td::before {
	content: attr(data-label);
	float: left;
	/**font-weight: bold;
	text-transform: uppercase;**/

	/*SFP-60119, xylene*/
	overflow-wrap: break-word;
	white-space: break-spaces;
	clear: both;
	/**max-width: 50%; SFP-71433, Steph**/
	text-align: left;
}

table td.borderSetup::before {
	width:50%;
	padding-top: 4px;
  	padding-bottom: 4px;
  	align-items: center;
  	display: flex;
}

table td:last-child {
	border-bottom: 0;
}

/* new */
/* fixed extra indent in beneficiary row header */
.slds-table_cell-buffer tr>td.nopaddingside:first-child{
/* .slds-table_cell-buffer tr>td.nopaddingside { */
	/*padding-left: 0; SFP-63279, Steph
	 padding-right: 0; */

	/*SFP-60119, xylene*/
	clear: both;
}

.columnhelptext {
	float: left;
    /* padding-left: 5px; */
    padding-left: var(--lwc-spacingXSmall,0.5rem);
	
	/* SFP-60276, burhan */
	margin-top: -2px;
}


.slds-table:not(.slds-no-row-hover) tbody tr:hover>td, 
.slds-table:not(.slds-no-row-hover) tbody tr:hover>th, 
.slds-table:not(.slds-no-row-hover) tbody tr:focus>td, 
.slds-table:not(.slds-no-row-hover) tbody tr:focus>th{
	background: #FFFFFF;
}

.slds-table_bordered:not(.slds-no-row-hover) tbody tr:hover>td:not(.slds-has-focus), 
.slds-table_bordered:not(.slds-no-row-hover) tbody tr:hover>th:not(.slds-has-focus), 
.slds-table_bordered:not(.slds-no-row-hover) tbody tr:focus>td:not(.slds-has-focus), 
.slds-table_bordered:not(.slds-no-row-hover) tbody tr:focus>th:not(.slds-has-focus), 
.slds-table--bordered:not(.slds-no-row-hover) tbody tr:hover>td:not(.slds-has-focus), 
.slds-table--bordered:not(.slds-no-row-hover) tbody tr:hover>th:not(.slds-has-focus), 
.slds-table--bordered:not(.slds-no-row-hover) tbody tr:focus>td:not(.slds-has-focus), 
.slds-table--bordered:not(.slds-no-row-hover) tbody tr:focus>th:not(.slds-has-focus){
	box-shadow:none;
}

.removeButton{
    border-color: #b50217;
	height:44px;
	border: 1px solid #B50217;
	box-sizing: border-box;
	color: #B50217;
	margin-top:-8px;
}
.utilityIconDelete{
	--sds-c-icon-color-foreground-default: #B50217;
	margin-right: 10px;
}
.addButton{
    border-color: #3B4E98;
	height:44px;
	border: 1px solid #3B4E98;
	box-sizing: border-box;
	color: #3B4E98;
	/**--sds-c-button-neutral-color-background: lightgrey;**/
}
.utilityIconAdd{
	--sds-c-icon-color-foreground-default: #3B4E98;;
	margin-right: 10px;
}

.cardLightning{
	--sds-c-card-sizing-border: 0;
	--sds-c-card-body-spacing-block-start:0;
	--sds-c-card-body-spacing-block-end:24px;
	--sds-c-card-header-spacing-block-start:0;
	--sds-c-card-header-spacing-inline-end:0;
	--sds-c-card-header-spacing-block-end:0;
	--sds-c-card-header-spacing-inline-start:0;
}

/*.slds-table_cell-buffer tr>td.nopaddingside{
		padding-top: 0; Steph
}*/

.slds-table_cell-buffer tr>td.borderSetup{ /**SFP-63279, Steph**/
	border-bottom:1.5px solid #DDDBDA;
	background:#FAFAF9;
	padding-left: 8px;
	display: flex;
    align-items: stretch;
    flex: 1;
	font-weight:600;
}
.slds-table_cell-buffer tr>td.borderSetup[data-id="1"]{ /**SFP-63279, Steph**/
	border-top:1.5px solid #DDDBDA;
}
.slds-table_cell-buffer tr>td.rowHeaderStyle,
.rowHeaderStyle{ /**SFP-63279, Steph**/
	padding-bottom:24px;
}
.slds-table_cell-buffer tr>td.pHeaderStyle, 
.pHeaderStyle{
	color: #001E41; 
	font-family: Open Sans;
	font-style: normal;
	font-weight: 600;
	font-size: 16px;
	line-height: 24px;
}
.slds-table_cell-buffer tr>td.rowHdrStyle{ /**SFP-63279, Steph**/
	padding-top:16px;
}
.slds-table_cell-buffer tr>td.rowHdrStyle[data-label="My beneficiary 1"]{ /**SFP-63279, Steph**/
	padding-top:0;
}
.insideCard{ /**SFP-63279, Steph**/
	padding:0;
}

.formModalContent{
	padding: 16px;
	box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.16);
    border-radius: 4px;
	width: 343px;
}

.formModalBody{
    font-family: Open Sans;
    font-style: normal;
    font-weight: normal;
    font-size: 20px;
    line-height: 30px;

    color: #001E41;
	padding-bottom: 32px;
}

.slds-button_brand.formModalYes{
    font-family: Open Sans;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;

    text-align: center;

    color: #FFFFFF;

    padding-top: 10px;
    padding-bottom: 10px;
    padding-right: 24px;
    padding-left: 24px;
	
	width: 75px;
	height: 44px;
	min-width: 0;
}

.slds-button_neutral.formModalNo{
    font-family: Open Sans;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;

    /*display: flex;*/
    align-items: center;
    text-align: center;

    color: #525252;

    padding-top: 10px;
    padding-bottom: 10px;
    padding-right: 24px;
    padding-left: 24px;

	width: 71px;
	height: 44px;
	min-width: 0;
}

.clearboth {
	clear: both;
}

.defaultfont {
	font-size: 14px;
	line-height: 21px;
}

.paddingleft5percent {
	padding-left: 5%;
}

.dataColumnStyle{/**SFP-63279, Steph**/
    white-space: normal;
    text-align: left;
	overflow-wrap: break-word;
	white-space: break-spaces;
	background: white;
	padding-left: 8px;
	height: 100%;
	font-weight: normal;
	padding-top: 4px;
  	padding-bottom: 4px;
  	align-items: center;
  	display: flex;
	flex: 1 1 0%;
	text-align: left;
	background: white;
}

.spaceStyle{/**SFP-63279, Steph**/
	padding-top:24px;
	padding-bottom:24px;
}
.addPadding{
	padding-bottom:24px;
}
.addPadding16{
	padding-bottom:16px;
}
.noPaddingStyle{
	padding:0;
}
@media (max-width: 47.999em) {/**SFP-63279, Steph**/
	.slds-table_cell-buffer tr>td.rowHeaderStyle,
	.rowHeaderStyle{ 
		padding-bottom:16px;
	}
	.spaceStyle{
		padding-top:16px;
		padding-bottom:16px;
	}
	.removeButton{
		margin-top:0;
	}
}

.hrStyle{ /**SFP-63776, Bea**/
	margin: 32px 0px 32px 0px;
	background: #C6C6C8;
	}

/* .table {
	border-collapse: collapse;
	display: inline-table;
  }
  
  .tr {
	display: table-row;
  }
  
  .th, .td {
	display: table-cell;
	border: 1px solid #555;
	padding: 3px 6px;
  }
  
  .th {
	background-color: #ddd;
	font-weight: bold;
	text-align: center;
  }
  
  .td {
	text-align: right;
  }
  
  .my-table.show-thin {
	display: block;
  }
  
  .show-thin .tr {
	border-bottom: 1px solid black;
	display: block;
	margin-bottom: 2px;
	padding-bottom: 2px;
  }
  
  .show-thin .td {
	border: none;
	display: block;
	padding: 0;
	text-align: left;
  }
  
  .show-thin .td:before {
	content: attr(title) ':';
	display: inline-block;
	font-weight: bold;
	padding-right: 5px;
  }
  
  .show-thin .thin-hide {
	display: none;
  }

  @media screen and (max-width: 9600px) {
	.tr {
		border-bottom: 1px solid black;
		display: block;
		margin-bottom: 2px;
		padding-bottom: 2px;
	}
	
	.td {
		border: none;
		display: block;
		padding: 0;
		text-align: left;
	}
	
	.td:before {
		content: attr(title) ':';
		display: inline-block;
		font-weight: bold;
		padding-right: 5px;
	}
	
	.thin-hide {
		display: none;
	}
  } */