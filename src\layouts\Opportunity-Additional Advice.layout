<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Opportunity Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>AccountId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Joint_Account__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Household__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Opportunity_Source__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LeadSource</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Type</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Client_Motivation__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Interest__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Last_Service_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Servicing_Adviser__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Source_System_ID__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CampaignId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>CloseDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>StageName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Date_of_Last_Contact__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>RecordTypeId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Sales_Outcome__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Referrer_Contact__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Servicing_Practice__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Pricebook2Id</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Action_Plan_Template__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Estimated Revenue (Excl GST)</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Advice__c</field>
            </layoutItems>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Probability</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Advice_Estimated_Revenue__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Amount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Total_Estimated_Revenue__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Asset Under Management (AUM) Info</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Total_AUM__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>AUM_Probability__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Manual_AUM__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>AUM_Expected__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Annual Premium Income (API) Info</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Total_API__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>API_Probability__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Manual_API__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>API_Expected__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Advice Exploration Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Conversation_2_Meeting__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Conversation_2_Scenarios_Completed__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Detailed_Profile_Completed__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Advice_Builder_Completed__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Conversation_2_Complete__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Initial_Profile_Completed__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Worksheet_Questions_Completed__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Handover_to_ACT_Completed__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Goal Planning &amp; Presentation Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Conversation_3_Meeting__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Goals_Planning__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Goals_Plan__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Cust_Will_Implement_Advice__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Uploaded_Authority_to_Proceed__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Conversation_3_Complete__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Signed_Authority_to_Proceed__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Received_Payment__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Interaction Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Number_of_Days_in_Stage__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Stage_Threshold__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Date_of_Last_Stage_Update__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Won/Lost Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Won_Lost_Reason__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns/>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Description Information</label>
        <layoutColumns/>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Other Information</label>
        <layoutColumns/>
        <layoutColumns/>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
        <platformActionListItems>
            <actionName>Edit</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>0</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Delete</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>1</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Clone</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>2</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__NewTaskAdvisor</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>3</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewEvent</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>4</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Sales_Comms_Log</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>5</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.ContentNote</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>6</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.TextPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>7</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.ContentPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>8</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>SendEmail</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>9</sortOrder>
        </platformActionListItems>
    </platformActionList>
    <quickActionList>
        <quickActionListItems>
            <quickActionName>FeedItem.TextPost</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>FeedItem.ContentPost</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Sales_Comms_Log</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>FinServ__NewTaskAdvisor</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>NewEvent</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>FeedItem.LinkPost</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>FeedItem.PollPost</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>SendEmail</quickActionName>
        </quickActionListItems>
    </quickActionList>
    <relatedLists>
        <fields>Product2</fields>
        <fields>API__c</fields>
        <fields>AUM__c</fields>
        <fields>TotalPrice</fields>
        <relatedList>RelatedLineItemList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Date_Time_Created__c</fields>
        <fields>CREATEDBY_USER</fields>
        <fields>File_Note_Type__c</fields>
        <fields>Status__c</fields>
        <relatedList>File_Note__c.Opportunity__c</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedContentNoteList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>TASK.SUBJECT</fields>
        <fields>TASK.WHO_NAME</fields>
        <fields>ACTIVITY.TASK</fields>
        <fields>FinServ__Regarding__c</fields>
        <fields>TASK.PRIORITY</fields>
        <fields>TASK.DUE_DATE</fields>
        <fields>TASK.STATUS</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <fields>EVENT.LOCATION</fields>
        <relatedList>RelatedActivityList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>TASK.SUBJECT</fields>
        <fields>TASK.WHO_NAME</fields>
        <fields>ACTIVITY.TASK</fields>
        <fields>FinServ__Regarding__c</fields>
        <fields>TASK.PRIORITY</fields>
        <fields>TASK.DUE_DATE</fields>
        <fields>EVENT.LOCATION</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <relatedList>RelatedHistoryList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Category__c</fields>
        <fields>Overall_Status__c</fields>
        <fields>Start_Date__c</fields>
        <relatedList>Action_Plan__c.Opportunity__c</relatedList>
        <sortField>Start_Date__c</sortField>
        <sortOrder>Desc</sortOrder>
    </relatedLists>
    <relatedLists>
        <fields>CASES.CASE_NUMBER</fields>
        <fields>CASES.SUBJECT</fields>
        <fields>CASES.CREATED_DATE</fields>
        <fields>CASES.PRIORITY</fields>
        <relatedList>Case.Opportunity__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>MemberName</fields>
        <fields>OppAccessLevel</fields>
        <fields>TeamMemberRole</fields>
        <relatedList>RelatedOpportunitySalesTeam</relatedList>
    </relatedLists>
    <relatedObjects>AccountId</relatedObjects>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00h9000000i96DW</masterLabel>
        <sizeX>3</sizeX>
        <sizeY>1</sizeY>
        <summaryLayoutItems>
            <field>AccountId</field>
            <posX>0</posX>
            <posY>0</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>CloseDate</field>
            <posX>1</posX>
            <posY>0</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>OwnerId</field>
            <posX>2</posX>
            <posY>0</posY>
        </summaryLayoutItems>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
