/**
 * <PERSON> (Accenture) SFP-61733
 */
 import { LightningElement, track, wire, api } from 'lwc';
 import { loadStyle } from "lightning/platformResourceLoader";
 
 //For navigation
 import { NavigationMixin } from 'lightning/navigation';
 
 // CSS from static resource
 import cdrCss from '@salesforce/resourceUrl/CDR_Styles';
 import CHECK_LOGO from '@salesforce/resourceUrl/check_logo';
 import ERROR_LOGO from '@salesforce/resourceUrl/error_logo';
 import WARNING_LOGO from '@salesforce/resourceUrl/warning_logo';
 import NOTIFS_LOGO from '@salesforce/resourceUrl/notifications_logo'; //CDR Notif Preferences
 
 // Toast events
 import {ShowToastEvent} from "lightning/platformShowToastEvent";
 
 // Apex controller methods
 import getJointFinancialAccounts from '@salesforce/apex/CdrDOMSLandingController.getJointFinancialAccounts';
 import doesCustomerOwnJointAccount from '@salesforce/apex/CdrDOMSLandingController.doesCustomerOwnJointAccount'; //SFP-67282
 import isLoggedInUserEligible from '@salesforce/apex/CdrDOMSLandingController.isLoggedInUserEligible'; //SFP-67282
 
 //Labels
 import jointEnableInfo from '@salesforce/label/c.DOMS_JointAcc_ThingsYouShouldKnow_Enable';
 import jointDisableInfo from '@salesforce/label/c.DOMS_JointAcc_ThingsYouShouldKnow_disable';
 import jointGeneralInfo from '@salesforce/label/c.DOMS_JointAcc_ThingsYouShouldKnow_Info';
 import whatIsDataSharing from '@salesforce/label/c.DOMS_What_is_data_sharing';
 import notEligibleForCDR from '@salesforce/label/c.DOMS_No_Joint_Account_Owned'; //SFP-67282 *not eligible for CDR
 import noJointAccountOwned from '@salesforce/label/c.DOMS_Dont_Own_Joint'; //SFP-67282
 
 
 export default class cdrDOMSLandingPage extends NavigationMixin(LightningElement) {
     @track parameters;
     @track initializeToast = false;
     @track loaded;
     @track thingsYouShouldKnowCollapsed = true;
     @track moreInformationCollapsed  = true;
     @track recentJointActionEnabled= false;
     @track recentJointActionPartialEnabled= false;
     @track availableJointFinancialAccounts = [];
     @track hasError = false;
     @track cdrNotificationsSettings = true; //SFP-66666
     @track customerOwnedJointAccount = false; //SFP-67282
     @track loggedInUserEligible = false; //SFP-67282
     @track dataSharingNotifMessage = false; //SFP-66666
 
     faDataMap = new Map();
     svgURL = `${CHECK_LOGO}#check_logo`;
     svgURLError = `${ERROR_LOGO}#error_logo`;
     svgURLWarning = `${WARNING_LOGO}#warning_logo`;
     svgURLNotification = `${NOTIFS_LOGO}#notifications_logo`; //CDR Notif Preferences
 
     label = {
         jointEnableInfo,
         jointDisableInfo,
         jointGeneralInfo,
         whatIsDataSharing,
         notEligibleForCDR,
         noJointAccountOwned
     };
 
 
     constructor() { 
         super(); 
         Promise.all([ 
             loadStyle(this, cdrCss) 
         ]).then(() => { 
             // console.log("Loaded css"); 
         }).catch(error => { 
             console.error('Error loading static resources', error); 
         }); 
     }
 
     //This is the page initialization method
     connectedCallback(){
         //console.log('connected call back 1');
 
         //When this page originates from te DOMS Manage screen after changing FAR disclosure settings, determine the page toasty message
         this.parameters = this.getQueryParameters();
         if(this.parameters){
             //console.log('Params');
             // VALUE REFERENCE 5 :(Partial disabled but other joint owner fault) 4: no user enabled   2: you enabled but others don't   3:you dont enabled but others do   1:enabled
             if(this.parameters['jsharingStatus'] == '1'){
                 this.recentJointActionEnabled = true;
             } else if (this.parameters['jsharingStatus'] == '2'){
                 this.recentJointActionPartialEnabled = true;
             } else if(this.parameters['dataSharingNotif'] == '1'){
                this.dataSharingNotifMessage = true; //SFP-66666
             }
             if(this.parameters['notif'] == '1'){
                 this.initializeToast = true;
             }
             //console.log('Status : '+ this.recentJointActionStatus);
             //console.log('Toast : '+this.initializeToast);
         }
 
         //Load the latest Joint fa details list
         //The CdrDOMSLandingController method call
         getJointFinancialAccounts()
         .then(data => {
             //console.log('data return');
             if(data){
                 //console.log('with data return');
                 this.finAcctDetailsWrapper = data;
                 //remove spinner when loaded
                 //iterate through list of wrappers passed from APEX controller
                 for(let faData of data){
                     if(!this.faDataMap.has(faData.finAcct.id)){
                         //key-> FA number and value is the each wrapper containing financial account details, availability, unavailability reasons and badge styling
                         this.faDataMap.set(faData.finAcct.Id, faData);
                         if(faData.finAcct.Bank_BSB__c != null){
                             faData.bsbLabel = faData.finAcct.Bank_BSB__c.split('').join(' ');
                         }
                         faData.accLabel = faData.finAcct.FinServ__FinancialAccountNumber__c.split('').join(' ');
 
                         //array of available FAs to be shared
                         this.availableJointFinancialAccounts.push(faData);
                     }
                 }
     
                 //split to store array of reasons for fa unavailability to be displayed in bulleted form
                 //this.unavailableFaReasonsArray = this.unavailableFaReasonsArray.split(';');
     
                 //if there are no financial accounts available, we set this variable to false in template
                 if(this.availableJointFinancialAccounts.length === 0){
                     this.availableJointFinancialAccounts = false;
                 }
                 
                 this.loaded = true;
             } else {
                 //console.log('without data return');
                 //console.log('Error fetching Financial Account information: ', error);
                 this.hasError = true;
                 console.error('Error fetching Financial Account information: ', 'No data recieved'); 
                 this.showToast('An unexpected error occurred when retrieving financial accounts', 'error', 'Please contact AMP Bank.');
             }
         }).catch(error => {
             //console.error(error); //This is needed in case there are untracebale error in JavaScript such as the profile doesn't have access to this JS
             console.error('Error fetching Financial Account information: ', error); 
             this.hasError = true;
             this.showToast('An unexpected error occurred when retrieving financial accounts', 'error', 'Please contact AMP Bank.');
         });
 
         //SFP-67282 return doesCustomerOwnJointAccount from CdrDOMSLandingController method
         doesCustomerOwnJointAccount()
         .then(result => {
                 console.log(result); 
                 this.customerOwnedJointAccount = result;
 
         }).catch(error => {
                 console.error('Error fetching Financial Account information: ', error);
                 this.hasError = true;
         });
 
         //SFP-67282 return isLoggedInUserEligible from CdrDOMSLandingController method
         isLoggedInUserEligible()
         .then(result => {
                 console.log(result); 
                 this.loggedInUserEligible = result;
 
         }).catch(error => {
                 console.error('Error fetching Financial Account information: ', error);
                 this.hasError = true;
         });
     }
 
     //SFP-66666
     handleNotificationsSettings(event) {
         event.preventDefault();
 
         this[NavigationMixin.Navigate]({
             type: 'comm__namedPage',
             attributes: {
                 pageName: 'joint-accounts-notifications-settings'
             }
         });
         return true;
 
     }
 
     //Element event action area
     handleThingsYouSHouldKnowCollapse(){
         this.thingsYouShouldKnowCollapsed = !this.thingsYouShouldKnowCollapsed;
     }
     handleMoreInformationCollapse(){
         this.moreInformationCollapsed = !this.moreInformationCollapsed;
     }
 
     //SFP-60529 DOMS
     //Go to the DOMS manage screen for a particular FA
     handleManage(event) {
         event.preventDefault();
         //Navigate to /data-sharing-arrangement?arrangement=arrangementId
         this[NavigationMixin.Navigate]({
             type: 'comm__namedPage',
             attributes: {
                 pageName: 'manage-joint-account-data-sharing'
             },
             state: {
                 'faId': event.currentTarget.dataset.id
             }
         });
         //return false;
     }
 
     //Get the URL parameters (if there's any)
     getQueryParameters() {
         var params = {};
         var search = location.search.substring(1);
         if (search) {
             params = JSON.parse('{"' + search.replace(/&/g, '","').replace(/=/g, '":"') + '"}', (key, value) => {
                 return key === "" ? value : decodeURIComponent(value)
             });
         }
         return params;
     }
 
     //Reusable error toasty
     showToast(toastTitle, toastVariant, toastMessage){
         this.dispatchEvent(
             new ShowToastEvent({
                 title: toastTitle,
                 variant: toastVariant,
                 message: toastMessage
             }),
         );
     }
 
     //Close Page success toasty
     closeToast(){
         this.initializeToast = !this.initializeToast;
     }
 
 }