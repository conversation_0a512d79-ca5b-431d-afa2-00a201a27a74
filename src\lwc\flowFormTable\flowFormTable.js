import { LightningElement, api } from 'lwc';   

export default class FlowFormTable extends LightningElement {

	
	indexTobeRemove;
	showModal = false;
	showPercentageErrorMsg = false;
	showMaxRowErrorMsg = false; 
	useEstatePercentage = false;
	showEstateSubtext = false; //SFP-63279, Steph
	useRowHeaderLabel = false;
	showAddButton = false;
	showDeleteButton = false;
	useEstateTooltip = false;
	useBenefitProportionTooltip = false;
	useTotalPercentageTooltip = false;
	isTotalSignBeforeValue = false;//SFP-59427, burhan
	showTotalErrorMsg = true;//SFP-59427, burhan, added this to avoid the need to edit the nominated beneficiary and pension payment flows, and prevent regression
	isOverrideDefaultMaxRowErrorMsg = false;//SFP-59427, burhan, added this to avoid the need to edit the nominated beneficiary and pension payment flows, and prevent regression
	showNomBenTotalPercentErrorMsg = true;//SFP-59427, burhan
	scopedNotificationIsError = false;// SFP-60533, xylene
	useStartRowHeaderWithCounter = false;//SFP-64222, steph
	mandatoryFieldsErrorMsgArr = [];
	modalTitle = '';
	modalBody = '';
	totalLabel = '';//SFP-59427, burhan
	totalErrorMsg = '';//SFP-59427, burhan
	totalSign = '';//SFP-59427, burhan
	picklistOption = [
		{
			field:"FinServ__Status__c",
			value:[
				{label:"Active",value:"Active"},
				{label:"Pending",value:"Pending"},
				{label:"Deleted",value:"Deleted"}
			]
		}
	];
	_columns = [
		{id: 1, field: "Name", label: 'Name', type: 'text', editable: false, required: true, disabled: false, readonly: false},
		{id: 2, field: "FinServ__Status__c", label: 'Status', type: 'picklist', editable: false, required: true, disabled: false, readonly: false},
		{id: 3, field: "FinServ__RenewalDate__c", label: 'Date of Birth', type: 'date', editable: false, required: false, disabled: false, readonly: false},
		{id: 4, field: "FinServ__TargetLimit__c", label: 'Percentage', type: 'number', editable: false, required: true, disabled: false, readonly: false},
		{id: 5, field: "Email", label: "Email", type: 'email', editable: true, required: false, disabled: true, readonly: true}
	];
	_inputRowKeys = '';
	_inputRowValue = '';
	_inputSecondRowValue = '';
	_defaultValuesForNewRow = '[{"field":"FinServ__TargetLimit__c","value":"1"}]';
	_isEditMode = false;
	_isEditMode2 = false;
	_hasMandatoryFieldsErrorMsg = false;
	_showTotalPercentage = true;
	_useDefaultEmptyRow = true;
	_showRowBenefitPortionErrorMsg = true;//SFP-59427, burhan, added this to avoid the need to edit the nominated beneficiary and pension payment flows, and prevent regression
	_btnAddLabel = 'Add';
	_btnDeleteLabel = 'Delete';
	_rowHeaderLabel = 'Beneficiary';
	_totalSetup = '{"label":"Total percentage:","sign":"%","isSignBeforeValue":false,"showErrorMsg":true,"errorMsg":"Benefit portion: The total percentage must equal 100%.","showNomBenTotalPercentErrorMsg":true}';//SFP-59427, burhan, added this to avoid the need to edit the nominated beneficiary and pension payment flows, and prevent regression
	_fieldForTotalComputation = 'FinServ__TargetLimit__c';//SFP-59427, burhan, renamed from _fieldForPercentage to _fieldForTotalComputation
	_dateFieldToFormat = 'Birth_Date__c';
	_deleteModalText = '{"header":"Remove","body":"Are you sure you want to remove this beneficiary?"}';//SFP-59427, burhan, added this to avoid the need to edit the nominated beneficiary and pension payment flows, and prevent regression
	_tableDelimiter = '{"column":",","row":"|"}';//SFP-59427, burhan
	_maxRowErrorMsg = 'You can nominate a maximum of 10 beneficiaries. Please review your nomination.';//SFP-59427, burhan, added this to avoid the need to edit the nominated beneficiary and pension payment flows, and prevent regression
	_estateText = '';
	_estateSubText = '';
	_benefitProportionLabel = '';
	_outputDataValues = '';
	_estateToolTip = '';
	_benefitProportionTooltip = '';
	_totalPercentageTooltip = '';
	_estatePercentage = 0;
	_totalPercentage = 0;
	_totalRowCount = 10;
	_data = [];
	_columnFieldSetup;//SFP-59427, burhan, this is a map from json input [{"field":"","minValue":"","maxValue":"","step":""},{"field":"","minValue":"","maxValue":"","step":""}]
	_headerSetup = '{"isStartWithCounter":false}';//SFP-64222, steph
	estatePercentageErrorMsg;//SFP-57187, burhan

	@api
	get headerSetup() { }

	set headerSetup(value) {
		this._headerSetup = value;
	}
	//SFP-59427, burhan
	@api
	get columnFieldSetup(){}
	
	set columnFieldSetup(value){
		this._columnFieldSetup = value;
	}

	//SFP-59427, burhan
	@api
	get maxRowErrorMsg(){}
	
	set maxRowErrorMsg(value){
		this._maxRowErrorMsg = value;
		this.isOverrideDefaultMaxRowErrorMsg = true;
	}

	//SFP-59427, burhan
	@api
	get showRowBenefitPortionErrorMsg(){}
	
	set showRowBenefitPortionErrorMsg(value){
		this._showRowBenefitPortionErrorMsg = value;
	}

	//SFP-59427, burhan
	@api
	get tableDelimiter(){}
	
	set tableDelimiter(value){
		this._tableDelimiter = value;
	}

	//SFP-59427, burhan
	@api
	get deleteModalText(){}
	
	set deleteModalText(value){
		this._deleteModalText = value;
	}

	//SFP-59427, burhan
	@api
	get totalSetup(){}
	
	set totalSetup(value){
		this._totalSetup = value;
	}

	@api
	get totalPercentageTooltip(){}
	
	set totalPercentageTooltip(value){
		this._totalPercentageTooltip = value;
		this.useTotalPercentageTooltip = true;
	}

	@api
	get benefitProportionTooltip(){}
	
	set benefitProportionTooltip(value){
		this._benefitProportionTooltip = value;
		this.useBenefitProportionTooltip = true;
	}

	@api
	get estateToolTip(){}

	set estateToolTip(value){
		this._estateToolTip = value;
		this.useEstateTooltip = true;
	}

	@api
	get useDefaultEmptyRow(){
		return this._useDefaultEmptyRow;
	}
	
	set useDefaultEmptyRow(value){
		this._useDefaultEmptyRow = value;
	}

	@api
	get showTotalPercentage(){}
	
	set showTotalPercentage(value){
		this._showTotalPercentage = value;
	}

	@api
	get dateFieldToFormat(){}

	set dateFieldToFormat(value){
		this._dateFieldToFormat = value;
	}

	@api
	get hasMandatoryFieldsErrorMsg(){
		return this._hasMandatoryFieldsErrorMsg;
	}

	set hasMandatoryFieldsErrorMsg(value){
		this._hasMandatoryFieldsErrorMsg = value;
	}

	@api
	get estateText(){}

	set estateText(value){
		this._estateText = value;
		this.useEstatePercentage = true;
	}

	@api
	get estateSubText(){}

	set estateSubText(value){
		this._estateSubText = value;
		this.showEstateSubtext = true;//SFP-63279, Steph
	}

	@api
	get benefitProportionLabel(){}

	set benefitProportionLabel(value){
		this._benefitProportionLabel = value;
	}

	@api
	get totalPercentage(){
		return this._totalPercentage;
	}
	
	@api 
	get fieldForPercentage(){}

	set fieldForPercentage(value){
		this._fieldForTotalComputation = value;//SFP-59427, burhan, renamed from _fieldForPercentage to _fieldForTotalComputation
	}

	@api
	get outputDataValues(){
		// return this._outputDataValues;
	}

	@api
	get estatePercentage(){
		// return this._estatePercentage == undefined || this._estatePercentage == '' ? 0 : this._estatePercentage;//SFP-59427, burhan, commented-out
		return !this._estatePercentage ? 0 : this._estatePercentage;//SFP-59427, burhan
	}

	set estatePercentage(value){
		this._estatePercentage = value;
	}

	@api 
	get totalRowCount(){
		// return this._data.length;//SFP-57496, burhan, commented-out
	}

	set totalRowCount(values){
		this._totalRowCount = values;
	}

	//SFP-57496, burhan
	@api 
	get getCurrentRowCount(){
		return this._data.length;
	}

	@api
	get btnAddLabel(){}

	set btnAddLabel(value){
		this._btnAddLabel = value;
		this.showAddButton = true;
	}

	@api
	get btnDeleteLabel(){}

	set btnDeleteLabel(value){
		this._btnDeleteLabel = value;
		this.showDeleteButton = true;
	}

	@api
	get inputPicklistOptions(){}

	set inputPicklistOptions(value){
		this.picklistOption = JSON.parse(value);
	}

	@api 
	get inputJSON(){}

	set inputJSON(value){
		if(value){
			this._data = JSON.parse(value);
		}
	}

	@api
	get inputColumns(){}

	set inputColumns(value){
		this._columns = JSON.parse(value);
	}

	@api
	get rowHeaderLabel(){}

	set rowHeaderLabel(value){
		this._rowHeaderLabel = value;
		this.useRowHeaderLabel = true;
	}

	@api
	get inputRowKeys(){}

	set inputRowKeys(value){
		this._inputRowKeys = value;
	}

	@api
	get inputRowValue(){
		return this._inputRowValue;//SFP-56277, burhan
	}

	set inputRowValue(value){
		this._inputRowValue = value;
	}

	@api
	get defaultValuesForNewRow(){}

	set defaultValuesForNewRow(value){
		this._defaultValuesForNewRow = value;
	}

	@api
	get isEditMode(){}

	set isEditMode(value){
		this._isEditMode = value;
	}

	@api
	get resultData(){
		return JSON.stringify(this._data);
	}

	@api //ren added
	get inputSecondRowValue(){
		return this._inputSecondRowValue;
	}
	
	set inputSecondRowValue(value){
		this._inputSecondRowValue = value;
	}
	
	
	get showHideTotalPercentage(){
		return this._showTotalPercentage && (this._data.length > 0 || this.useEstatePercentage);
	}
	//SFP-63279, Steph
	get _tdClass(){
		return this._isEditMode ? "nopaddingside" : "nopaddingside borderSetup";
	}
	//SFP-63279, Steph
	get _rowHeaderStyle(){
		console.log('steph data length',this._data.length);
		return this._data.length > 1 && !this._isEditMode ? "nopaddingside pHeaderStyle rowHeaderStyle rowHdrStyle" : "nopaddingside pHeaderStyle rowHeaderStyle";
	}
	get _updateToNoPadding(){ 
		return (this.showPercentageErrorMsg && this.showHideTotalPercentage) || this.showMaxRowErrorMsg ? "noPaddingStyle" : "addPadding";
	}
	get _estateTextStyle(){
		return this._isEditMode ? "slds-form-element__label slds-no-flex slds-float_left pHeaderStyle addPadding" : "slds-form-element__label slds-no-flex slds-float_left pHeaderStyle spaceStyle";
	}

    connectedCallback() {
		try {
			//console.log('bn connectedCallback this.isOverrideDefaultMaxRowErrorMsg',this.isOverrideDefaultMaxRowErrorMsg);
			//SFP-59427, burhan, added this to avoid the need to edit the nominated beneficiary and pension payment flows, and prevent regression
			if(!this.isOverrideDefaultMaxRowErrorMsg){
				this._maxRowErrorMsg = 'You can nominate a maximum of '+this._totalRowCount+' beneficiaries. Please review your nomination.'
				//console.log('bn connectedCallback this._maxRowErrorMsg',this._maxRowErrorMsg);
			}

			//console.log('bn connectedCallback this._columnFieldSetup',this._columnFieldSetup);
			//SFP-59427, burhan, added this to avoid the need to edit the nominated beneficiary and pension payment flows, and prevent regression, normally minValue, maxValue and step should be added in column json
			if(this._columnFieldSetup){
				let columnFieldSetupMap = JSON.parse(this._columnFieldSetup);
				//console.log('bn connectedCallback this._columns1',this._columns);
				//console.log('bn connectedCallback columnFieldSetupMap',columnFieldSetupMap);
				if(columnFieldSetupMap){
					let tempArr = this._columns.map(e => {
						columnFieldSetupMap.forEach(j => {
							//insert additional attributes to map/object element
							if(e.field == j.field){
								e.minValue = j.minValue;
								e.maxValue = j.maxValue;
								e.step = j.step;
							}
						});
						
						return e;
					});

					this._columns = [...tempArr];
				}
				//console.log('bn connectedCallback this._columns2',this._columns);
			}

			//SFP-64222, steph
			this.useStartRowHeaderWithCounter = false;
			if(this._headerSetup){
				let headerSetupMap = JSON.parse(this._headerSetup);
				this.useStartRowHeaderWithCounter = headerSetupMap.isStartWithCounter;
			}

			//console.log('bn connectedCallback this._totalSetup',this._totalSetup);
			//SFP-59427, burhan
			let totalSetupMap = JSON.parse(this._totalSetup);
			this.totalLabel = totalSetupMap.label;
			this.totalSign = totalSetupMap.sign;
			this.isTotalSignBeforeValue = totalSetupMap.isSignBeforeValue;
			this.showTotalErrorMsg = totalSetupMap.showErrorMsg;
			this.totalErrorMsg = totalSetupMap.errorMsg;
			this.showNomBenTotalPercentErrorMsg = totalSetupMap.showNomBenTotalPercentErrorMsg;
			this.scopedNotificationIsError = true;//SFP-60533, xylene

			//console.log('bn connectedCallback this._tableDelimiter',this._tableDelimiter);
			let tableDelimiterMap = JSON.parse(this._tableDelimiter);
			//console.log('bn connectedCallback tableDelimiterMap',tableDelimiterMap);
			let rowKeys = this._inputRowKeys.split(',');
			let groupArr = this._inputRowValue.split(tableDelimiterMap.row);//SFP-59427, burhan, changed pipe (|) to tableDelimiterMap.row
			let counter = 1;
			let firstWord = rowKeys[1];

     		let SecondgroupArr = this._inputSecondRowValue.split(tableDelimiterMap.row);
			 console.log('bn connectedCallback this._inputSecondRowValue',this._inputSecondRowValue);

			
			let SecondtempArr = [];
		    this._data = [];
              
		
		 //Ren added--------------------------------------
		 SecondgroupArr.forEach(e => {
			let rowHeaderLabel = this._rowHeaderLabel.concat(' ');

				//SFP-64222, steph
				if(this.useStartRowHeaderWithCounter && SecondgroupArr.length == 1){
					rowHeaderLabel = rowHeaderLabel.concat(counter);
					console.log(rowHeaderLabel + 'rowHeaderLabel 444') ;//VGSPEQ
					console.log(counter + 'counter 444') ;//VGSPEQ
				}

			if(e.length > 1){
				let indivArr = e.split(tableDelimiterMap.column);
				let SecondtempMap = {}
				

				for(let i=1; i<rowKeys.length; i++){
					let id = indivArr[0];
					let key = rowKeys[i];
					let val = indivArr[i];

					
					if(key == this._dateFieldToFormat && val && val.includes(' ')){
							//console.log('bn connectedCallback date val',val);

							//SFP-60758, burhan, commented-out
							// let tempVal = new Date(val).toLocaleString('en-AU');
							// let tempArr = tempVal.split(',');
							// if(tempArr && Array.isArray(tempArr) && tempArr[0]){
							// 	val = tempArr[0];
							// }

							//SFP-60758, burhan
							let date = new Date(val);
							let year = date.getFullYear();
							let month = date.getMonth()+1;
							let dt = date.getDate();

							if (dt < 10) {
								dt = '0' + dt;
							}
							if (month < 10) {
								month = '0' + month;
							}

							val = year+'-' + month + '-'+dt;

							//console.log('bn connectedCallback date val2',val);
						}
						//console.log('bn connectedCallback val1',val);
						//SFP-57500, burhan
						// if(val == undefined || val == 'undefined'){//SFP-59427, burhan, commented-out
						if(!val){//SFP-59427, burhan
							val = '';
						}
						//console.log('bn connectedCallback val2',val);
						
						SecondtempMap = {
							id: id, 
							key: key, 
							value: val
						};

							SecondtempArr= [...SecondtempArr, SecondtempMap];
				}

				     console.log('3rd valueeeeeeeeeeeeeee',SecondtempArr);


					//this._mdata = [...this._mdata, {SecondValue: SecondtempArr}];
				}
				// SFP-78102 Bruce Hernandez
				counter++;


			});

		  //Ren added----------------------------------------
		
		  groupArr.forEach(e => {
			let rowHeaderLabel = this._rowHeaderLabel.concat(' ');

				//SFP-64222, steph
				if(this.useStartRowHeaderWithCounter && groupArr.length == 1){
					rowHeaderLabel = rowHeaderLabel.concat(counter);
				}

			if(e.length > 1){
				let indivArr = e.split(tableDelimiterMap.column);//SFP-59427, burhan, changed comma (,) to tableDelimiterMap.column
				let tempMap = {}
				let tempArr = [];

				for(let i=1; i<rowKeys.length; i++){ //VGSPEQ
					let id = indivArr[0];
					let key = rowKeys[i];
					let val = indivArr[i];

					if(key == this._dateFieldToFormat && val && val.includes(' ')){
							//console.log('bn connectedCallback date val',val);

							//SFP-60758, burhan, commented-out
							// let tempVal = new Date(val).toLocaleString('en-AU');
							// let tempArr = tempVal.split(',');
							// if(tempArr && Array.isArray(tempArr) && tempArr[0]){
							// 	val = tempArr[0];
							// }

							//SFP-60758, burhan
							let date = new Date(val);
							let year = date.getFullYear();
							let month = date.getMonth()+1;
							let dt = date.getDate();

							if (dt < 10) {
								dt = '0' + dt;
							}
							if (month < 10) {
								month = '0' + month;
							}

							val = year+'-' + month + '-'+dt;

							//console.log('bn connectedCallback date val2',val);
						}
						//console.log('bn connectedCallback val1',val);
						//SFP-57500, burhan
						// if(val == undefined || val == 'undefined'){//SFP-59427, burhan, commented-out
						if(!val){//SFP-59427, burhan
							val = '';
						}
						//console.log('bn connectedCallback val2',val);
						
						tempMap = {
							id: id, 
							key: key, 
							value: val
						};

					tempArr = [...tempArr, tempMap];
				}

					if ((groupArr.length - 1) >= 1) {
					rowHeaderLabel = rowHeaderLabel.concat(counter-1);
					console.log(rowHeaderLabel + '579 rowheaderlabel'); //VGSPEQ
				}

					this._data = [...this._data, {rowHeaderLabel: rowHeaderLabel, value: tempArr, SecondValue: SecondtempArr}];
				}
				counter++;
				console.log(counter + '585 counter'); //VGSPEQ
			});
			//console.log('bn connectedCallback this._data',this._data);

		this.updateTotalPercentage();
		this.updteOutputDataValues();

			if(this._isEditMode){
				this.updateFieldValidation();
				
				if(this._data.length == 0 && this._useDefaultEmptyRow){
					this._useDefaultEmptyRow = false;//set to false so that it wont run again when returned to same screen for other validation errors
					this.handleAdd();
				}
			}
		} catch (error) {
			console.error('bn connectedCallback error',error);
		}
    }

    handleOnChangeInput(event) {
		let objectMap = JSON.parse(JSON.stringify(event.detail));
        this._data.forEach(e => {
			let element = e.value.find(j => j.id == objectMap.id && j.key == objectMap.key);
			if(element){
				element.value = objectMap.value;
			}
		});
		
		this.updateTotalPercentage();
		this.updateFieldValidation();
		this.updteOutputDataValues();
    }

	handleAdd() {
		if(this._data.length >= this._totalRowCount){
			this.showMaxRowErrorMsg = true;
			return;
		}

		let inputJSON;
		let defaultValueArr;
		let row = [];
		let cell = {};
		let tempId = 'new'.concat(Math.random().toString(36).substring(2, 15));
		let rowKeys = this._inputRowKeys.split(',');
		let updateRowHeader = false;

		let rowHeaderLabel = this._rowHeaderLabel;
			
		if(this._data.length > 0){
			updateRowHeader = true;
		}

		if (this.useStartRowHeaderWithCounter && this._data.length == 0) {
			updateRowHeader = true;
		}

		if(this._defaultValuesForNewRow){
			inputJSON = JSON.parse(this._defaultValuesForNewRow);
		}

		rowKeys.forEach(e => {
			if(e.toLowerCase() != 'id'){
				let defaultValue;

				if(inputJSON && Array.isArray(inputJSON)){
					defaultValueArr = inputJSON.filter(j => e == j.field);

					if(defaultValueArr && defaultValueArr[0]){
						defaultValue = defaultValueArr[0].value;
					}
				}

				cell = {
					id: tempId,
					key: e,
					value: defaultValue
				};
			
				row = [...row, cell];
			}
		});

		this._data = [...this._data, {rowHeaderLabel:rowHeaderLabel, value: row}];

		this.updateTotalPercentage();
		this.updateFieldValidation();
		this.updteOutputDataValues();

		if(updateRowHeader){
			this.updateRowHeader();
		}
	}

    handleRemove(event) {
        this.indexTobeRemove = event.target.name;
		
		let textMap = JSON.parse(this._deleteModalText);//SFP-59427, burhan

		this.modalTitle = textMap.header;//SFP-59427, burhan
		this.modalBody = textMap.body;//SFP-59427, burhan
		this.showModal = true;
    }

	handleConfirmDelete(){
		if(this._data.length > 0 && this._data[this.indexTobeRemove]){
			let tempList = JSON.parse(JSON.stringify(this._data));

			if(tempList && tempList[this.indexTobeRemove]){
				this._data = [];
				tempList.splice(this.indexTobeRemove, 1);
				
				//sleep 1 milli second just to give time for rerendering
				setTimeout(() => {
					this._data = [...tempList];
					this.showModal = false;
					
					if(this._data.length <= this._totalRowCount){
						this.showMaxRowErrorMsg = false;
					}
					
					this.updateTotalPercentage();
					this.updateFieldValidation();
					this.updteOutputDataValues();
					this.updateRowHeader();//call this last because it will call setTimeOut for this._data to force the table to rerender
				}, 1);
			}
		}
	}

	updateFieldValidation(){
		this.mandatoryFieldsErrorMsgArr = [];
		this._hasMandatoryFieldsErrorMsg = false;

		let tempArr = [];
		let tempStr = '';
		
		this._data.forEach(e => {
			e.value.forEach(j => {
				let key = j.key;
				let value = j.value;
				let col = this._columns.filter(k => k.field == key);
				if(col && col[0]){
					// SFP-56301, burhan
					if(col[0].type == 'picklist'){
						//set value to blank for picklist value not found in options
						let useBlank = true;
						let picklistOptions = this.picklistOption.filter(e => e.field == key);
						if(picklistOptions && picklistOptions[0]){
							picklistOptions[0].value.forEach(e => {
								 if(value != null){
									if(e.value.trim() == value.trim()){
										useBlank = false;
									}
								 }
								
							});
						}

						if(useBlank){
							value = undefined;
						}
					}

					// if((value == undefined || value == 'undefined' || value == '') && (col[0].required == true || col[0].required == 'true')){//SFP-59427, burhan, commented-out
					if((!value) && (col[0].required == true || col[0].required == 'true')){//SFP-59427, burhan
						tempStr = "All "+col[0].label+" fields are required!";

						if(col[0].label=="Please specify the expense type(s):" || col[0].label=="Amount ($)"){
							tempStr = "All "+'"'+col[0].label+'"'+" fields are required!";
							console.log('bn connectedCallback this._columns1rennnnnnnnnnnn',tempStr);


						}
						
						
						
						
						if(!tempArr.includes(tempStr)){
							tempArr.push(tempStr);
							this.mandatoryFieldsErrorMsgArr.push(tempStr);
							this._hasMandatoryFieldsErrorMsg = true;
						}
					}

					//benefit portion must not be less than 1
					if(this._showRowBenefitPortionErrorMsg && key == this._fieldForTotalComputation && value && parseInt(value) < 1){//SFP-59427, burhan, renamed from _fieldForPercentage to _fieldForTotalComputation, added this._showRowBenefitPortionErrorMsg
						tempStr = 'All '+col[0].label+ ' must not be less than 1%!';
						if(!tempArr.includes(tempStr)){
							tempArr.push(tempStr);
							this.mandatoryFieldsErrorMsgArr.push(tempStr);
							this._hasMandatoryFieldsErrorMsg = true;
						}
					}

					// SFP-57187, burhan, benefit portion must be whole number only (legal)
					if(key == this._fieldForTotalComputation && value && value.includes('.')){//SFP-59427, burhan, renamed from _fieldForPercentage to _fieldForTotalComputation
						tempStr = 'Invalid input for '+col[0].label+ '!';
						if(!tempArr.includes(tempStr)){
							tempArr.push(tempStr);
							this.mandatoryFieldsErrorMsgArr.push(tempStr);
							this._hasMandatoryFieldsErrorMsg = true;
						}
					}

					//date must not be lower than 1900-01-01
					if(key == this._dateFieldToFormat && value){
						// const MS_PER_DAY = 1000 * 60 * 60 * 24;
						const a = new Date("1900-01-01");
						if(value.includes('/')){
							let tempArr = value.split('/');
							if(tempArr[0] && tempArr[1] && tempArr[2]){
								value = tempArr[2] + '-' + tempArr[1] + '-' + tempArr[0];
							}
						}
						let b = new Date(value);
						const utc1 = Date.UTC(a.getFullYear(), a.getMonth(), a.getDate());
						const utc2 = Date.UTC(b.getFullYear(), b.getMonth(), b.getDate());

						// let result = Math.floor((utc2 - utc1) / MS_PER_DAY);
						let result = Math.floor(utc2 - utc1);
						if(result < 0){
							tempStr = 'All '+col[0].label+' must not be earlier than 01/01/1900!';
							if(!tempArr.includes(tempStr)){
								tempArr.push(tempStr);
								this.mandatoryFieldsErrorMsgArr.push(tempStr);
								this._hasMandatoryFieldsErrorMsg = true;
							}
						}
					}
				}
			});
		});
	}

	updteOutputDataValues(){
		try {
			//console.log('bn updteOutputDataValues this._tableDelimiter',this._tableDelimiter);
			//SFP-59427, burhan
			let tableDelimiterMap = JSON.parse(this._tableDelimiter);
			//console.log('bn updteOutputDataValues tableDelimiterMap',tableDelimiterMap);

		this._outputDataValues = '';
		this._data.forEach(e => {
			this._outputDataValues = this._outputDataValues + e.value[0].id;
			e.value.forEach(j => {
				let key = j.key;
				let val = j.value;

				//SFP-56286, burhan
				//convert yyyy-MM-dd back to dd/MM/yyyy
				if(key == this._dateFieldToFormat && val && val.includes('-')){
					let tempArr = val.split('-');
					if(tempArr[0] && tempArr[1] && tempArr[2]){
						val = tempArr[2] + '/' + tempArr[1] + '/' +tempArr[0];
					}
				}

				//SFP-59427, burhan
				if(!val){
					val = '';
				}

				// this._outputDataValues = this._outputDataValues + ',' + val;//SFP-59427, burhan, commented-out
				this._outputDataValues = this._outputDataValues + tableDelimiterMap.column + val;//SFP-59427, burhan
			});

				// this._outputDataValues = this._outputDataValues + '|';//SFP-59427, burhan, commented-out
				this._outputDataValues = this._outputDataValues + tableDelimiterMap.row;//SFP-59427, burhan
			});
			this._inputRowValue = this._outputDataValues;//SFP-56277, burhan
			//console.log('bn updteOutputDataValues this._outputDataValues',this._outputDataValues);
		} catch (error) {
			console.error('bn updteOutputDataValues error',error);
		}
	}

	updateTotalPercentage(){
		try {
			this._totalPercentage = 0;
			this.estatePercentageErrorMsg = undefined;//SFP-57187, burhan
			//console.log('bn updateTotalPercentage this._fieldForTotalComputation',this._fieldForTotalComputation);

			this._data.forEach(e => {
				e.value.forEach(j => {
					if(j.key == this._fieldForTotalComputation){//SFP-59427, burhan, renamed from _fieldForPercentage to _fieldForTotalComputation
						// let targetValue = j.value == undefined || j.value == '' ? 0 : j.value;//SFP-59427, burhan, commented-out
						let targetValue = !j.value ? 0 : j.value;//SFP-59427, burhan
						// this._totalPercentage = parseInt(this._totalPercentage) + parseInt(targetValue);//SFP-59427, burhan, commented-out
						this._totalPercentage = parseFloat(this._totalPercentage) + parseFloat(targetValue);//SFP-59427, burhan, this will include decimal places in row level
					}
				});
			});
			//console.log('bn updateTotalPercentage this._totalPercentage1',this._totalPercentage);

			// let estatePercentage = this._estatePercentage == undefined || this._estatePercentage == '' ? 0 : this._estatePercentage;//SFP-59427, burhan, commented-out
			let estatePercentage = !this._estatePercentage ? 0 : this._estatePercentage;//SFP-59427, burhan
			// this._totalPercentage = parseInt(this._totalPercentage) + parseInt(estatePercentage);//SFP-57187, burhan, commented-out
			this._totalPercentage = parseFloat(this._totalPercentage) + parseFloat(estatePercentage);//SFP-57187, burhan
			//console.log('bn updateTotalPercentage this._totalPercentage2',this._totalPercentage);

		//SFP-57187, burhan, estate benefit portion must be whole number only
		if(estatePercentage.toString().includes('.')){
			this.estatePercentageErrorMsg = 'Invalid input for Estate Benefit portion!';
		}

			if(this._totalPercentage != 100){
				this.showPercentageErrorMsg = true;
			}else{
				this._totalPercentage = parseInt(this._totalPercentage)//SFP-57187, burhan
				this.showPercentageErrorMsg = false;
			}

			//SFP-60485, burhan
			if(this._totalPercentage){
				this._totalPercentage = this.numberWithCommas(this._totalPercentage);
			}
		} catch (error) {
			console.error('bn updateTotalPercentage error',error);
		}
	}
	
	updateRowHeader() {
		if(this._data.length == 0){
			return;
		}

		let counter = 1;
		let cloneList = JSON.parse(JSON.stringify(this._data));
		let tempList = [];
		this._data = [];

		cloneList.forEach(e => {
			console.log(counter + 'line 915 updateRowHeader' ); //SFP-78102 vgspeq
			let rowHeaderLabel = this._rowHeaderLabel.concat(' ');

			//SFP-64222, steph
			if(this.useStartRowHeaderWithCounter && cloneList.length == 1){
				rowHeaderLabel = rowHeaderLabel.concat(counter);
			}

			if(cloneList.length > 1){
				rowHeaderLabel = rowHeaderLabel.concat(counter);
			}
			
			tempList = [...tempList, {rowHeaderLabel: rowHeaderLabel, value: e.value}];
			console.log(e.value + 'line 928 updateRowHeader' ); //SFP-78102 vgspeq v3
			counter++;
		});

		setTimeout(() => {
			this._data = [...tempList];
		}, 1);
	}

	handleBenefitEstateChange(event){
		this._estatePercentage = event.target.value;
		this.updateTotalPercentage();
	}

	handleCloseModal(){
		this.showModal = false;
	}

	// SFP-60485, burhan
	numberWithCommas(value) {
		// return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
		return parseFloat(value).toLocaleString("en-AU");
	}
}