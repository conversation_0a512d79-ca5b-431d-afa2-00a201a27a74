import { LightningElement, api } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { loadScript } from 'lightning/platformResourceLoader';
import lodash from '@salesforce/resourceUrl/lodash';
import AMP_Util from '@salesforce/resourceUrl/AMP_Util';

export default class InteractionCreateDocumentEditor extends LightningElement {

    @api outcomeToTemplateData;
	@api availableOutcomes;

    @api availableOutcomeOptions = [];
    @api value = []; // Outcome to email template configuration in array form.

	isMiniModalVisible;
	
	tableData = [];
    outcomeOptions = []; // All available outcome options

	connectedCallback() {
		Promise.all([
			loadScript(this, lodash),
			loadScript(this, AMP_Util)
		]).then(() => {
        	this.scriptsLoaded();
		});
    }

	scriptsLoaded(){
        let outcomeDictionary = _util.tryParseJSON(this.availableOutcomes); // e.g. {"A":{"label":"Accepted","order":0},"B":{"label":"Declined","order":1}}
        if (!outcomeDictionary) outcomeDictionary = {};
        
		let outcomeOptions = [];
        for (let outcomeValue in outcomeDictionary) {
            let outcome = outcomeDictionary[outcomeValue];
            outcomeOptions.push({ "value": outcomeValue, "label": outcome.label, "order": outcome.order});
        }
        // Build outcome options
		let NO_OUTCOME_OPTION = {
			value: 'none', 
			label: 'No outcome selected', 
			order: 998, 
			description: 'Associated conga template will be applied if no outcome is selected.'
		}

		let ANY_OUTCOME_OPTION = {
			value: 'any', 
			label: 'Any outcome selected', 
			order: 999, 
			description: 'Associated conga template will be applied if an outcome has no conga template configured.'
		}
        outcomeOptions = [ ...outcomeOptions, ANY_OUTCOME_OPTION, NO_OUTCOME_OPTION ];
        outcomeOptions.sort((a,b) => a.order - b.order);
        this.availableOutcomeOptions = [...outcomeOptions];
        
		// Parse data
        let outcomeToTemplateData = _util.tryParseJSON(this.outcomeToTemplateData);
        if (!outcomeToTemplateData) outcomeToTemplateData = [];
        let tableData = outcomeToTemplateData.map(origRow =>{
            let row = _.cloneDeep(origRow);
            let outcomeOption = outcomeOptions.find(option => option.value === row.outcome);
            if(outcomeOption){
                row.outcomeLabel = outcomeOption.label;
                //  remove from available options
                this.availableOutcomeOptions = this.availableOutcomeOptions.filter(option =>{
                    if(option.value !== row.outcome) return option;
                });
            }else{
                row.outcomeLabel = row.outcome;
            }
            return row;
        });

		this.tableData = tableData;
        this.value = outcomeToTemplateData;
        this.outcomeOptions = outcomeOptions;
	}
	

	handleMiniModalOpen(){
        if(this.availableOutcomeOptions.length < 1){
            this.showToast('Cannot add new row', 'All possible outcomes have been selected.', 'error', 'dismissible');
        }else{
            this.isMiniModalVisible = true;
        }
	}

	handleMiniModalClose(){
		this.isMiniModalVisible = false;
	}

    handleOutcomeSelected(event){
        let tableData = this.tableData;
        let availableOutcomeOptions = this.availableOutcomeOptions;
        
        let selection = event.currentTarget.dataset.value;
        let selectedOption = this.getOutcomeOption(this.outcomeOptions, selection);

        // update table data
        tableData.push({'outcome': selectedOption.value, 'outcomeLabel': selectedOption.label, 'congatemplates': []});
        this.tableData = tableData;

        
        this.value.push({'outcome': selectedOption.value, 'congatemplates': []});
        
        // remove selected option from options
        availableOutcomeOptions = availableOutcomeOptions.filter(option =>{
            if(option.value !== selection) return option;
        });

        this.availableOutcomeOptions = availableOutcomeOptions;
        this.isMiniModalVisible = false;
    }

    getOutcomeOption(outcomeOptions, outcome){
        return outcomeOptions.find(option => option.value === outcome);
    }

    showToast(title, message, variant, mode) {
        const event = new ShowToastEvent({
            title: title,
            message: message,
            variant: variant,
            mode: mode
        });
        this.dispatchEvent(event);
    }

    handleTemplateChange(event) {
        let selectedValueIndex = (this.value != undefined && this.value) ? this.value.findIndex(option => option.outcome === event.detail.outcome) : -1;

        // check if the event.detail is inside this.value
        if(selectedValueIndex >= 0){
            //Update object's conga templates property.
            this.value[selectedValueIndex].congatemplates = event.detail.congatemplates;
        }else{
            this.value.push({outcome: event.detail.outcome, congatemplates: event.detail.congatemplates});
        }
    }

    handleTableChange(event) {
        // remove element
        let tempTableData = this.tableData.filter( obj => obj.outcome !== event.detail.outcome.outcome);
        this.tableData = tempTableData;

        let tempValue = this.value.filter(obj => obj.outcome !== event.detail.outcome.outcome);
        this.value = tempValue;

        // add the outcome back to the options
        if(!this.availableOutcomeOptions.find(option => option.value === event.detail.outcome.outcome)){
            let outcome = this.getOutcomeOption(this.outcomeOptions, event.detail.outcome.outcome);
            if(outcome){ // only add back defined outcome
                this.availableOutcomeOptions.push(outcome);
                this.availableOutcomeOptions.sort((a,b) => a.order - b.order);
            }
        }
    }
}