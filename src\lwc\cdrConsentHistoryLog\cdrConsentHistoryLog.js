// cdrConsentHistoryLog.js
import { LightningElement, track, api } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import { loadStyle } from "lightning/platformResourceLoader";
import cdrCss from '@salesforce/resourceUrl/CDR_Styles';
import getScopeRecords from '@salesforce/apex/CdrLandingDashboardController.getScopeRecords';

export default class CdrConsentHistoryLog extends NavigationMixin(LightningElement) {
 
    @api value;
    @track arrangement;
    @track parameters = {};
    @track error;
    scopeRecords = []; // Array to store fetched records
    isRecordFound = false;
    loaded = false;
  

    constructor() {
        super();
        Promise.all([
            loadStyle(this, cdrCss)
        ]).then(() => {
            // Styles loaded successfully
        }).catch(error => {
            console.error('Error loading static resources', error);
        });
    }

    connectedCallback() {
        this.loaded = false;
        
        this.parameters = this.getQueryParameters();
        if(this.parameters){
            this.arrangement = this.parameters.arrangement;
        }
        this.fetchScopeRecords();
    }

    fetchScopeRecords() {
        getScopeRecords({ consentArrangementId: this.arrangement })
            .then(result => {
                this.scopeRecords = result.map(record => ({
                    id: record.Id,
                    startDate: this.formatDate(new Date(record.Start_Date__c)),
                    endDate: this.formatDate(new Date(record.End_Date__c))
                }));

                this.loaded = true;

                if (this.scopeRecords.length > 0) 
                    this.isRecordFound = true;
                else
                    this.isRecordFound = false;
            })
            .catch(error => {
                console.error('Error fetching records:', error);
                this.isRecordFound = false;
            });
    }

    // Format Date
    formatDate(date) {
        if (!date) return '';
        const day = date.getDate();
        const month = date.toLocaleString('default', { month: 'long' });
        const year = date.getFullYear();
        return `${day} ${month} ${year}`;
    }

    handleDateClick(event) {
        event.preventDefault();
        this[NavigationMixin.Navigate]({
            type: 'comm__namedPage',
            attributes: {
                pageName:'consent-history-detail'
            },
            state: {
                'scopeId': event.currentTarget.dataset.id,
                'arrangement': this.arrangement
            }
        });
        return false;
    }

    getQueryParameters() {
        let params = {};
        let search = location.search.substring(1);
        if (search) {
            params = JSON.parse('{"' + search.replace(/&/g, '","').replace(/=/g, '":"') + '"}', (key, value) => {
                return key === "" ? value : decodeURIComponent(value)
            });
        }
        return params;
    }
}