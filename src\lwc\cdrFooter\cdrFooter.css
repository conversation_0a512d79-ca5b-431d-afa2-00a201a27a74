.bottom { 
    padding-top: 12px;
    padding-bottom: -2px;
}

.content__body-footer a.footer__links{
    font-size:14px;
    color: #3B4E98;
    padding-left: 0;
}

.copyright-text {
    color: #525252;
    padding-bottom: -2px;
}

.bottom__links-right {
    color: #525252;
}

.top__right-links__yt {
    padding-right: 0px;
}

.top__right-links{
    float: right;
}

.divider{
    color: #E6E7E8;
}

/* Create two equal columns that floats next to each other */
.column {
    float: left;
    width: 50%;
}

/* Clear floats after the columns */
.row:after {
    content: "";
    display: table;
    clear: both;
}

.icons_link {
    min-width: 44px;
    min-height: 44px;
}

/* Responsive layout - makes the two columns stack on top of each other instead of next to each other */
@media screen and (max-width: 500px) {
    .column {
      width: 100%;
    }
    .bottom__right{
        text-align: left;
    }
    .top__right {
        padding-top: 16px;
    }
    .top__left {
        padding-top: 16px;
        padding-bottom: 4px;
    }
    .top{
        padding-bottom: 12px;
    }
    .top__right-links__fb {
        padding-left: 0px;
    }
    .top__right-links{
        float: none;
    }
}