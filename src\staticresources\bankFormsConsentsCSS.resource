/** CSS for Bank Forms Consents */

@font-face {
    font-family: 'hurmeFont';
    src: url('/sfsites/c/resource/Hurme_Font') format('woff');
}



/**CSS Outside the form**/
.siteforceStarterBody .cCenterPanel {
    padding: 0;
    margin-top: -12px;
    max-width: 100% !important;

}

.siteforceContentArea .comm-layout-column {
    padding: 0;
}

.backgroundColor-grey {
    background-color: #F5F5F5;
    text-align: center;
    border-color: #f5f5f5;
    min-height: 77.5vh;
    max-width: 628px;
    margin: auto;
    padding: 0;
}

.uiModal--flowruntime .slds-modal__container {
    max-width: 680px;
}

@media (min-width: 48em) {
    .modal-container.slds-modal__container {
        max-width: 60rem;
    }
}

/**CSS Inside the formContainer**/
.formContainer {
    background-color: white;

    text-align: left;
    padding: 24px;
}

.slds-scoped-notification_light {
    background-color: #F5F5F5;
}

.header-style {
    font-family: hurmeFont;
    font-style: normal;
    font-weight: 400;
    font-size: 34px;
    line-height: 52px;
    text-align: center;
    color: #343538;
    padding-bottom: 24px;
}

.subheader-style {
    font-family: hurmeFont;
    font-style: normal;
    font-weight: 400;
    font-size: 24px;
    line-height: 36px;
    text-align: left;
    color: #001E41;
    padding-bottom: 16px;
}

.heading-style {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    color: #001E41;
}

.pbody-style,
.pbody-style span p {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: #525252;

    max-width: 100%;
}



.scoped-notification-style {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 21px;
    color: #080707;
}

.footer-button-style {
    text-align: right;
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    padding-top: 16px;
    padding-right: 0;
}

.close-button-style {
    width: 90px;
    height: 44px;
}

/** checkbox **/
.slds-checkbox .slds-checkbox__label .slds-form-element__label {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 21px;
    color: #3E3E3C;

}

.slds-checkbox .slds-checkbox_faux,
.slds-checkbox .slds-checkbox--faux {
    width: 16px;
    height: 16px;
}

.slds-checkbox [type=checkbox]:checked+.slds-checkbox_faux:after,
.slds-checkbox [type=checkbox]:checked+.slds-checkbox--faux:after,
.slds-checkbox [type=checkbox]:checked~.slds-checkbox_faux:after,
.slds-checkbox [type=checkbox]:checked~.slds-checkbox--faux:after,
.slds-checkbox [type=checkbox]:checked+.slds-checkbox__label .slds-checkbox_faux:after,
.slds-checkbox [type=checkbox]:checked+.slds-checkbox__label .slds-checkbox--faux:after {
    height: 4px;
    width: 9px;
}

.slds-button.buttonSpace{
    margin-left: 16px;
}

/** lightning-card css start**/
.card-container-style .slds-card {
    box-shadow: 0px 1px 2px rgb(0 0 0 / 6%), 0px 1px 3px rgb(0 0 0 / 10%);
    background: #FFFFFF;
    padding: 24px;
    border-style: none;

}

.card-container-style {
    margin-bottom: 24px;
}

.card-container-style .slds-card__header {
    padding: 0;
    margin: 0;
}

.card-container-style .slds-card__body {
    padding: 0;
    margin: 0;
}

.card-container-style .slds-card__body p {
    padding: 0;
}

/** lightning-card css end**/

/** table css start**/

.table-heading-style {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    color: #001E41;
    padding-bottom: 24px;
}

.slds-rich-text-editor__textarea table,
.slds-rich-text-editor__output table {
    width: 100%;
}

table {
    border-color: white;
    margin-bottom: 24px;

}

table tr {
    border-bottom-color: #C9C7C5;
    border-bottom-style: solid;
    border-bottom-width: 0.5px;
    border-top-color: #C9C7C5;
    border-top-style: solid;
    border-top-width: 0.5px;
}

table tr td:first-child {
    background: #F5F5F5;
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 21px;
    color: #525252;
    width: 50%;
}

table tr td:last-child {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 21px;
    color: #525252;
}

/** table css end**/
/** accordion css start **/
.slds-accordion__summary-heading {
    display: flex;
    flex-grow: 1;
    min-width: 0;
    margin-bottom: 0;
}

.slds-accordion__summary-action {
    /**margin-top: -8px;
	margin-left: -10px;**/
    padding: 10px;

}

.slds-accordion__summary-action-icon {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 21px;
    color: #000000;
}

.slds-accordion__summary {
    /**margin-top: -8px;
	margin-left: -10px;**/
    padding: 10px;

}

.slds-accordion__summary-content {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal !important;

    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 21px;
    color: #000000;
}

.slds-accordion__list-item {
    border-top-width: var(--slds-c-accordion-sizing-border, var(--sds-c-accordion-sizing-border, var(--lwc-borderWidthThin, 1px)));
    border-top-style: solid;
    border-top-color: var(--slds-c-accordion-color-border, var(--sds-c-accordion-color-border, var(--lwc-colorBorder, #D4D4D4)));
}

.slds-accordion__list-item:first-child {
    border-top: 0;
}

/** accordion css start **/

/** error message css start **/
.slds-has-error .slds-form-element__help{
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: normal;
    font-size: 16px;
    line-height: 24px;
    /**color: #001E41;**/
}

.validateCheckbox .slds-required{
    display: none;
}

/** mobile **/
@media (max-width: 47.999em) {

    .slds-button.buttonSpace{
        margin-left: 2.5px;
    }

}