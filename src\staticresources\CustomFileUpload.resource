.custom-file-upload .slds-form-element__label {
    display: none;
}

.custom-file-upload .slds-file-selector_files {
    display: flex;
    background: rgb(243, 242, 242);
}

.custom-file-upload .slds-file-selector__dropzone {
    display: flex;
    flex-grow: 1;
}

.custom-file-upload .slds-file-selector__dropzone>slot {
    display: flex;
    flex-grow: 1;
}

.custom-file-upload .slds-file-selector__body {
    display: flex;
    flex-grow: 1;
    justify-content: center;
    flex-direction: column;
    height: 5.5rem;
}

.custom-file-upload .slds-file-selector__text {
    margin-top: 0.75rem;
}

.custom-file-upload-table {
    max-height: 25rem;
    overflow-y: auto;
}
.custom-file-upload-table table {
    border-top: 0px;
    position: relative;
}
.custom-file-upload-table table tbody .custom-row:first-child td{
    border-top: 0px;
}
.custom-file-upload-table table thead th{
    border-top: 1px solid #dddbda;
    border-bottom: 1px solid #dddbda;
    background: #ecebea;
    position: sticky;
    top: 0;
    z-index: 3;
}