/**
 * Created by jerry.poon on 1/07/2021.
 */

import {LightningElement, api} from 'lwc';

export default class ClientCommXplanAssets extends LightningElement {
    _assetTotal = 0.00;
    _xplanData = [];
    _tableData = [];
    _collapsed = true;
    _showDownChevron = true;
    _showUpChevron = false;
    _defaultSortDirection = 'asc';
    _sortDirection = 'asc';
    _sortedBy; 

    //datatable columns
    _columns = [
        { label: 'Type', fieldName: 'type_group', sortable: true, editable: false },
        { label: 'Asset Type', fieldName: 'type', sortable: true, editable: false },
        { label: 'Description', fieldName: 'desc', sortable: true, editable: false },
        { label: 'Owner Type', fieldName: 'owner', sortable: true, editable: false },
        { label: 'Current Value', fieldName: 'amount', type:'currency', sortable: true, editable: false }
    ];

    @api
    get xplanData(){
        return this._xplanData;
    }
    set xplanData(dataDetails){
        this._xplanData = dataDetails;
        this.initialiseData();
    }

    get _mainClass(){
        return (this._collapsed) ? 'xplan-details__recordCard xplan-details__recordCard__collapsed' : 'xplan-details__recordCard';
    }

    initialiseData() {
        //create a table row for each asset
        this._xplanData.forEach((asset) => {
            let row = {};
            row.type_group = asset.Type__c;
            row.type = asset.Asset_Type__c;
            row.desc = asset.Description__c;
            row.owner = asset.Owner_Type__c;
            row.amount = asset.Current_Value__c;
            row.index = asset.External_Id__c;
            this._tableData.push(row);
            //Sum up the asset value
            if (asset?.Current_Value__c) {
                this._assetTotal = this._assetTotal == null ? asset.Current_Value__c : (this._assetTotal + asset.Current_Value__c);
            }
        });
        //convert to currency
        this._assetTotal = this.formatter.format(this._assetTotal);
    }

    handleToggleCollapse(){
        this._collapsed = !this._collapsed;
        this._showDownChevron = this._collapsed;
        this._showUpChevron = !this._collapsed;
    }

    //Sorting
    sortBy(field, reverse, primer) {
        const key = primer
            ? function (x) {
                if(primer(x[field] === undefined)) {
                    return '';
                }
                return primer(x[field]);
            }
            : function (x) {
                if(x[field] === undefined) {
                    return '';
                }
                return x[field];
            };

        return function (a, b) {
            a = key(a);
            b = key(b);
            return reverse * ((a > b) - (b > a));
        };
    }

    onHandleSort(event) {
        const { fieldName: sortedBy, sortDirection } = event.detail;
        const cloneData = [...this._tableData];

        cloneData.sort(this.sortBy(sortedBy, sortDirection === 'asc' ? 1 : -1));
        this._tableData = cloneData;
        this._sortDirection = sortDirection;
        this._sortedBy = sortedBy;
    }

    //Converts number to currency
    formatter = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'AUD'
    });
}