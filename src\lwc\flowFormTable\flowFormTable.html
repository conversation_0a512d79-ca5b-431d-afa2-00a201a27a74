
<template>   
    <lightning-card title="" class="cardLightning">
        <div class="insideCard">
			<table class="slds-table_cell-buffer">
			 
				<thead>
					<tr class="">
						<th class="" scope="col">
							<div class="slds-truncate" title="Beneficiary">Beneficiary</div>
						</th>
						<template for:each = {_columns} for:item = "col" for:index="indexVar">
							<th key={col.id} class="" scope="col">
								<div class="slds-truncate" title={col.label}>{col.label}</div>
							</th>
						</template>
						<th class="" scope="col"></th>
					</tr>
				</thead>
				<tbody>
					<template for:each={_data} for:item="item" for:index="indexVar">
						<tr key={item.rowHeaderLabel} class="slds-hint-parent">
							<template if:true={useRowHeaderLabel}>
								<td data-label={item.rowHeaderLabel} scope="row" class={_rowHeaderStyle}><!--SFP-63279-->
									<div class="slds-truncate" title={item.rowHeaderLabel}>&nbsp;</div>
								</td>
							</template>
							<template for:each={_columns} for:item="col" for:index="colIndex">
								<td key={col.id} data-id={col.id} data-label={col.label} class={_tdClass}>
									<template if:true={_isEditMode}>
										<template if:true={col.required}>
											<abbr title="required" class="slds-required slds-float_left">*</abbr>
										</template>
									</template>
									<template if:true={col.usetooltip}>
										<lightning-helptext content={col.tooltip} class="columnhelptext"></lightning-helptext>
									</template>
									<!-- //SFP-59427, burhan, added min-value={col.minValue}, max-value={col.maxValue}, and step={col.step} -->
									<c-flow-table-row
										is-edit-mode={_isEditMode}
										col-type={col.type}
										col-index={colIndex}
										label={col.label}
										input-keys={_inputRowKeys}
										item={item.value}
										picklist-options={picklistOption}
										oninputchange={handleOnChangeInput}
										disabled={col.disabled}
										min-value={col.minValue}
										max-value={col.maxValue}
										step={col.step}
										style="flex:1;"
									></c-flow-table-row>
								    
                                    

									<template if:false={_isEditMode}>
									<c-flow-table-row
									is-edit-mode={_isEditMode}
									col-type="text"
									col-index={colIndex}
									label={col.label}
									input-keys={_inputRowKeys}
									item={item.SecondValue}
									picklist-options={picklistOption}
									oninputchange={handleOnChangeInput}
									disabled={col.disabled}
									min-value={col.minValue}
									max-value={col.maxValue}
									step={col.step}
									style="flex:1;"
								    ></c-flow-table-row>

								    </template>
								

									

								</td>
							</template>
							<!-- DELETE BEA-->
							<template if:true={_isEditMode}>
								<template if:true={showDeleteButton}>
									<td class="nopaddingside" data-label="">
										<button
											name={indexVar}
											class="slds-button slds-button_stretch removeButton"
											onclick={handleRemove}>
											<lightning-icon icon-name="utility:delete" class="utilityIconDelete"></lightning-icon> 
											{_btnDeleteLabel}
										</button>
										<hr class="hrStyle"><!--SFP-63776 Bea Boyco-->
									</td>
								</template>
							</template>
						</tr>
					</template>
				</tbody>
			</table>
		</div>

		<template if:true={useEstatePercentage}>
			<span class="slds-form-element">
				<label for="textInput" class={_estateTextStyle}> <!--SFP-63279-->
					{_estateText}
				</label>
				<template if:true={useEstateTooltip}>
					<lightning-helptext content={_estateToolTip} class="slds-float_left"></lightning-helptext>
				</template>
			</span>

			<div class="clearboth"></div>

			<template if:true={showEstateSubtext}><!--SFP-63279-->
				<div class="rowHeaderStyle defaultfont">{_estateSubText}</div>				
			</template>
			
			<span class="slds-form-element">
				<div class="">
					<template if:true={_isEditMode}>
						<label for="textInput" class="slds-form-element__label slds-no-flex">{_benefitProportionLabel}</label>
						<template if:true={useBenefitProportionTooltip}>
							<lightning-helptext content={_benefitProportionTooltip}></lightning-helptext>
						</template>
						<!-- <lightning-input id="textInput" type="numeric" label="" value={_estatePercentage} onchange={handleBenefitEstateChange}>{_estatePercentage}</lightning-input> -->

						<!-- SFP-57187, burhan -->
						<lightning-input id="textInput" type="number" label="" value={_estatePercentage} onchange={handleBenefitEstateChange}>{_estatePercentage}</lightning-input>
					</template>
					<template if:false={_isEditMode}>
						<!--div class="slds-grid slds-wrap">
							<div class="slds-col slds-size_6-of-12">
							  <span>
								<label class="defaultfont">{_benefitProportionLabel}</label>
								<template if:true={useBenefitProportionTooltip}>
									<lightning-helptext content={_benefitProportionTooltip}></lightning-helptext>
								</template>
							  </span>
							</div>
							<div class="slds-col slds-size_6-of-12 paddingleft5percent">
							  <span>
								<label class="defaultfont">{_estatePercentage}</label>
							  </span>
							</div>
						  </div--><!--SFP-63279-->
						<table class="slds-table_cell-buffer">
							<thead class="">
								<tr class="">
									<th scope="col"></th>
								</tr>
							</thead>
							<tbody>
								<tr class="slds-hint-parent">
									<td data-label={_benefitProportionLabel} class={_tdClass} style="border-top: 1px solid #D4D4D4;">
										<template if:true={useBenefitProportionTooltip}>
											<lightning-helptext content={_benefitProportionTooltip} class="columnhelptext"></lightning-helptext>
										</template>
										<div class="dataColumnStyle">{_estatePercentage}</div>
									</td>
								</tr>
							</tbody>
						</table>
					</template>
				</div>
			</span>
			<template if:true={_isEditMode}>
				<hr class="hrStyle"><!--SFP-63776 Bea Boyco-->
			</template>
		</template>

		<div class="clearboth"></div>

		<template if:true={_isEditMode}>
			<template if:true={showAddButton}>				
				<!-- SFP-63776, burhan, added margin top 10px (ADD)-->
				<button 
					onclick={handleAdd}
					class="slds-button slds-button_stretch addButton">
					<lightning-icon icon-name="utility:add" class="utilityIconAdd"></lightning-icon> 
					{_btnAddLabel}
				</button>
			</template>
		</template>

		<!--br/-->
		<template if:true={showHideTotalPercentage}>
			<br/>
			<div class="addPadding16"></div>
			<span class="slds-form-element">
				<template if:true={useTotalPercentageTooltip}>
					<lightning-helptext content={_totalPercentageTooltip} class="slds-float_right"></lightning-helptext>
				</template>
				<label for="textInput" class="slds-form-element__label slds-no-flex slds-float_right">
					<!-- //SFP-59427, burhan added {totalLabel} {totalSign} -->
					<p>{totalLabel} <template if:true={isTotalSignBeforeValue}>{totalSign}</template>{totalPercentage}<template if:false={isTotalSignBeforeValue}>{totalSign}</template></p>
				</label>
			</span>

			<div class="clearboth"></div>
	
			<template if:true={showTotalErrorMsg}> <!-- //SFP-59427, burhan added {showTotalErrorMsg} -->
				<template if:true={showNomBenTotalPercentErrorMsg}> <!-- //SFP-59427, burhan added {showNomBenTotalPercentErrorMsg} -->
					<template if:true={showPercentageErrorMsg}>
						<div class="addPadding"></div>
						<!-- SFP-60533, xylene-->
						<!-- <p class="slds-text-color_error">{totalErrorMsg}</p>//SFP-59427, burhan added {totalErrorMsg} -->
						<div style="font-size:14px;">
							<c-scoped-notification message={totalErrorMsg} error={scopedNotificationIsError}></c-scoped-notification>
						</div>
					</template>
				</template>

				 <!-- //SFP-59427, burhan -->
				<template if:false={showNomBenTotalPercentErrorMsg}>
					<!-- new error logic here -->
				</template>
			</template>

			<!-- SFP-57187, burhan -->
			<template if:true={estatePercentageErrorMsg}>
				<br/>
				<p class="slds-text-color_error">{estatePercentageErrorMsg}</p>
			</template>
		</template>

		<template if:true={showMaxRowErrorMsg}>
			<!-- SFP-60533, xylene-->
			<!-- <p class="slds-text-color_error">{_maxRowErrorMsg}</p> //SFP-59427, burhan added {_maxRowErrorMsg} -->
			<div style="font-size:14px;">
				<c-scoped-notification message={_maxRowErrorMsg} error={scopedNotificationIsError}></c-scoped-notification>
			</div>	
		</template>
		<template if:true={_hasMandatoryFieldsErrorMsg}>
			<div class={_updateToNoPadding}></div>
			<ul style="margin-left:0;">
				<template for:each={mandatoryFieldsErrorMsgArr} for:item="item">
					<!-- SFP-60533, xylene-->
					<!-- <li key={item}><p key={item} class="slds-text-color_error">{item}</p></li> -->
					<div key={item} style="font-size:14px;">
						<c-scoped-notification key={item} message={item} error={scopedNotificationIsError}></c-scoped-notification>
					</div>
				</template>
			</ul>
		</template>
	</lightning-card>

	<template if:true={showModal}>
        <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container formModalContainer">
                <div class="slds-modal__content slds-p-around_medium formModalContent" id="modal-content-id-1">
                    <div class="formModalBody">{modalBody}</div>
					
					<div class="slds-float_right">
						<button class="slds-button slds-button_neutral formModalNo" onclick={handleCloseModal} title="Cancel">No</button>
						<button class="slds-button slds-button_brand formModalYes" onclick={handleConfirmDelete} title="Yes">Yes</button>
					</div>
                </div>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
	</template>
</template>