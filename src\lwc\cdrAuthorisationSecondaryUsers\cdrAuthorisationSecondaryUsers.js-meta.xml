<?xml version="1.0" encoding="UTF-8"?>
<LightningComponentBundle xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>56.0</apiVersion>
    <isExposed>true</isExposed>
    <description>CDR Authorization Page Lightning Web Component for Secondary Users enhancement</description>
    <targets>
        <target>lightningCommunity__Page</target>
        <target>lightningCommunity__Default</target>
    </targets>
    <targetConfigs>
        <targetConfig targets="lightningCommunity__Default">
            <property name="originatedFromArrangement" label="Originated From Arrangement" type="Boolean" description="If true, this component will find existing arrangement from query parameter" default="false"/>
        </targetConfig>
    </targetConfigs>
</LightningComponentBundle>