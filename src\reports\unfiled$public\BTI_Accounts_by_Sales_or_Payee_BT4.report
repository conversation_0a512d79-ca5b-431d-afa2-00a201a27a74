<?xml version="1.0" encoding="UTF-8"?>
<Report xmlns="http://soap.sforce.com/2006/04/metadata">
    <columns>
        <field>FinServ__FinancialAccount__c$Sales_Account__c.Mapped_Payee_Id__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$Source_Contract_Id__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$Sales_Id__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$Sales_Account__c.Adviser_Name__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$Sales_Account__c.Status__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$FinServ__FinancialAccountNumber__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$Name</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$FinServ__Status__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$FinServ__SourceSystemId__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$POWN_Name__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$FinServ__PrimaryOwner__c.SCVID__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$Product_System_Code__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$Sales_Account__c.Comm_Payee_Name__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$FinServ__Address1__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$Suburb__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$FinServ__PostalCode__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$FinServ__State__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$POWN_Email__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$POWN_Phone__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$Adviser_Phone_Formula__c</field>
    </columns>
    <columns>
        <field>FinServ__FinancialAccount__c$FinServ__PrimaryOwner__c.Party_Type__c</field>
    </columns>
    <description>This report gives you a full list of all internal policies associated with a practice.
It picks up a sales ID that is mapped to a particular payee ID.</description>
    <filter>
        <criteriaItems>
            <column>FinServ__FinancialAccount__c$Product_System_Code__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>contains</operator>
            <value></value>
        </criteriaItems>
        <criteriaItems>
            <column>FinServ__FinancialAccount__c$Source_Contract_Id__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>contains</operator>
            <value></value>
        </criteriaItems>
        <criteriaItems>
            <column>FinServ__FinancialAccount__c$FinServ__Status__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>Active,Open,Pending,Lodged</value>
        </criteriaItems>
        <criteriaItems>
            <column>FinServ__FinancialAccount__c$Sales_Account__c.Mapped_Payee_Id__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>contains</operator>
            <value>BAJQO-N</value>
        </criteriaItems>
        <criteriaItems>
            <column>FinServ__FinancialAccount__c$Sales_Account__c.Mapped_Payee_Name__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>contains</operator>
            <value></value>
        </criteriaItems>
        <criteriaItems>
            <column>FinServ__FinancialAccount__c$Sales_Id__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>contains</operator>
            <value></value>
        </criteriaItems>
        <criteriaItems>
            <column>FinServ__FinancialAccount__c$FinServ__PrimaryOwner__c.Party_Type__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>Golden</value>
        </criteriaItems>
        <language>en_US</language>
    </filter>
    <format>Tabular</format>
    <name>BTI - Accounts by Sales or Payee</name>
    <params>
        <name>co</name>
        <value>1</value>
    </params>
    <reportType>Financial_Accounts_with_Sales_Account__c</reportType>
    <scope>organization</scope>
    <showDetails>true</showDetails>
    <showGrandTotal>true</showGrandTotal>
    <showSubTotals>true</showSubTotals>
    <timeFrameFilter>
        <dateColumn>FinServ__FinancialAccount__c$Sales_Account__c.Start_Date__c</dateColumn>
        <interval>INTERVAL_CUSTOM</interval>
    </timeFrameFilter>
</Report>
