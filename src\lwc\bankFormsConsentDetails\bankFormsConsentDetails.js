import { LightningElement, track, wire, api } from 'lwc';
import { loadStyle } from "lightning/platformResourceLoader";
import { NavigationMixin } from 'lightning/navigation'; //For Navigation
import bankFormsCss from '@salesforce/resourceUrl/bankFormsConsentsCSS'; //CSS from static resource
// Apex controller methods
import getScopedNotificationPD from '@salesforce/apex/BankFormsConsentController.getScopedNotificationPD';

export default class BankFormsConsents extends NavigationMixin(LightningElement) {
    @api dfrId;
    @api selectedConsent=[];
    @api redirectUrl;
    @track caseNoteTable;
    @track isDeclined = false;
    @track isModalOpen = false;
    @track formName='Form Name';
    @track isPartialDischarge = false;
    @track txtScopedNotif;
    pageNumber = 2;
    _form = 'Partial loan discharge authority or security substitution request';

    constructor() {
        super();
        Promise.all([
            loadStyle(this, bankFormsCss)
        ]).then(() => {
            // console.log("Loaded css");
        }).catch(error => {
            console.error('Error loading static resources', error);
        });
    }

    connectedCallback(){
        let dfrRecId = this.dfrId;
        let consent= [];
        consent=this.selectedConsent;
        //console.log('consent: ', consent);
        this.caseNoteTable=consent.dfr.Case_Note_Table__c;
        this.formName = consent.formName;

        let form = consent.dfr.Form_Name__c;
        if(form == 'Partial loan discharge authority' || form == 'Security substitution'){
            this.formName = form + ' request';
            
            getScopedNotificationPD({ formname: this._form})
            .then(data => {
                this.isLoaded = !this.isLoaded;
                if (data) {
                    console.log('data: ', data);
                    this.txtScopedNotif = data;
                }

            }).catch(error => {
                console.error('Error in getScopedNotificationPD: ', error);
                this.hasError = true;
                this.showToast('An unexpected error occurred', 'error', 'Please contact AMP Bank.');
            });

            this.isPartialDischarge = true;
        }
    }

    handleContinue(event){
        //move to another page
        this.pageNumber = this.pageNumber + 1;
        
    }

    handlePages(){
        //handle event to return to first page from second page
        //decrement current page number by 1 (authorisation page)
        this.pageNumber = this.pageNumber -1;
    }

    openModal() {
        // to open modal set isModalOpen track value as true
        this.isModalOpen = true;
    }
    closeModal() {
        // to close modal set isModalOpen track value as false
        this.isModalOpen = false;
    }
    submitDetails() {
        // to close modal set isModalOpen track value as false
        //Add your code to call apex method or do some processing
        this.isModalOpen = false;
        this.isDeclined = true;
    }

    //getter to render page by page number
    get renderPageNumber(){
        let renderPage;
        if(this.pageNumber === 2){
            renderPage = true;
        }
        else{
            renderPage = false;
        }
        //console.log('renderPage: ',renderPage);
        return renderPage;
    }

}