<?xml version="1.0" encoding="UTF-8"?>  
<LightningComponentBundle xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>56.0</apiVersion>
    <isExposed>true</isExposed>
    <masterLabel>Lwc 2 Factor Authentication</masterLabel>
    <description>Lwc 2 Factor Authentication</description>
    <targets>
        <target>lightning__FlowScreen</target>
    </targets>
    <targetConfigs>
        <targetConfig targets="lightning__FlowScreen">
            <property name="varValidAttempts" type="Boolean" label="Is Valid Attempt?"></property>
            <property name="varSendSecurityCodeButton" type="Boolean" label="Is Send Security Code Button Clicked?"></property>
            <property name="varResendSecurityCodeButton" type="Boolean" label="Is Resend Security Code Button Clicked?"></property>
            <property name="inputSecurityCode" type="String" label="Security Code"></property>
            <property name="codeLength" type="String" label="Code Length"></property>
            <property name="varFirstAttempt" type="Boolean" label="Is First Attempt?"></property>
            <property name="varResendSecurityCodeMessage" type="Boolean" label="Resend Security Code Message"></property>
            <property name="varNumberOfAttempts" type="String" label="Number of Attempts"></property>
            <property name="varTotalNumberOfAttempt" type="String" label="Total Number of Attempts"></property>
            <property name="varGetPhoneNumber" type="String" label="Get the Mobile Number"></property>
            <property name="mobileNumberLast3digits" type="String" label="The Last 3 digits of Mobile Number"></property>
            <property name="I_have_read_and_understood_the_important_information" type="Boolean" label="I understand tickbox invalid"></property>
            <property name="i_understand_tickbox" type="Boolean" label="I understand tickbox"></property>
            <property name="checkboxAgent_tickbox" type="Boolean" label="Checkbox agent tickbox"></property>
            <property name="randSecurityCode" type="Boolean" label="Invalid Six digit security code"></property>
            <property name="varRandSecurityCode" type="String" label="Six digit security code"></property>
        </targetConfig>
    </targetConfigs>
</LightningComponentBundle>