<template> 
	<lightning-input 
		type="tel" 
		label="Mobile" 
		value={mobileNumber}
		onblur={checkInput} 
		message-when-bad-input={validationMessage}
		message-when-pattern-mismatch={validationMessage}
		pattern="^[+]*[0-9()]+(?:\s[0-9()]+)*$"
		min-length="6"
		required={isRequired}
		disabled={isDisabled}
	></lightning-input>
	<template if:true={isWarning}>
		<div style='font-size: 14px'>
			<c-scoped-notification message={warningMessage} warning="true"></c-scoped-notification>
			<!-- <c-scoped-notification if:true={isError} message={errorMessage} error="true"></c-scoped-notification> -->
		</div>
	</template>
</template> 