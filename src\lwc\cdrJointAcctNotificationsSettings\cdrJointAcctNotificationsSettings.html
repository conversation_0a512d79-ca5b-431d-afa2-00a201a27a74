<template>
    <div class="content__body">
        <br>
        <br>
        <!-- loading -->
        <template if:false={loaded}>
            <lightning-spinner class="loading slds-is-relative" alternative-text="Loading" size="large"
                style="position: fixed;"></lightning-spinner>
        </template>

        <!-- SFP-66666 -->
        <template if:true={loaded}>
            <!-- Header -->
            <h2 class="slds-text-align_left header-text hurme-text m-margin">Joint account data sharing notifications
                settings</h2>
            <div class="slds-var-p-top_medium confirmation-text">
                <p>Manage your data sharing notifications frequency.</p>
            </div>

            <div class="joint-account-sharing-notifications">
                <div class="content__body-main-dashboard-shadow">
                    <div
                        class="sharing-notifs slds-media__body slds-grid slds-grid_vertical-align-center slds-var-p-top_xx-small">
                        <div class="slds-grid slds-wrap">
                            <lightning-input class="toggle_button" data-id="notif_toggle" type="toggle"
                                label="Data sharing notifications" checked={notifToggle} onchange={handleToggleChange}
                                message-toggle-active="" message-toggle-inactive=""></lightning-input>
                            <br>
                        </div>
                    </div>

                    <template if:true={notifToggle}>
                        <div
                            class="slds-border_top border-style-top slds-var-p-top_large slds-var-p-bottom_large slds-var-p-left_large">
                            <lightning-radio-group name="frequencyOptions" label="Notifications Frequency"
                                options={frequencyOptions} value={frequencyValue} type="radio"
                                onchange={handleRadioChange} onclick={handleRadioClick}></lightning-radio-group>
                        </div>
                    </template>
                </div>
                <!--end of content__body-main-dashboard-shadow-->
            </div>
            <template if:true={isToggled}>
                <template if:true={isModalOpen}>
                    <template if:false={notifToggle}>
                        <section aria-modal="true" class="slds-modal slds-fade-in-open">
                            <div class="slds-modal__container ">
                                <header class="confirmation-modal-header">
                                    <h3 class="confirmation-modal-text">Do you want to turn off your notifications?</h3>
                                </header>
                                <div class="slds-modal__content ">
                                    <div class="confirmation-modal-body">
                                        <lightning-formatted-rich-text value={labels.notifDisable}>
                                        </lightning-formatted-rich-text>
                                    </div>
                                </div>
                                <footer class="">
                                    <div class="slds-grid confirmation-modal-footer">
                                        <div class="slds-col slds-size_6-of-12 confirmation-modal-button-container">
                                            <button
                                                class="slds-button slds-align_absolute-center confirmation_modal_button"
                                                onclick={handleCloseModal}>
                                                <p class="manage-label">No, go back</p>
                                            </button>
                                        </div>
                                        <div class="slds-col slds-size_6-of-12 confirmation-modal-button-container">
                                            <button
                                                class="slds-button slds-align_absolute-center confirmation_modal_button"
                                                onclick={handleClick}>
                                                <p class="manage-label">Yes, turn them off</p>
                                            </button>
                                        </div>
                                    </div>
                                </footer>
                            </div>
                        </section>
                    </template>
                    <template if:true={notifToggle}>
                        <section aria-modal="true" class="slds-modal slds-fade-in-open">
                            <div class="slds-modal__container ">
                                <header class="confirmation-modal-header">
                                    <h3 class="confirmation-modal-text">Do you want to turn on your notifications?</h3>
                                </header>
                                <div class="slds-modal__content ">
                                    <div class="confirmation-modal-body">
                                        <lightning-formatted-rich-text value={labels.notifEnable}>
                                        </lightning-formatted-rich-text>
                                    </div>
                                </div>
                                <footer class="">
                                    <div class="slds-grid confirmation-modal-footer">
                                        <div class="slds-col slds-size_6-of-12 confirmation-modal-button-container">
                                            <button
                                                class="slds-button slds-align_absolute-center confirmation_modal_button"
                                                onclick={handleCloseModal}>
                                                <p class="manage-label">No, go back</p>
                                            </button>
                                        </div>
                                        <div class="slds-col slds-size_6-of-12 confirmation-modal-button-container">
                                            <button
                                                class="slds-button slds-align_absolute-center confirmation_modal_button"
                                                onclick={handleClick}>
                                                <p class="manage-label">Yes, turn them on</p>
                                            </button>
                                        </div>
                                    </div>
                                </footer>
                            </div>
                        </section>
                    </template>
                    <div class="slds-backdrop slds-backdrop_open"></div>
                </template>
            </template>

            <div>
                <template if:true={isToggled}>
                    <br>
                    <h5 class="hurme-text">
                        <button type="button"
                            class="slds-button slds-button_neutral button__medium button__stop-sharing"
                            onclick={handleOpenModal}>Save changes</button>
                    </h5>
                </template>
                <template if:false={isToggled}>
                    <br>
                    <h5 class="hurme-text">
                        <button type="button"
                            class="slds-button slds-button_neutral button__medium button__stop-sharing"
                            onclick={handleClick}>Save changes</button>
                    </h5>
                </template>

            </div>
        </template>
    </div> <!-- end of content__body-dashboard-->
</template>