<?xml version="1.0" encoding="UTF-8"?>
<ReportType xmlns="http://soap.sforce.com/2006/04/metadata">
    <baseObject>FinServ__FinancialAccount__c</baseObject>
    <category>other</category>
    <deployed>true</deployed>
    <description>Report to be able to extract the various comms preference fields. Process Admin &amp; Contact Centre users should be able to access this report type.</description>
    <label>Plan level Comms Preference</label>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Account_Class__c</field>
            <table>FinServ__FinancialAccount__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Financial Account Number</displayNameOverride>
            <field>Account_Number_ProductCode__c</field>
            <table>FinServ__FinancialAccount__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Plan_Short_Code__c</field>
            <table>FinServ__FinancialAccount__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Plan Short Code</displayNameOverride>
            <field>Plan_Number__c</field>
            <table>FinServ__FinancialAccount__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Plan_Name__c</field>
            <table>FinServ__FinancialAccount__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Status__c</field>
            <table>FinServ__FinancialAccount__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Financial Account Product</displayNameOverride>
            <field>Product__c</field>
            <table>FinServ__FinancialAccount__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedDate</field>
            <table>FinServ__FinancialAccount__c</table>
        </columns>
        <masterLabel>Financial Accounts</masterLabel>
    </sections>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Title</displayNameOverride>
            <field>FinServ__PrimaryOwner__c.FinServ__PrimaryContact__c.Salutation</field>
            <table>FinServ__FinancialAccount__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>First Name</displayNameOverride>
            <field>FinServ__PrimaryOwner__c.FinServ__PrimaryContact__c.FirstName</field>
            <table>FinServ__FinancialAccount__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Surname</displayNameOverride>
            <field>FinServ__PrimaryOwner__c.FinServ__PrimaryContact__c.LastName</field>
            <table>FinServ__FinancialAccount__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Member&apos;s Date of Birth</displayNameOverride>
            <field>FinServ__PrimaryOwner__c.Primary_Contact_Birthdate__c</field>
            <table>FinServ__FinancialAccount__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Member&apos;s Email Address</displayNameOverride>
            <field>FinServ__PrimaryOwner__c.Primary_Contact_Email__c</field>
            <table>FinServ__FinancialAccount__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Email Invalid</displayNameOverride>
            <field>FinServ__PrimaryOwner__c.Email_Invalid__c</field>
            <table>FinServ__FinancialAccount__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Member&apos;s Mailing Address</displayNameOverride>
            <field>FinServ__PrimaryOwner__c.Account_Mailing_Address__c</field>
            <table>FinServ__FinancialAccount__c</table>
        </columns>
        <masterLabel>Primary Owner Lookup</masterLabel>
    </sections>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Statements &amp; Updates about Product</displayNameOverride>
            <field>FinServ__PrimaryOwner__c.Statements_updates_about_your_products__c</field>
            <table>FinServ__FinancialAccount__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Servicing SMS Message</displayNameOverride>
            <field>FinServ__PrimaryOwner__c.Servicing_SMS_messages__c</field>
            <table>FinServ__FinancialAccount__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Online statements</displayNameOverride>
            <field>FinServ__PrimaryOwner__c.Online_Statements_Commencement_Note_Sent__c</field>
            <table>FinServ__FinancialAccount__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>Annual Report</displayNameOverride>
            <field>FinServ__PrimaryOwner__c.Annual_Report__c</field>
            <table>FinServ__FinancialAccount__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <displayNameOverride>News &amp; insights</displayNameOverride>
            <field>FinServ__PrimaryOwner__c.News_insights__c</field>
            <table>FinServ__FinancialAccount__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SCV_PrimaryOwner__c.Marketing_Calls__c</field>
            <table>FinServ__FinancialAccount__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SCV_PrimaryOwner__c.Marketing_Emails__c</field>
            <table>FinServ__FinancialAccount__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SCV_PrimaryOwner__c.Marketing_Letters__c</field>
            <table>FinServ__FinancialAccount__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SCV_PrimaryOwner__c.Marketing_SMS_messages__c</field>
            <table>FinServ__FinancialAccount__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SCV_PrimaryOwner__c.Market_Research__c</field>
            <table>FinServ__FinancialAccount__c</table>
        </columns>
        <masterLabel>Member Comms Preference</masterLabel>
    </sections>
</ReportType>
