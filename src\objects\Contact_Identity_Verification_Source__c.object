<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>Accept</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Accept</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Accept</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <allowInChatterGroups>false</allowInChatterGroups>
    <compactLayoutAssignment>SYSTEM</compactLayoutAssignment>
    <deploymentStatus>Deployed</deploymentStatus>
    <enableActivities>false</enableActivities>
    <enableBulkApi>true</enableBulkApi>
    <enableFeeds>false</enableFeeds>
    <enableHistory>true</enableHistory>
    <enableLicensing>false</enableLicensing>
    <enableReports>true</enableReports>
    <enableSearch>true</enableSearch>
    <enableSharing>true</enableSharing>
    <enableStreamingApi>true</enableStreamingApi>
    <externalSharingModel>ControlledByParent</externalSharingModel>
    <fields>
        <fullName>Contact_Identity_Verification__c</fullName>
        <externalId>false</externalId>
        <label>Contact Identity Verification</label>
        <referenceTo>Contact_Identity_Verification__c</referenceTo>
        <relationshipLabel>Contact Identity Verification Sources</relationshipLabel>
        <relationshipName>Contact_Identity_Verification_Sources</relationshipName>
        <relationshipOrder>0</relationshipOrder>
        <reparentableMasterDetail>false</reparentableMasterDetail>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>MasterDetail</type>
        <writeRequiresMasterRead>false</writeRequiresMasterRead>
    </fields>
    <fields>
        <fullName>Document_expiry_date__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Document expiry date</label>
        <required>false</required>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Date</type>
    </fields>
    <fields>
        <fullName>Document_type__c</fullName>
        <externalId>false</externalId>
        <label>Document type</label>
        <required>false</required>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Picklist</type>
        <valueSet>
            <restricted>true</restricted>
            <valueSetDefinition>
                <sorted>false</sorted>
                <value>
                    <fullName>ATO Debt Pay Notice</fullName>
                    <default>false</default>
                    <label>ATO Debt Pay Notice</label>
                </value>
                <value>
                    <fullName>Birth Certificate</fullName>
                    <default>false</default>
                    <label>Birth Certificate</label>
                </value>
                <value>
                    <fullName>Citizenship Certificate</fullName>
                    <default>false</default>
                    <label>Citizenship Certificate</label>
                </value>
                <value>
                    <fullName>Driver&apos;s Licence</fullName>
                    <default>false</default>
                    <label>Driver&apos;s Licence</label>
                </value>
                <value>
                    <fullName>Financial Benefits Notice</fullName>
                    <default>false</default>
                    <label>Financial Benefits Notice</label>
                </value>
                <value>
                    <fullName>Health Card</fullName>
                    <default>false</default>
                    <label>Health Card</label>
                </value>
                <value>
                    <fullName>National Identity Card</fullName>
                    <default>false</default>
                    <label>National Identity Card</label>
                </value>
                <value>
                    <fullName>Other</fullName>
                    <default>false</default>
                    <label>Other</label>
                </value>
                <value>
                    <fullName>Passport</fullName>
                    <default>false</default>
                    <label>Passport</label>
                </value>
                <value>
                    <fullName>Pension Card</fullName>
                    <default>false</default>
                    <label>Pension Card</label>
                </value>
                <value>
                    <fullName>Provision of Services Notice</fullName>
                    <default>false</default>
                    <label>Provision of Services Notice</label>
                </value>
                <value>
                    <fullName>School Attendance Notice</fullName>
                    <default>false</default>
                    <label>School Attendance Notice</label>
                </value>
                <value>
                    <fullName>Visa</fullName>
                    <default>false</default>
                    <label>Visa</label>
                </value>
            </valueSetDefinition>
        </valueSet>
    </fields>
    <fields>
        <fullName>Individual_source_outcome__c</fullName>
        <externalId>false</externalId>
        <label>Individual source outcome</label>
        <required>false</required>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Picklist</type>
        <valueSet>
            <restricted>true</restricted>
            <valueSetDefinition>
                <sorted>false</sorted>
                <value>
                    <fullName>VERIFIED</fullName>
                    <default>false</default>
                    <label>VERIFIED</label>
                </value>
                <value>
                    <fullName>VERIFIED_ADMIN</fullName>
                    <default>false</default>
                    <label>VERIFIED_ADMIN</label>
                </value>
                <value>
                    <fullName>VERIFIED_WITH_CHANGES</fullName>
                    <default>false</default>
                    <label>VERIFIED_WITH_CHANGES</label>
                </value>
                <value>
                    <fullName>PASSED</fullName>
                    <default>false</default>
                    <label>PASSED</label>
                </value>
                <value>
                    <fullName>FAILED</fullName>
                    <default>false</default>
                    <label>FAILED</label>
                </value>
                <value>
                    <fullName>AUTOFAIL</fullName>
                    <default>false</default>
                    <label>AUTOFAIL</label>
                </value>
                <value>
                    <fullName>IN_PROGRESS</fullName>
                    <default>false</default>
                    <label>IN_PROGRESS</label>
                </value>
                <value>
                    <fullName>PENDING</fullName>
                    <default>false</default>
                    <label>PENDING</label>
                </value>
                <value>
                    <fullName>NOT_CONTRIBUTING</fullName>
                    <default>false</default>
                    <label>NOT_CONTRIBUTING</label>
                </value>
                <value>
                    <fullName>LOCKED_OUT</fullName>
                    <default>false</default>
                    <label>LOCKED_OUT</label>
                </value>
                <value>
                    <fullName>ERROR</fullName>
                    <default>false</default>
                    <label>ERROR</label>
                </value>
                <value>
                    <fullName>NOT_FOUND_ON_LIST</fullName>
                    <default>false</default>
                    <label>NOT_FOUND_ON_LIST</label>
                </value>
                <value>
                    <fullName>FOUND_ON_LIST</fullName>
                    <default>false</default>
                    <label>FOUND_ON_LIST</label>
                </value>
                <value>
                    <fullName>MATCH_REVIEW_REQUIRED</fullName>
                    <default>false</default>
                    <label>MATCH_REVIEW_REQUIRED</label>
                </value>
                <value>
                    <fullName>MATCH_CONFIRMED</fullName>
                    <default>false</default>
                    <label>MATCH_CONFIRMED</label>
                </value>
                <value>
                    <fullName>MATCH_FALSE_POSITIVE</fullName>
                    <default>false</default>
                    <label>MATCH_FALSE_POSITIVE</label>
                </value>
                <value>
                    <fullName>EMPTY</fullName>
                    <default>false</default>
                    <label>EMPTY</label>
                </value>
                <value>
                    <fullName>VERIFED_WITH_CHANGES</fullName>
                    <default>false</default>
                    <isActive>false</isActive>
                    <label>VERIFED_WITH_CHANGES</label>
                </value>
            </valueSetDefinition>
        </valueSet>
    </fields>
    <fields>
        <fullName>Service_request_ID_case_number__c</fullName>
        <deleteConstraint>SetNull</deleteConstraint>
        <externalId>false</externalId>
        <label>Service request ID / case number</label>
        <referenceTo>Case</referenceTo>
        <relationshipLabel>Contact Identity Verification Sources</relationshipLabel>
        <relationshipName>Contact_Identity_Verification_Sources</relationshipName>
        <required>false</required>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>Source_expiry_date__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Source expiry date</label>
        <required>false</required>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Date</type>
    </fields>
    <fields>
        <fullName>Verification_field_results__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Verification field results</label>
        <length>2000</length>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Html</type>
        <visibleLines>10</visibleLines>
    </fields>
    <fields>
        <fullName>Verification_source__c</fullName>
        <externalId>false</externalId>
        <label>Verification source</label>
        <required>false</required>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Picklist</type>
        <valueSet>
            <restricted>true</restricted>
            <valueSetDefinition>
                <sorted>false</sorted>
                <value>
                    <fullName>Driver&apos;s Licence</fullName>
                    <default>false</default>
                    <label>Driver&apos;s Licence</label>
                </value>
                <value>
                    <fullName>Electoral Roll</fullName>
                    <default>false</default>
                    <label>Electoral Roll</label>
                </value>
                <value>
                    <fullName>Centrelink</fullName>
                    <default>false</default>
                    <label>Centrelink</label>
                </value>
                <value>
                    <fullName>Certificate - Name Change</fullName>
                    <default>false</default>
                    <label>Certificate - Name Change</label>
                </value>
                <value>
                    <fullName>Credit History</fullName>
                    <default>false</default>
                    <label>Credit History</label>
                </value>
                <value>
                    <fullName>Certificate - Marriage</fullName>
                    <default>false</default>
                    <label>Certificate - Marriage</label>
                </value>
                <value>
                    <fullName>Health Insurance</fullName>
                    <default>false</default>
                    <label>Health Insurance</label>
                </value>
                <value>
                    <fullName>Passport</fullName>
                    <default>false</default>
                    <label>Passport</label>
                </value>
                <value>
                    <fullName>Visa</fullName>
                    <default>false</default>
                    <label>Visa</label>
                </value>
                <value>
                    <fullName>Australian Claims Database</fullName>
                    <default>false</default>
                    <label>Australian Claims Database</label>
                </value>
                <value>
                    <fullName>Australian Tenancy File</fullName>
                    <default>false</default>
                    <label>Australian Tenancy File</label>
                </value>
                <value>
                    <fullName>Birth Certificate</fullName>
                    <default>false</default>
                    <label>Birth Certificate</label>
                </value>
                <value>
                    <fullName>Citizenship Certificate</fullName>
                    <default>false</default>
                    <label>Citizenship Certificate</label>
                </value>
                <value>
                    <fullName>Foreign Passport</fullName>
                    <default>false</default>
                    <label>Foreign Passport (Visa)</label>
                </value>
                <value>
                    <fullName>Certificate - Birth</fullName>
                    <default>false</default>
                    <isActive>false</isActive>
                    <label>Certificate - Birth</label>
                </value>
                <value>
                    <fullName>Certificate - Citizenship</fullName>
                    <default>false</default>
                    <isActive>false</isActive>
                    <label>Certificate - Citizenship</label>
                </value>
            </valueSetDefinition>
        </valueSet>
    </fields>
    <fields>
        <fullName>Verification_source_method__c</fullName>
        <externalId>false</externalId>
        <label>Verification source method</label>
        <required>false</required>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>Picklist</type>
        <valueSet>
            <restricted>true</restricted>
            <valueSetDefinition>
                <sorted>false</sorted>
                <value>
                    <fullName>Interactive</fullName>
                    <default>false</default>
                    <label>Interactive</label>
                </value>
                <value>
                    <fullName>Background</fullName>
                    <default>false</default>
                    <label>Background</label>
                </value>
            </valueSetDefinition>
        </valueSet>
    </fields>
    <fields>
        <fullName>Verified_date_and_time__c</fullName>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Verified date and time</label>
        <required>false</required>
        <trackHistory>true</trackHistory>
        <trackTrending>false</trackTrending>
        <type>DateTime</type>
    </fields>
    <label>Contact Identity Verification Source</label>
    <listViews>
        <fullName>All</fullName>
        <columns>NAME</columns>
        <columns>Contact_Identity_Verification__c</columns>
        <columns>Verification_source__c</columns>
        <columns>Verified_date_and_time__c</columns>
        <columns>Individual_source_outcome__c</columns>
        <columns>Source_expiry_date__c</columns>
        <filterScope>Everything</filterScope>
        <label>All</label>
    </listViews>
    <nameField>
        <displayFormat>IVS-{0000}</displayFormat>
        <label>Verification Source Name</label>
        <trackHistory>true</trackHistory>
        <type>AutoNumber</type>
    </nameField>
    <pluralLabel>Contact Identity Verification Sources</pluralLabel>
    <recordTypeTrackHistory>true</recordTypeTrackHistory>
    <recordTypes>
        <fullName>Source_Admin</fullName>
        <active>true</active>
        <label>Source Admin</label>
        <picklistValues>
            <picklist>Document_type__c</picklist>
            <values>
                <fullName>ATO Debt Pay Notice</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Birth Certificate</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Citizenship Certificate</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Driver%27s Licence</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Financial Benefits Notice</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Health Card</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>National Identity Card</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Other</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Passport</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Pension Card</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Provision of Services Notice</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>School Attendance Notice</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Visa</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>Individual_source_outcome__c</picklist>
            <values>
                <fullName>AUTOFAIL</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>EMPTY</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERROR</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>FAILED</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>FOUND_ON_LIST</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>IN_PROGRESS</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>LOCKED_OUT</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>MATCH_CONFIRMED</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>MATCH_FALSE_POSITIVE</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>MATCH_REVIEW_REQUIRED</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>NOT_CONTRIBUTING</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>NOT_FOUND_ON_LIST</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>PASSED</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>PENDING</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>VERIFIED</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>VERIFIED_ADMIN</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>VERIFIED_WITH_CHANGES</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>Verification_source__c</picklist>
            <values>
                <fullName>Birth Certificate</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Centrelink</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Certificate - Marriage</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Certificate - Name Change</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Citizenship Certificate</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Credit History</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Driver%27s Licence</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Electoral Roll</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Foreign Passport</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Health Insurance</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Passport</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Visa</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>Verification_source_method__c</picklist>
            <values>
                <fullName>Background</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Interactive</fullName>
                <default>false</default>
            </values>
        </picklistValues>
    </recordTypes>
    <recordTypes>
        <fullName>Source_eVID</fullName>
        <active>true</active>
        <label>Source eVID</label>
        <picklistValues>
            <picklist>Document_type__c</picklist>
            <values>
                <fullName>ATO Debt Pay Notice</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Birth Certificate</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Citizenship Certificate</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Driver%27s Licence</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Financial Benefits Notice</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Health Card</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>National Identity Card</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Other</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Passport</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Pension Card</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Provision of Services Notice</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>School Attendance Notice</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Visa</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>Individual_source_outcome__c</picklist>
            <values>
                <fullName>AUTOFAIL</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>EMPTY</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERROR</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>FAILED</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>FOUND_ON_LIST</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>IN_PROGRESS</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>LOCKED_OUT</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>MATCH_CONFIRMED</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>MATCH_FALSE_POSITIVE</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>MATCH_REVIEW_REQUIRED</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>NOT_CONTRIBUTING</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>NOT_FOUND_ON_LIST</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>PASSED</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>PENDING</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>VERIFIED</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>VERIFIED_ADMIN</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>VERIFIED_WITH_CHANGES</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>Verification_source__c</picklist>
            <values>
                <fullName>Australian Claims Database</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Australian Tenancy File</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Birth Certificate</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Centrelink</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Certificate - Marriage</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Certificate - Name Change</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Citizenship Certificate</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Credit History</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Driver%27s Licence</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Electoral Roll</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Foreign Passport</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Health Insurance</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Passport</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Visa</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>Verification_source_method__c</picklist>
            <values>
                <fullName>Background</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Interactive</fullName>
                <default>false</default>
            </values>
        </picklistValues>
    </recordTypes>
    <searchLayouts>
        <searchResultsAdditionalFields>Contact_Identity_Verification__c</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Verification_source__c</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Verified_date_and_time__c</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Individual_source_outcome__c</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Source_expiry_date__c</searchResultsAdditionalFields>
    </searchLayouts>
    <sharingModel>ControlledByParent</sharingModel>
    <validationRules>
        <fullName>Document_expiry_date</fullName>
        <active>true</active>
        <errorConditionFormula>IF($Setup.Bypass__c.Validation_Rules__c, FALSE,
Document_expiry_date__c &lt;= TODAY())</errorConditionFormula>
        <errorMessage>Date must be in the future</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>Source_expiry_date</fullName>
        <active>true</active>
        <errorConditionFormula>IF($Setup.Bypass__c.Validation_Rules__c, FALSE,
 Source_expiry_date__c &lt;= TODAY())</errorConditionFormula>
        <errorMessage>Date cannot be in the past</errorMessage>
    </validationRules>
    <visibility>Public</visibility>
</CustomObject>