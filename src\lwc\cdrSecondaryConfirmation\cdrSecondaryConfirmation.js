/**
SFP-74650 - Open Banking Secondary Users - Management of financial accounts (Management Portal)
*/
import { LightningElement, track, wire, api } from 'lwc'; 
import getSecondaryDataPermission from '@salesforce/apex/CdrSecondaryUserManagement.getSecondaryDataPermission'; 
import getSecondaryDataPermissionName from '@salesforce/apex/CdrSecondaryUserManagement.getSecondaryDataPermissionName';
import saveSecondaryUserPermission from '@salesforce/apex/CdrSecondaryUserManagement.saveSecondaryUserPermission'; 

import getSecondaryUser from '@salesforce/apex/CdrSecondaryUserManagement.getSecondaryUser';
import getSoftName from '@salesforce/apex/CdrSecondaryUserManagement.getSoftName';

//import fetchCDRSetting from '@salesforce/apex/CdrLandingDashboardController.fetchCDRSetting'; 
//Toast events
import {ShowToastEvent} from "lightning/platformShowToastEvent" 
//For navigation
import { NavigationMixin } from 'lightning/navigation'; 
//CSS from static resource
import { loadStyle } from 'lightning/platformResourceLoader'; 
import cdrCss from '@salesforce/resourceUrl/CDR_Styles'; 
import LOGIN_LOGO from '@salesforce/resourceUrl/login_logo';
import CDR_LOGO from '@salesforce/resourceUrl/CDR_Logo';
import CHECK_LOGO from '@salesforce/resourceUrl/check_logo';
import JOINT_ACCOUNT_LOGO from '@salesforce/resourceUrl/joint_account_logo'; //Joint changes
import CIRCLE_MINUS_ICON from '@salesforce/resourceUrl/circle_minus_icon'; //SFP-62341
// Custom labels
import CDR_Secondary_Review_and_Confirm_1 from '@salesforce/label/c.CDR_Secondary_Review_and_Confirm_1';
import CDR_Secondary_Review_and_Confirm_2 from '@salesforce/label/c.CDR_Secondary_Review_and_Confirm_2';

import CDR_Secondary_Review_and_Confirm_3 from '@salesforce/label/c.CDR_Secondary_Review_and_Confirm_3';
import CDR_Secondary_Review_and_Confirm_4 from '@salesforce/label/c.CDR_Secondary_Review_and_Confirm_4';
import CDR_Secondary_Review_and_Confirm_5 from '@salesforce/label/c.CDR_Secondary_Review_and_Confirm_5';
import CDR_Secondary_Review_and_Confirm_6 from '@salesforce/label/c.CDR_Secondary_Review_and_Confirm_6';

import CDR_Secondary_Review_and_Confirm_7 from '@salesforce/label/c.CDR_Secondary_Review_and_Confirm_7';
import CDR_Secondary_Review_and_Confirm_8 from '@salesforce/label/c.CDR_Secondary_Review_and_Confirm_8';
import CDR_Secondary_Review_and_Confirm_9 from '@salesforce/label/c.CDR_Secondary_Review_and_Confirm_9';
import CDR_Secondary_Review_and_Confirm_10 from '@salesforce/label/c.CDR_Secondary_Review_and_Confirm_10';

import CDR_Secondary_Review_and_Confirm_11 from '@salesforce/label/c.CDR_Secondary_Review_and_Confirm_11';
import CDR_Secondary_Review_and_Confirm_12 from '@salesforce/label/c.CDR_Secondary_Review_and_Confirm_12';
import CDR_Secondary_Review_and_Confirm_13 from '@salesforce/label/c.CDR_Secondary_Review_and_Confirm_13';
import CDR_Secondary_Review_and_Confirm_14 from '@salesforce/label/c.CDR_Secondary_Review_and_Confirm_14';

export default class CdrDashboardLandingPage extends NavigationMixin(LightningElement) { 
    
    dRecipient; //claud
    @track activeSecondary = [];  
    @track sharingDisabledFinAccts = []; 
    @track stopSharingAccounts = [];
    @track selectedFinAccts = [];
    @track finAcctDetailsWrapper = [];
    //@track partialAccountsList = [];
    @track cdrFAQLink = '';
    @track cdrJointAcctSharing = false;
    showSelectedFinAccts = false;
    showStopSharingAccounts = false;
    showPartialAccountsSection = true;
    secondaryUserFARMap;
    loaded=true;  
    error; 
    loginLogo = LOGIN_LOGO; 
    cdrLogo=  CDR_LOGO; 

    jsof;
    secDetails;
    softDetails;
    
    //@track deselectedAllAcc = false;
    @track availableFinancialAccounts = [];
    //@track unavailableFinancialAccounts = [];
    //@track selectedFinancialAccounts = [];
    //@track loaded;
    
    legend_color=  'status-style_border status-style_green-color';
    legend_color2=  'status-style_border status-style_grey-color';

    secondaryUser;
    recipient; //claud
    indivTrustAccId;
    isIndivTrust = false;
    consentRecordDetails;
    consentRecordId;



    labels = {                 
        CDR_Secondary_Review_and_Confirm_1,
        CDR_Secondary_Review_and_Confirm_2,
        CDR_Secondary_Review_and_Confirm_3,
        CDR_Secondary_Review_and_Confirm_4,
        CDR_Secondary_Review_and_Confirm_5,
        CDR_Secondary_Review_and_Confirm_6,

        CDR_Secondary_Review_and_Confirm_7,
        CDR_Secondary_Review_and_Confirm_8,
        CDR_Secondary_Review_and_Confirm_9,
        CDR_Secondary_Review_and_Confirm_10,
        CDR_Secondary_Review_and_Confirm_11,
        CDR_Secondary_Review_and_Confirm_12,
        CDR_Secondary_Review_and_Confirm_13,
        CDR_Secondary_Review_and_Confirm_14
    };

    constructor() { 
        super(); 
        Promise.all([ 
            loadStyle(this, cdrCss) 
        ]).then(() => { 
            // console.log("Loaded css"); 
        }).catch(error => { 
            console.error('Error loading static resources', error); 
        }); 
    }

    svgURL = `${CHECK_LOGO}#check_logo`;
    svgURLJointAccount = `${JOINT_ACCOUNT_LOGO}#joint_account_logo`; //Joint changes
    svgRemovedIcon = `${CIRCLE_MINUS_ICON}#circle_minus_icon`; //SFP-62341

    connectedCallback(){

        this.parameters = this.getAccId();
        //console.log(this.parameters );

        if(this.getTrustAccId()){
            this.indivTrustAccId = this.getTrustAccId();
            this.isIndivTrust = true;
            console.log(this.indivTrustAccId + ' ' +  this.isIndivTrust);
        }


       
    //REN added
       
        getSecondaryUser({
            userID: this.parameters
        })
        .then(data => {
            if(data){
                this.secDetails = data;
                let secuser = JSON.parse(JSON.stringify(this.secDetails));
                console.log('testing123', secuser);
                if(!secuser.errorMessage){

                    this.jsof = secuser.SecUserID;
                    console.log('testing246',this.jsof);
                }
                if(this.jsof){

                    this.setSoftName();
                }
                


            }else{
                this.error = error;
                console.log('Error fetching Data Sharing Arrangements: ', error);
                this.showToast('An unexpected error occurred when retrieving data sharing arrangements', 'error', 'Please contact your system administrator.');
            }
        }).catch(error => {
            this.error = error;
            console.log('Error fetching Data Sharing Arrangements: ', error);
            this.showToast('An unexpected error occurred when retrieving data sharing arrangements', 'error', 'Please contact your system administrator.');
        })

          
         
        
        
        

        
        
       
        getSecondaryDataPermissionName({
            myID: this.parameters
        })
        .then(data => {
            if(data){
                this.consentRecordDetails = data;
                let Permission = JSON.parse(JSON.stringify(this.consentRecordDetails));
                //console.log('testing'+JSON.stringify(data));
                if(!Permission.errorMessage){
                    this.secondaryUser = Permission.secondaryName;
                }
            }else{
                this.error = error;
                console.log('Error fetching Data Sharing Arrangements: ', error);
                this.showToast('An unexpected error occurred when retrieving data sharing arrangements', 'error', 'Please contact your system administrator.');
            }
        }).catch(error => {
            this.error = error;
            console.log('Error fetching Data Sharing Arrangements: ', error);
            this.showToast('An unexpected error occurred when retrieving data sharing arrangements', 'error', 'Please contact your system administrator.');
        })
        
        getSecondaryDataPermission({
            accountID: this.parameters
        })
        .then(data => {
            if(data){
                for(var key in data){ 
                    var secondaryData = data[key]; 
                    var newMap = []; 
                    for (var key2 in secondaryData ) { 
                        newMap.push({ 
                            value: secondaryData[key2], 
                            key: key2 
                        }) 
                    } 
                    //If the status is Active, store the arrangement to the activeArrangements 
                   
                        this.activeSecondary.push({value:newMap,  
                            key:key,  
                            recipientName: secondaryData['secondaryUserName']                
                        }); 
                } 
            }else{
                this.error = error;
                console.log('Error fetching Data Sharing Arrangements: ', error);
                this.showToast('An unexpected error occurred when retrieving data sharing arrangements', 'error', 'Please contact your system administrator.');
            }
        }).catch(error => {
            this.error = error;
            console.log('Error fetching Data Sharing Arrangements: ', error);
            this.showToast('An unexpected error occurred when retrieving data sharing arrangements', 'error', 'Please contact your system administrator.');
        })

        let selectedFinAcctsArray = JSON.parse(JSON.stringify(this.getSelectedFAarray()));
        let finAcctDetailsWrapperArray = JSON.parse(JSON.stringify(this.getAvailableFinancialAccountsArray()));
        //let partialAcctsArray = JSON.parse(JSON.stringify(this.getpartialAccessAccountsArray()));
        this.finAcctDetailsWrapper = finAcctDetailsWrapperArray;
        this.selectedFinAccts = selectedFinAcctsArray;
        //this.partialAccountsList = partialAcctsArray;
        //console.log('partialAccountsListsize' + this.partialAccountsList.size())
        let selectedFAs = this.selectedFinAccts;
        let faArray = [];
        if(selectedFAs){
            for(let faData of selectedFAs){
                faArray.push(faData.finAccId);
            }
        }
        
        let faSelectedArray = faArray;

        for(let faData of this.finAcctDetailsWrapper){ 
            if(!faSelectedArray.includes(faData.finAccId)){
                this.stopSharingAccounts.push(faData);
                this.isStopSharingAccount = true;
            }
        }

        this.setAvailableSections();
    }


    async setSoftName(){

        getSoftName({
        softID: this.jsof
      
    })
    .then(data => {
       
        
        if(data){
            this.softDetails = data;
            let SoftData = JSON.parse(JSON.stringify(this.softDetails));
            
            console.log('jsofren',SoftData );
            if(!SoftData.errorMessage){
                    
                this.dRecipient=SoftData.softID;
            }

           


        }else{
            this.error = error;
            console.log('Error fetching Data Sharing Arrangements: ', error);
            this.showToast('An unexpected error occurred when retrieving data sharing arrangements', 'error', 'Please contact your system administrator.');
        }
    }).catch(error => {
        this.error = error;
        console.log('Error fetching Data Sharing Arrangements: ', error);
        this.showToast('An unexpected error occurred when retrieving data sharing arrangements', 'error', 'Please contact your system administrator.');
    })}

    

    async setAvailableSections(){
        if(this.selectedFinAccts.length > 0 && this.stopSharingAccounts.length > 0){
            this.showSelectedFinAccts = true;
            this.showStopSharingAccounts = true;
        }else if(this.selectedFinAccts.length > 0){
            this.showSelectedFinAccts = true;
        }else{
            this.showStopSharingAccounts = true;
        }
    }

    getQueryParameters() {
        var params = {};
        var search = location.search.substring(1);
        if (search) {
            params = JSON.parse('{"' + search.replace(/&/g, '","').replace(/=/g, '":"') + '"}', (key, value) => {
                return key === "" ? value : decodeURIComponent(value)
            });
        }
        return params;
    }

    getAccId() {
        const queryString = window.location.search;
        
        const urlParams = new URLSearchParams(queryString);

        const account = urlParams.get('account');

        //console.log('account Id: ' + account);

        return account;
        
    }

    getTrustAccId() {
        const queryString = window.location.search;
        
        const urlParams = new URLSearchParams(queryString);

        const trustAcc = urlParams.get('trustAcc');

        //console.log('trustAcc Id: ' + trustAcc);

        return trustAcc;
        
    }

    getSelectedFAarray() {
        const queryString = window.location.search;
        
        const urlParams = new URLSearchParams(queryString);

        const getSelectedFAarray = urlParams.get('selectedFaSet');
        
        return JSON.parse(getSelectedFAarray);
        
    }

    getAvailableFinancialAccountsArray() {
        const queryString = window.location.search;
        
        const urlParams = new URLSearchParams(queryString);

        const getAvailableFinancialAccounts = urlParams.get('availableFinancialAccounts');
        
        return JSON.parse(getAvailableFinancialAccounts);
        
    }   

    getpartialAccessAccountsArray() {
        const queryString = window.location.search;
        
        const urlParams = new URLSearchParams(queryString);

        const getpartialAccessAccounts = urlParams.get('partialAccessAccounts');
        
        return JSON.parse(getpartialAccessAccounts);
        
    }   

    //handle ENTER key press on LWC checkbox
    changeToggleValue(event){
        if(event.which == 13){  
            //alert("Selected"+event.target.checked);
            event.target.checked = !event.target.checked;
            this.handleSelect(event);
        }
    }
   
    //handle toast events
    showToast(toastTitle, toastVariant, toastMessage){
        this.dispatchEvent(
            new ShowToastEvent({
                title: toastTitle,
                variant: toastVariant,
                message: toastMessage
            }),
        );
    }

    handleConfirm(event){
        this.loaded = false;

            let secondaryUserPermissionMap = new Map();
            this.selectedFinAccts.forEach(item => {
                if (!secondaryUserPermissionMap.has(item.finAccId)) {
                    secondaryUserPermissionMap.set(item.finAccId, true);
                }
            });
    
            this.stopSharingAccounts.forEach(item => {
                if (!secondaryUserPermissionMap.has(item.finAccId)) {
                    secondaryUserPermissionMap.set(item.finAccId, false);
                }
            });
            
            const jsonString = JSON.stringify([...secondaryUserPermissionMap]);
            //let mapJSON = JSON.stringify(Array.from(secondaryUserPermissionMap.entries()))
            //console.log('secondaryUserPermissionMap' + secondaryUserPermissionMap);


            saveSecondaryUserPermission({
                secondaryUserPermissionSettingsJSON : jsonString,
                secondaryUserAccId : this.parameters
            }).then(result => {
                //this.showToast('Success!', 'success', 'Client Consented Record has been created successfully.');
                //this.showToast('Success!', 'success'); // commenting out since page will be redirected SFP-42970
                this.loaded = true;
                if(this.isIndivTrust){
                    event.preventDefault();
                    this[NavigationMixin.Navigate]({
                        type: 'comm__namedPage',
                        attributes: {
                            pageName: 'manage-secondary-user-instructions'
                        },
                        state: {
                            'account' : this.parameters,
                            'trustAcc': this.indivTrustAccId,
                            'fromConfirmation' : 'true'
                        }
                    });
                }else{
                    event.preventDefault();
                    this[NavigationMixin.Navigate]({
                        type: 'comm__namedPage',
                        attributes: {
                            pageName: 'manage-secondary-user-instructions'
                        },
                        state: {
                            'account' : this.parameters,
                            'fromConfirmation' : 'true'
                        }
                    });
                }
            }).catch(error => {
                this.showToast('Error', 'error', 'An unexpected error occurred when updating Financial Account Role.');
                console.error(error);
                this.loaded = true;
            }); 
    }

    handleCancel(event) {
        if(this.isIndivTrust){
            event.preventDefault();
            this[NavigationMixin.Navigate]({
                type: 'comm__namedPage',
                attributes: {
                    pageName: 'manage-secondary-user-instructions'
                },
                state: {
                    'account' : this.parameters,
                    'trustAcc': this.indivTrustAccId
                }
            });
        }else{
            event.preventDefault();
            this[NavigationMixin.Navigate]({
                type: 'comm__namedPage',
                attributes: {
                    pageName: 'manage-secondary-user-instructions'
                },
                state: {
                    'arrangement': event.currentTarget.dataset.id,
                    'account': this.parameters
                }
            });
        }
        
    }

}