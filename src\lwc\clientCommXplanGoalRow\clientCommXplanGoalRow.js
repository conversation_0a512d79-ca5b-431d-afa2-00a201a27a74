/**
 * Created by jerry.poon on 30/06/2021. 
 */

import {LightningElement, api} from 'lwc';

export default class ClientCommXplanGoalRow extends LightningElement {
    _collapsed = false;
    _details;
    _showDownChevron = false;
    _showUpChevron = true;
    _title = '';

    @api
    get details(){
        return this._details;
    }
    set details(dataDetails){
        this._title = dataDetails.Category__c;
        this._details = this.getFieldDetails(dataDetails);
    }

    get _mainClass(){
        return (this._collapsed) ? 'xplan-details__recordCard xplan-details__recordCard__collapsed slds-grid slds-wrap' : 'xplan-details__recordCard slds-grid slds-wrap';
    }

    handleToggleCollapse(){
        this._collapsed = !this._collapsed;
        this._showDownChevron = this._collapsed;
        this._showUpChevron = !this._collapsed;
    }

    //Gets all the fields values to be shown per goal
    getFieldDetails(details) {
        let fields = [];
        //Owner
        fields.push(this.fieldDetailBuilder(
                'owner',
                details.Owner__c,
                'Owner',
                'text',
                {
                    xmlns: 'http://www.w3.org/2000/svg',
                    class: 'h-5 w-5',
                    fill: '#c3c6c7',
                    viewBox: '0 0 20 20',
                    fillRule: 'evenodd',
                    d: 'M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z',
                    clipRule:'evenodd'
                }
            )
        );

        //Priority
        fields.push(this.fieldDetailBuilder(
                'priority',
                details.Priority__c,
                'Priority',
                'text',
                {
                    xmlns: 'http://www.w3.org/2000/svg',
                    class: 'h-6 w-6',
                    fill: 'none',
                    viewBox: '0 0 24 24',
                    d: 'M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01',
                    strokeLinecap:'round',
                    strokeLinejoin:'round',
                    strokeWidth:'2'
                }
            )
        );
        //Target Amount
        fields.push(this.fieldDetailBuilder('target_amount', details.Target_Amount__c, 'Target Amount', 'currency'));
        //Target Date
        fields.push(this.fieldDetailBuilder('target_date', details.Target_Date__c, 'Target Date', 'date'));
        //Status
        fields.push(this.fieldDetailBuilder(
                'status',
                details.Status__c,
                'Status',
                'text',
                {
                    xmlns: 'http://www.w3.org/2000/svg',
                    class: 'h-6 w-6',
                    fill: 'none',
                    viewBox: '0 0 24 24',
                    d: 'M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z',
                    strokeLinecap:'round',
                    strokeLinejoin:'round',
                    strokeWidth:'2'
                }
            )
        );
        //Description
        fields.push(this.fieldDetailBuilder(
                'description',
                details.Description__c,
                'Description',
                'text'
            )
        );
        return fields;
    }

    //build the object required for child component
    fieldDetailBuilder(name, value, label, type, iconOverride) {
        let field = {};
        field.name = name;
        field.value = value;
        field.fieldMetadata = {label: label, type: type};
        field.iconOverride = iconOverride;
        return field;
    }
}