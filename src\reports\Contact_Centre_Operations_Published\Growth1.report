<?xml version="1.0" encoding="UTF-8"?>
<Report xmlns="http://soap.sforce.com/2006/04/metadata">
    <buckets>
        <bucketType>text</bucketType>
        <developerName>BucketField_8451752</developerName>
        <masterLabel>Business Unit</masterLabel>
        <nullTreatment>n</nullTreatment>
        <sourceColumnName>Case.Created_By_Team__c</sourceColumnName>
        <useOther>false</useOther>
        <values>
            <sourceValues>
                <sourceValue>Bank Contact Centre Team 1</sourceValue>
            </sourceValues>
            <sourceValues>
                <sourceValue>Bank Contact Centre Team 2</sourceValue>
            </sourceValues>
            <sourceValues>
                <sourceValue>Corporate Super Contact Centre</sourceValue>
            </sourceValues>
            <sourceValues>
                <sourceValue>Customer Contact Centre Team 1</sourceValue>
            </sourceValues>
            <sourceValues>
                <sourceValue>Customer Contact Centre Team 2</sourceValue>
            </sourceValues>
            <sourceValues>
                <sourceValue>Customer Contact Centre Team 3</sourceValue>
            </sourceValues>
            <value>Head of Sydney</value>
        </values>
        <values>
            <sourceValues>
                <sourceValue>Contact Centre Bus.Super Invest.&amp; Mature</sourceValue>
            </sourceValues>
            <sourceValues>
                <sourceValue>Customer Contact Centre Team 4</sourceValue>
            </sourceValues>
            <sourceValues>
                <sourceValue>Customer Contact Centre Team 6</sourceValue>
            </sourceValues>
            <sourceValues>
                <sourceValue>Customer Contact Centre Team 8</sourceValue>
            </sourceValues>
            <value>Head of Melb/Bris</value>
        </values>
    </buckets>
    <columns>
        <field>CASE_NUMBER</field>
    </columns>
    <columns>
        <field>Case.Request_ID__c</field>
    </columns>
    <columns>
        <field>RECORDTYPE</field>
    </columns>
    <columns>
        <field>CREATED_DATE</field>
    </columns>
    <columns>
        <field>CREATED</field>
    </columns>
    <columns>
        <field>OWNER</field>
    </columns>
    <columns>
        <field>Case.Case_Owner_Team__c</field>
    </columns>
    <columns>
        <field>STATUS</field>
    </columns>
    <columns>
        <field>Case.Referral_Type__c</field>
    </columns>
    <columns>
        <field>Case.My_AMP__c</field>
    </columns>
    <columns>
        <field>Case.Type__c</field>
    </columns>
    <columns>
        <field>Case.Sub_Type__c</field>
    </columns>
    <filter>
        <booleanFilter>1 AND (2 OR 3 OR 4 OR (5 AND 6)) AND (7 OR 8 OR 9 OR 10 OR 11 OR 12 OR 13 OR 14 OR 15 OR 16)</booleanFilter>
        <criteriaItems>
            <column>RECORDTYPE</column>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>Case.Call_Connect,Case.ContactCentreServiceAU,Case.Contact_Centre_Service_Bank,Case.Complaint_AU,Case.Referral_AU,Case.ServiceRequest,Case.ServiceRequest_ReadOnly</value>
        </criteriaItems>
        <criteriaItems>
            <column>Case.Referral_Type__c</column>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>SMSF,Growth/Sales,Bank Sales,Phone Based Scoped Advice</value>
        </criteriaItems>
        <criteriaItems>
            <column>Case.My_AMP__c</column>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>Accepted</value>
        </criteriaItems>
        <criteriaItems>
            <column>Case.Apply_Online_Offered__c</column>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>1</value>
        </criteriaItems>
        <criteriaItems>
            <column>Case.Type__c</column>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>Super Consolidation</value>
        </criteriaItems>
        <criteriaItems>
            <column>Case.Sub_Type__c</column>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>Campaign Super Match</value>
        </criteriaItems>
        <criteriaItems>
            <column>Case.Created_By_Team__c</column>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>Bank Contact Centre Team 1</value>
        </criteriaItems>
        <criteriaItems>
            <column>Case.Created_By_Team__c</column>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>Bank Contact Centre Team 2</value>
        </criteriaItems>
        <criteriaItems>
            <column>Case.Created_By_Team__c</column>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>Corporate Super Contact Centre</value>
        </criteriaItems>
        <criteriaItems>
            <column>Case.Created_By_Team__c</column>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>Customer Contact Centre Team 1</value>
        </criteriaItems>
        <criteriaItems>
            <column>Case.Created_By_Team__c</column>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>Customer Contact Centre Team 2</value>
        </criteriaItems>
        <criteriaItems>
            <column>Case.Created_By_Team__c</column>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>Customer Contact Centre Team 3</value>
        </criteriaItems>
        <criteriaItems>
            <column>Case.Created_By_Team__c</column>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>Contact Centre Bus.Super Invest.&amp; Mature</value>
        </criteriaItems>
        <criteriaItems>
            <column>Case.Created_By_Team__c</column>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>Customer Contact Centre Team 4</value>
        </criteriaItems>
        <criteriaItems>
            <column>Case.Created_By_Team__c</column>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>Customer Contact Centre Team 6</value>
        </criteriaItems>
        <criteriaItems>
            <column>Case.Created_By_Team__c</column>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>Customer Contact Centre Team 8</value>
        </criteriaItems>
        <language>en_US</language>
    </filter>
    <format>Matrix</format>
    <groupingsAcross>
        <dateGranularity>Month</dateGranularity>
        <field>CREATED_DATEONLY</field>
        <sortOrder>Asc</sortOrder>
    </groupingsAcross>
    <groupingsDown>
        <dateGranularity>Day</dateGranularity>
        <field>BucketField_8451752</field>
        <sortOrder>Asc</sortOrder>
    </groupingsDown>
    <name>Growth</name>
    <params>
        <name>units</name>
        <value>h</value>
    </params>
    <params>
        <name>co</name>
        <value>1</value>
    </params>
    <reportType>CaseList</reportType>
    <scope>organization</scope>
    <showDetails>true</showDetails>
    <showGrandTotal>true</showGrandTotal>
    <showSubTotals>true</showSubTotals>
    <timeFrameFilter>
        <dateColumn>CREATED_DATEONLY</dateColumn>
        <interval>INTERVAL_CUSTOM</interval>
        <startDate>2018-05-01</startDate>
    </timeFrameFilter>
</Report>
