import { LightningElement,api,track } from 'lwc';
 
export default class SelectComp extends LightningElement {
    @api options;
    @api preSelectedValue;
    @api placeholder;
    
    showError = false;
    selectedValue;
    
    get parentClassName(){
        return this.showError?'slds-form-element slds-has-error' : 'slds-form-element' ;
    }


    connectedCallback(){
        this.selectedValue = this.preSelectedValue;
    }

    handleSelectedValue(event){
        this.selectedValue = event.target.value;
        if(this.selectedValue){
            let payload = {
                selectedValue: this.selectedValue
            };
            this.dispatchEvent(new CustomEvent('selectvalue', {detail: payload}));
        }else{
            this.showError = true;
        }  
    }

    @api reportValidity(){
        if(this.selectedValue){
            this.showError = false;
        }else{
            this.showError = true;
        }
        return !this.showError;
    }

}