<template>

    <template if:false={loaded}>
        <lightning-spinner class="loading slds-is-relative" alternative-text="Loading" size="large" style="position: fixed;"></lightning-spinner>
    </template> 
    <template if:true={loaded}>
        <template if:false={showErrorScreen}>
            <div class="back-button">
                <template if:true={isIndivTrust}>
                    <c-cdr-header back-btn-relative-url="secondary-user" business-acc-id={indivTrustAccId} back-enabled="true"></c-cdr-header> 
                </template>
                <template if:false={isIndivTrust}>
                    <c-cdr-header back-btn-relative-url="secondary-user" back-enabled="true"></c-cdr-header>
                </template>
            </div>

        
            <h2 class="slds-text-align_left hurme-text header-spacing font-style_light-bold m-margin">{secondaryUser}'s data sharing
                permissions</h2>

            

            <template if:true={editMode}>
    <!--             <h6 class="slds-text-align_left hurme-text header-spacing font-style_light-bold m-margin slds-m-top_x-large"></h6> -->    

                <div class="slds-border_left">
                    <lightning-layout class="slds-var-p-bottom_large table-header slds-grid_vertical-align-center slds-border_left">
                        <lightning-layout-item size="7" flexibility="auto" padding="around-none" class=" header_left slds-text-align_left slds-align-middle section-header-pad">
                            <h3 class="slds-var-p-top_small hurme-text">Select the account(s) that {secondaryUser} may be able to share</h3>
                            <h6 class="slds-var-p-top_large hurme-text">Keep in mind</h6>
                            <p><i>A secondary user can only share data for accounts that they can also transact on.</i></p>
                        
                        </lightning-layout-item>
                        <template if:true={availableFinancialAccounts}>
                            <lightning-layout-item size="5" flexibility="auto"  padding="around-none" class="slds-text-align_right slds-var-p-right_large slds-float_right">
                            <h6 class="hurme-text">
                                    <lightning-button-stateful  class="slds-button"
                                                                label-when-off="Select all"
                                                                label-when-on="Deselect all"
                                                                selected={deselectedAllAcc}
                                                                onclick={handleSelectAll}
                                                                variant="text">
                                    </lightning-button-stateful>
                                </h6> 
                            </lightning-layout-item>
                        </template>
                    </lightning-layout>
                </div>

                <!-- Available account selection -->
                <template if:true={availableFinancialAccounts}>
                        <div class="accounts-list-box slds-border_left slds-border_bottom slds-border_right">
                            <template for:each={availableFinancialAccounts} for:item="finAcct">
                                <div key={finAcct.finAccId}>
                                    <lightning-layout class="slds-grid_vertical-align-center fin-acct-details financial-accounts-padding">
                                        <lightning-layout-item size="9" flexibility="auto" padding="horizontal-none" class=" slds-align-middle slds-grid slds-grid_vertical">
                                            <div class="slds-col">
                                                <h6 class="acc-name-label">{finAcct.finAccName}
                                                </h6>
                                            </div>
                                            <div class="slds-col">
                                                <p class="text-gray fin-accts-number-text"> 
                                                    <template if:true={finAcct.bsbNumber}>
                                                        BSB <span class="acc-bsb-label">{finAcct.bsbNumber}</span> &nbsp;
                                                    </template> 
                                                        ACC <span class="acc-bsb-label">{finAcct.accNumber}</span> &nbsp;
                                                </p>
                                            </div>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="3" flexibility="auto" padding="around-none" class="slds-text-align_right slds-align-middle">                                                              
                                            <div class = "slds-float_right select-grid-container">
                                                <div class="select-grid-item" >
                                                    <div class="slds-form-element">
                                                        <label class="slds-checkbox_toggle slds-grid">
                                                        <span class="slds-form-element__label slds-m-bottom_none"></span>
                                                        <input
                                                                data-id={finAcct.finAccId}
                                                                checked={finAcct.isSecondaryUserEnabled}
                                                                label={finAcct.finAccName}
                                                                class="toggleSelection"
                                                                type="checkbox" 
                                                                onchange={handleSelect}
                                                                onkeypress={changeToggleValue}
                                                                name="selectFA" 
                                                                value={finAcct.finAccId}/>
                                                            
                                                        <span aria-live="assertive" id={finAcct.finAccId} class="slds-checkbox_faux_container slds-align_absolute-center">
                                                            <span class="slds-checkbox_faux"></span>
                                                                <span class="slds-checkbox_on" aria-label="enabled"></span>
                                                                <span class="slds-checkbox_off" aria-label="disabled"></span>
                                                        </span>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </lightning-layout-item>
                                    </lightning-layout>
                                    <div if:false={finAcct.last} key={finAcct.finAccId} class="slds-border_bottom border-style "></div>
                                </div>
                            </template>
                        </div>
                </template>

                

                    <div class="continue-cancel-footer slds-var-p-bottom_large  slds-grid_vertical-align-center slds-grid_horizontal-align-center">
                        <div>
                            <h5 class="hurme-text">
                                <button type="button"
                                    class="slds-button slds-button_brand button__large button-width_medium ss-button-cont continueDisabled"
                                    onclick={handleContinue}>Continue</button>
                                <button type="button"
                                    class="slds-button slds-button_border button__large  button-width_medium ss-button-cancel slds-align_absolute-center button_color-primaryBlue"
                                    onclick={handleCancel}>Cancel</button>
                            </h5>
                        </div>
                    </div>

            </template>

            
            
            
        
            <template if:false={editMode}>
                <template if:true={availableFinancialAccounts}>

                    <template if:true={availableFinancialAccounts}>
                        <div class="slds-text-align_left header-spacing slds-grid absolute">
                        
                            <div class="slds-col slds-size_1-of-2">
                                <h3 class="hurme-text">Full access</h3>
                            </div>
                            <template if:true={fromConfirmation}>
                                <div class="slds-col slds-size_1-of-2"> 
                                    <button class="slds-button slds-float_right" data-id={consentRecordId} onclick={handleEditAccounts}><p class="edit-label">Edit</p>
                                    <svg xmlns="http://www.w3.org/2000/svg" aria-label="Pencil graphic" viewBox="0 0 52 52" id="edit" class="icon-chevron-style">
                                            <path d="M9.5 33.4l8.9 8.9c.4.4 1 .4 1.4 0L42 20c.4-.4.4-1 0-1.4l-8.8-8.8c-.4-.4-1-.4-1.4 0L9.5 32.1c-.4.4-.4 1 0 1.3zM36.1 5.7c-.4.4-.4 1 0 1.4l8.8 8.8c.4.4 1 .4 1.4 0l2.5-2.5c1.6-1.5 1.6-3.9 0-5.5l-4.7-4.7c-1.6-1.6-4.1-1.6-5.7 0l-2.3 2.5zM2.1 48.2c-.2 1 .7 1.9 1.7 1.7l10.9-2.6c.4-.1.7-.3.9-.5l.2-.2c.2-.2.3-.9-.1-1.3l-9-9c-.4-.4-1.1-.3-1.3-.1l-.2.2c-.3.3-.4.6-.5.9L2.1 48.2z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </template>
                        </div>
                        
                        
                        
                    </template>

                    
                    <template if:false={fromConfirmation}>
                        <template if:false={loadedFromList}>
                            <lightning-spinner class="loading slds-is-relative" alternative-text="Loading" size="large" style="position: fixed;"></lightning-spinner>
                        </template>
                        <template if:true={loadedFromList}>
                                <fieldset class="content__body-main-dashboard slds-p-bottom_medium slds-p-top_xx-large">
        
                                    <div class="slds-border_bottom slds-border_right slds-border_left slds-border_top finAcc-list-box">
                                        <template for:each={availableFinancialAccountsFromList} for:item="finAcct">
                                            <div key={finAcct.finAccId}>
                                                <lightning-layout
                                                    class="slds-var-p-right_medium fin-acct-details slds-grid_vertical-align-center financial-accounts-padding">
                                                    <lightning-layout-item flexibility="auto" padding="horizontal-none"
                                                        class="slds-text-align_left slds-align-middle">
                                                        <div class="slds-grid slds-wrap slds-grid_vertical-align-center">
                                                            <div class="slds-col slds-size_6-of-12">
                                                                <h5 class="slds-text-align_left acc-name-label">{finAcct.finAccName}
                                                                </h5>
                                                                <p class="slds-text-align_left text-gray fin-accts-number-text">
                                                                    <template if:true={finAcct.bsbNumber}>
                                                                        BSB <span class="acc-bsb-label">{finAcct.bsbNumber}</span>
                                                                        &nbsp;
                                                                    </template>
                                                                    ACC <span class="acc-bsb-label">{finAcct.accNumber}</span>
                                                                    &nbsp;
                                                                </p>
                                                            </div>
                                                            <div class="slds-col slds-size_6-of-12 consent-details"
                                                                style="position: relative">
                                                                <p class="text-gray">Permission granted by: {finAcct.changedBy}</p>
                                                                <p class="text-gray">Permission granted on: {finAcct.changedDate}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </lightning-layout-item>
                                                </lightning-layout>
                                                <div if:false={finAcct.last} key={finAcct.finAccId}
                                                    class="slds-border_bottom border-style"></div>
                                            </div>
                                        </template>
                                    </div>
                                </fieldset>
                        </template>
                    </template>
                    

                    <template if:true={fromConfirmation}>
                        <template if:true={activeFinancialAccounts}>
                            <fieldset class="content__body-main-dashboard slds-p-bottom_medium">
                                <!-- <legend class={legend_color}>
                                    <p>SHARING ENABLED</p>
                                </legend> -->
                                <div class="slds-col slds-p-top_xx-large slds-p-bottom_medium"> 
                                    <h5 class="hurme-text">Sharing Enabled</h5>
                                </div> 
                                <!--  <div class="slds-border_top slds-border_bottom slds-border_right slds-border_left finAcc-list-box">
                                    <template for:each={activeFinancialAccounts} for:item="finAcct">
                                        <div key={finAcct.finAccId} class="slds-media slds-var-p-left_large slds-var-p-right_large slds-var-p-bottom_large slds-var-p-top_small">
                                            <div class="slds-media__body slds-grid slds-grid_vertical-align-center slds-var-p-top_xx-small">
                                                <div class="slds-col">
                                                    <h4 class="inline-text">{finAcct.finAccName}</h4>
                                                    <p class="slds-text-align_left text-gray consent-owner-text"> 
                                                        Permission granted by <span class="consent-owner">{finAcct.changedBy}</span>:&nbsp;{finAcct.changedDate}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </div> -->
        
                            
        
                                <div class="slds-border_bottom slds-border_right slds-border_left slds-border_top finAcc-list-box">
                                    <template for:each={activeFinancialAccounts} for:item="finAcct">
                                        <div key={finAcct.finAccId}>
                                            <lightning-layout
                                                class="slds-var-p-right_medium fin-acct-details slds-grid_vertical-align-center financial-accounts-padding">
                                                <lightning-layout-item flexibility="auto" padding="horizontal-none"
                                                    class="slds-text-align_left slds-align-middle">
                                                    <div class="slds-grid slds-wrap slds-grid_vertical-align-center">
                                                        <div class="slds-col slds-size_6-of-12">
                                                            <h5 class="slds-text-align_left acc-name-label">{finAcct.finAccName}
                                                            </h5>
                                                            <p class="slds-text-align_left text-gray fin-accts-number-text">
                                                                <template if:true={finAcct.bsbNumber}>
                                                                    BSB <span class="acc-bsb-label">{finAcct.bsbNumber}</span>
                                                                    &nbsp;
                                                                </template>
                                                                ACC <span class="acc-bsb-label">{finAcct.accNumber}</span>
                                                                &nbsp;
                                                            </p>
                                                        </div>
                                                        <div class="slds-col slds-size_6-of-12 consent-details"
                                                            style="position: relative">
                                                            <p class="text-gray">Permission granted by: {finAcct.changedBy}</p>
                                                            <p class="text-gray">Permission granted on: {finAcct.changedDate}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </lightning-layout-item>
                                            </lightning-layout>
                                            <div if:false={finAcct.last} key={finAcct.finAccId}
                                                class="slds-border_bottom border-style"></div>
                                        </div>
                                    </template>
                                </div>
                            </fieldset>
                            
                        </template>
                    
                        <template if:true={inactiveFinancialAccounts}>
                            <fieldset class="content__body-main-dashboard slds-p-bottom_medium">
        
                                <div class="slds-col slds-p-top_xx-large slds-p-bottom_medium">
                                    <h5 class="hurme-text">Sharing Disabled</h5>
                                </div>
        
                                
                                <!-- <legend class={legend_color2}>
                                    <p>SHARING DISABLED</p>
                                </legend> --> 
        
                                <div class="slds-border_bottom slds-border_right slds-border_left slds-border_top finAcc-list-box">
                                    <template for:each={inactiveFinancialAccounts} for:item="finAcct">
                                        <div key={finAcct.finAccId}>
                                            <lightning-layout
                                                class="slds-var-p-right_medium fin-acct-details slds-grid_vertical-align-center financial-accounts-padding">
                                                <lightning-layout-item flexibility="auto" padding="horizontal-none"
                                                    class="slds-text-align_left slds-align-middle">
                                                    <div class="slds-grid slds-wrap slds-grid_vertical-align-center">
                                                        <div class="slds-col slds-size_6-of-12">
                                                            <h5 class="slds-text-align_left acc-name-label">{finAcct.finAccName}
                                                            </h5>
                                                            <p class="slds-text-align_left text-gray fin-accts-number-text">
                                                                <template if:true={finAcct.bsbNumber}>
                                                                    BSB <span class="acc-bsb-label">{finAcct.bsbNumber}</span>
                                                                    &nbsp;
                                                                </template>
                                                                ACC <span class="acc-bsb-label">{finAcct.accNumber}</span>
                                                                &nbsp;
                                                            </p>
                                                        </div>
                                                    </div>
                                                </lightning-layout-item>
                                            </lightning-layout>
                                            <div if:false={finAcct.last} key={finAcct.finAccId}
                                                class="slds-border_bottom border-style"></div>
                                        </div>
                                    </template>
                                </div>
                            </fieldset>
                            
                        </template>
                    
                        <template if:true={withdrawnFinancialAccounts}>
                            <fieldset class="content__body-main-dashboard slds-p-bottom_medium">
        
                                
                                
                                    <div class="slds-col slds-p-top_xx-large slds-p-bottom_medium">
                                        <h5 class="hurme-text">Sharing Withdrawn</h5>
                                    </div>
                            
        
                                
                            <!--  <legend class={legend_color2}>
                                    <p>WITHDRAWN</p>
                                </legend> -->
        
                                <div class="slds-border_bottom slds-border_right slds-border_left slds-border_top finAcc-list-box ">
                                    <template for:each={withdrawnFinancialAccounts} for:item="finAcct">
                                        <div key={finAcct.finAccId}>
                                            <lightning-layout
                                                class="slds-var-p-right_medium fin-acct-details slds-grid_vertical-align-center financial-accounts-padding">
                                                <lightning-layout-item flexibility="auto" padding="horizontal-none"
                                                    class="slds-text-align_left slds-align-middle">
                                                    <div class="slds-grid slds-wrap slds-grid_vertical-align-center">
                                                        <div class="slds-col slds-size_6-of-12">
                                                            <h5 class="slds-text-align_left acc-name-label">{finAcct.finAccName}
                                                            </h5>
                                                            <p class="slds-text-align_left text-gray fin-accts-number-text">
                                                                <template if:true={finAcct.bsbNumber}>
                                                                    BSB <span class="acc-bsb-label">{finAcct.bsbNumber}</span>
                                                                    &nbsp;
                                                                </template>
                                                                ACC <span class="acc-bsb-label">{finAcct.accNumber}</span>
                                                                &nbsp;
                                                            </p>
                                                        </div>
                                                        <div class="slds-col slds-size_6-of-12 consent-details"
                                                            style="position: relative">
                                                            <p class="text-gray">Permission withdrawn by: {finAcct.changedBy}</p>
                                                            <p class="text-gray">Permission withdrawn on: {finAcct.changedDate}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </lightning-layout-item>
                                            </lightning-layout>
                                            <div if:false={finAcct.last} key={finAcct.finAccId}
                                                class="slds-border_bottom border-style"></div>
                                        </div>
                                    </template>
                                </div>
                            </fieldset>
                            
                        </template>
                    
                        <template if:true={unavailableFinancialAccounts}>
                            <fieldset class="content__body-main-dashboard slds-p-bottom_medium">
        
                                <div class="slds-col slds-p-top_xx-large slds-p-bottom_medium">
                                    <h5 class="hurme-text">Sharing Unavailable</h5>
                                </div>
        
                                
                            <!--  <legend class={legend_color2}>
                                    <p>WITHDRAWN</p>
                                </legend> -->
                                <div class="slds-border_bottom slds-border_right slds-border_left slds-border_top finAcc-list-box">
                                    <template for:each={unavailableFinancialAccounts} for:item="finAcct">
                                        <div key={finAcct.finAccId}>
                                            <lightning-layout
                                                class="slds-var-p-right_medium fin-acct-details slds-grid_vertical-align-center financial-accounts-padding">
                                                <lightning-layout-item flexibility="auto" padding="horizontal-none"
                                                    class="slds-text-align_left slds-align-middle">
                                                    <div class="slds-grid slds-wrap slds-grid_vertical-align-center">
                                                        <div class="slds-col slds-size_6-of-12">
                                                            <h5 class="slds-text-align_left acc-name-label">{finAcct.finAccName}
                                                            </h5>
                                                            <p class="slds-text-align_left text-gray fin-accts-number-text">
                                                                <template if:true={finAcct.bsbNumber}>
                                                                    BSB <span class="acc-bsb-label">{finAcct.bsbNumber}</span>
                                                                    &nbsp;
                                                                </template>
                                                                ACC <span class="acc-bsb-label">{finAcct.accNumber}</span>
                                                                &nbsp;
                                                            </p>
                                                        </div>
                                                    </div>
                                                </lightning-layout-item>
                                            </lightning-layout>
                                            <div if:false={finAcct.last} key={finAcct.finAccId}
                                                class="slds-border_bottom border-style"></div>
                                        </div>
                                    </template>
                                </div>
                            </fieldset>
                        </template>

                        <template if:true={partialAccounts}>

                            <div class="slds-text-align_left header-spacing slds-grid absolute">
                            
                                <div class="slds-col slds-size_1-of-2">
                                    <h3 class="hurme-text">View-only access</h3>
                                </div>
                                <template if:false={availableFinancialAccounts}>
                                    <div class="slds-col slds-size_1-of-2"> 
                                        <button class="slds-button slds-float_right" data-id={consentRecordId} onclick={handleEditAccounts}><p class="edit-label">Edit</p>
                                        <svg xmlns="http://www.w3.org/2000/svg" aria-label="Pencil graphic" viewBox="0 0 52 52" id="edit_partial1" class="icon-chevron-style">
                                                <path d="M9.5 33.4l8.9 8.9c.4.4 1 .4 1.4 0L42 20c.4-.4.4-1 0-1.4l-8.8-8.8c-.4-.4-1-.4-1.4 0L9.5 32.1c-.4.4-.4 1 0 1.3zM36.1 5.7c-.4.4-.4 1 0 1.4l8.8 8.8c.4.4 1 .4 1.4 0l2.5-2.5c1.6-1.5 1.6-3.9 0-5.5l-4.7-4.7c-1.6-1.6-4.1-1.6-5.7 0l-2.3 2.5zM2.1 48.2c-.2 1 .7 1.9 1.7 1.7l10.9-2.6c.4-.1.7-.3.9-.5l.2-.2c.2-.2.3-.9-.1-1.3l-9-9c-.4-.4-1.1-.3-1.3-.1l-.2.2c-.3.3-.4.6-.5.9L2.1 48.2z"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </template>
                            </div>
                            
                            <fieldset class="content__body-main-dashboard slds-p-bottom_medium">
                                <!-- <legend class={legend_color}>
                                    <p>SHARING ENABLED</p>
                                </legend> -->
                                <!--  <div class="slds-border_top slds-border_bottom slds-border_right slds-border_left finAcc-list-box">
                                    <template for:each={activeFinancialAccounts} for:item="finAcct">
                                        <div key={finAcct.finAccId} class="slds-media slds-var-p-left_large slds-var-p-right_large slds-var-p-bottom_large slds-var-p-top_small">
                                            <div class="slds-media__body slds-grid slds-grid_vertical-align-center slds-var-p-top_xx-small">
                                                <div class="slds-col">
                                                    <h4 class="inline-text">{finAcct.finAccName}</h4>
                                                    <p class="slds-text-align_left text-gray consent-owner-text"> 
                                                        Permission granted by <span class="consent-owner">{finAcct.changedBy}</span>:&nbsp;{finAcct.changedDate}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </div> -->
        
                            
        
                                <div class="slds-m-top_xx-large slds-border_bottom slds-border_right slds-border_left slds-border_top finAcc-list-box">
                                    <template for:each={partialAccounts} for:item="finAcct">
                                        <div key={finAcct.finAccId}>
                                            <lightning-layout
                                                class="slds-var-p-right_medium fin-acct-details slds-grid_vertical-align-center financial-accounts-padding">
                                                <lightning-layout-item flexibility="auto" padding="horizontal-none"
                                                    class="slds-text-align_left slds-align-middle">
                                                    <div class="slds-grid slds-wrap slds-grid_vertical-align-center">
                                                        <div class="slds-col slds-size_6-of-12">
                                                            <h5 class="slds-text-align_left acc-name-label">{finAcct.finAccName}
                                                            </h5>
                                                            <p class="slds-text-align_left text-gray fin-accts-number-text">
                                                                <template if:true={finAcct.bsbNumber}>
                                                                    BSB <span class="acc-bsb-label">{finAcct.bsbNumber}</span>
                                                                    &nbsp;
                                                                </template>
                                                                ACC <span class="acc-bsb-label">{finAcct.accNumber}</span>
                                                                &nbsp;
                                                            </p>
                                                        </div>
                                                    </div>
                                                </lightning-layout-item>
                                            </lightning-layout>
                                            <div if:false={finAcct.last} key={finAcct.finAccId}
                                                class="slds-border_bottom border-style"></div>
                                        </div>
                                    </template>
                                </div>
                            </fieldset>
                    
                        </template>
                    </template>
                
                </template>

                <template if:false={fromConfirmation}>
                    <template if:true={showPartialAccountsFromList}>

                        <div class="slds-text-align_left header-spacing">
                        
                                <p class="hurme-text"><i>{secondaryUser} cannot share data on any of the accounts listed below as they do not have transactional access to the accounts.  

                                    If you would like to enable data sharing for {secondaryUser}, please follow the instructions on <a target="_blank" href={cdrFAQLink}><u class="link-blue">Consumer Data Right FAQs</u></a></i></p>
                        </div>

                        <div class="slds-text-align_left header-spacing slds-grid absolute">
                        
                            <div class="slds-col slds-size_1-of-2">
                                <h3 class="hurme-text">View-only access</h3>
                            </div>
                            <template if:false={availableFinancialAccounts}>
                                <div class="slds-col slds-size_1-of-2"> 
                                    <button class="slds-button slds-float_right" data-id={consentRecordId} onclick={handleEditAccounts}><p class="edit-label">Edit</p>
                                    <svg xmlns="http://www.w3.org/2000/svg" aria-label="Pencil graphic" viewBox="0 0 52 52" id="edit_partial" class="icon-chevron-style">
                                            <path d="M9.5 33.4l8.9 8.9c.4.4 1 .4 1.4 0L42 20c.4-.4.4-1 0-1.4l-8.8-8.8c-.4-.4-1-.4-1.4 0L9.5 32.1c-.4.4-.4 1 0 1.3zM36.1 5.7c-.4.4-.4 1 0 1.4l8.8 8.8c.4.4 1 .4 1.4 0l2.5-2.5c1.6-1.5 1.6-3.9 0-5.5l-4.7-4.7c-1.6-1.6-4.1-1.6-5.7 0l-2.3 2.5zM2.1 48.2c-.2 1 .7 1.9 1.7 1.7l10.9-2.6c.4-.1.7-.3.9-.5l.2-.2c.2-.2.3-.9-.1-1.3l-9-9c-.4-.4-1.1-.3-1.3-.1l-.2.2c-.3.3-.4.6-.5.9L2.1 48.2z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </template>
                        </div>
                        <div>
                            <fieldset class="content__body-main-dashboard slds-p-bottom_medium">
                                <!-- <legend class={legend_color}>
                                    <p>SHARING ENABLED</p>
                                </legend> -->
                                <!--  <div class="slds-border_top slds-border_bottom slds-border_right slds-border_left finAcc-list-box">
                                    <template for:each={activeFinancialAccounts} for:item="finAcct">
                                        <div key={finAcct.finAccId} class="slds-media slds-var-p-left_large slds-var-p-right_large slds-var-p-bottom_large slds-var-p-top_small">
                                            <div class="slds-media__body slds-grid slds-grid_vertical-align-center slds-var-p-top_xx-small">
                                                <div class="slds-col">
                                                    <h4 class="inline-text">{finAcct.finAccName}</h4>
                                                    <p class="slds-text-align_left text-gray consent-owner-text"> 
                                                        Permission granted by <span class="consent-owner">{finAcct.changedBy}</span>:&nbsp;{finAcct.changedDate}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </div> -->
    
                            
    
                                <div class="slds-m-top_xx-large slds-border_bottom slds-border_right slds-border_left slds-border_top finAcc-list-box">
                                    <template for:each={partialAccountsFromList} for:item="finAcct">
                                        <div key={finAcct.finAccId}>
                                            <lightning-layout
                                                class="slds-var-p-right_medium fin-acct-details slds-grid_vertical-align-center financial-accounts-padding">
                                                <lightning-layout-item flexibility="auto" padding="horizontal-none"
                                                    class="slds-text-align_left slds-align-middle">
                                                    <div class="slds-grid slds-wrap slds-grid_vertical-align-center">
                                                        <div class="slds-col slds-size_6-of-12">
                                                            <h5 class="slds-text-align_left acc-name-label">{finAcct.finAccName}
                                                            </h5>
                                                            <p class="slds-text-align_left text-gray fin-accts-number-text">
                                                                <template if:true={finAcct.bsbNumber}>
                                                                    BSB <span class="acc-bsb-label">{finAcct.bsbNumber}</span>
                                                                    &nbsp;
                                                                </template>
                                                                ACC <span class="acc-bsb-label">{finAcct.accNumber}</span>
                                                                &nbsp;
                                                            </p>
                                                        </div>
                                                    </div>
                                                </lightning-layout-item>
                                            </lightning-layout>
                                            <div if:false={finAcct.last} key={finAcct.finAccId}
                                                class="slds-border_bottom border-style"></div>
                                        </div>
                                    </template>
                                </div>
                            </fieldset>
                        </div>
                        
                        
                
                    </template>

                    <div class="continue-cancel-footer slds-var-p-bottom_large  slds-grid_vertical-align-center slds-grid_horizontal-align-center">
                        <div>
                            <h5 class="hurme-text">
                                <button type="button"
                                    class="slds-button slds-button_brand button__large button-width_medium ss-button-cont continueDisabled"
                                    onclick={handleContinueFromList}
                                    disabled={enableContinue}>Continue</button>
                            </h5>
                        </div>
                    </div>
                </template>
                

            </template>
            
        </template>
        <template if:true={showErrorScreen}>
            <div class="slds-p-vertical_medium slds-grid slds-align_absolute-center">

                    <img src={svgURL} alt="Error Icon" class="slds-float_left error-icon"/>         
                    <h3 class="slds-p-horizontal_large hurme-text">Oops! Something went wrong</h3>
            </div>
            <div class="slds-p-vertical_medium slds-grid slds-align_absolute-center">
                    <p class="slds-text-align_left">Please close this window and try again. If the problem persists, please contact AMP Bank on 13 30 30 between 8am-8pm Monday to Friday, or 9am-5pm Saturday and Sunday (AEST)</p>
            </div>
        </template>    
    </template>

    
</template>