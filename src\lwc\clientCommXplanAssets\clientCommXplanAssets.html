<!--
 - Created by jerry.poon on 1/07/2021.
 -->

<!-- Client Comm Xplan Assets -->
<template> 
    <section class={_mainClass}>
        <div class="xplan-details__sectionHead">
            <div class="slds-media slds-media_center">
                <!-- Title -->
                <div class="slds-col">
                    Assets
                </div>
                <!-- Chevron -->
                <div class="slds-no-flex">
                    {_assetTotal}
                    <div class="xplan-details__headerButton" onclick={handleToggleCollapse}>
                        <template if:true={_showDownChevron}>
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>
                        </template>
                        <template if:true={_showUpChevron}>
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path></svg>
                        </template>
                    </div>
                </div>
            </div>
        </div>
        <!-- Details -->
        <div class="xplan-asset-liability__body slds-m-left_x-small">
            <lightning-datatable
                    key-field="index"
                    columns={_columns}
                    data={_tableData}
                    hide-checkbox-column
                    default-sort-direction={_defaultSortDirection}
                    sorted-direction={_sortDirection}
                    sorted-by={_sortedBy}
                    onsort={onHandleSort}>
            </lightning-datatable>
        </div>
    </section>
</template>