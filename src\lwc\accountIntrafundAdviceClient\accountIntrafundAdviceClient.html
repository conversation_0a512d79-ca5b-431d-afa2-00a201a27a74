<template>
    <!-- Intrafund Advice Client Section -->
    <div data-id="ifa-checkbox-section" class="slds-section ifa-client">
        <h3 class="slds-section__title ">
            <template if:true={chevronDown}>
                <button class="slds-float_left slds-button slds-button slds-section__title-action" onclick={toggleIFASection}>
                    <lightning-icon class="slds-button__icon slds-button__icon_left" size="x-small" icon-name="utility:chevrondown"></lightning-icon>&nbsp;{ifaSectionLabel}
                </button>
            </template>
            <template if:false={chevronDown}>
                <button class="slds-float_left slds-button slds-button slds-section__title-action" onclick={toggleIFASection}>
                    <lightning-icon class="slds-button__icon slds-button__icon_left" size="x-small" icon-name="utility:chevronright"></lightning-icon>&nbsp;{ifaSectionLabel}
                </button>
            </template>
        </h3>
        <!-- Edit Mode-->
        <template if:true={isEditMode}>
            <div class="slds-section__content" aria-hidden="false" >
                <div if:false={isProcessDone} class="slds-is-relative ifa-spinner">
                    <lightning-spinner variant="brand" size="medium"> </lightning-spinner>
                </div>
                <div class="slds-p-bottom_small">
                    <div class="slds-p-left_medium slds-p-right_medium">
                        <div class="slds-grid">
                            <div class="slds-col slds-size_1-of-2 slds-p-left_none slds-p-right_medium">
                                <div class="slds-form-element">
                                    <label class="slds-checkbox__label">
                                        <span class="slds-form-element__label">{ifaCheckboxLabel}</span>
                                    </label>
                                    <!-- Custom Checkbox -->
                                    <div class="slds-form-element__control">
                                        <template if:true={isIFAClient}>
                                            <lightning-input class="slds-form-element__static slds-form-element" type="checkbox" onchange={handleCheckboxChange} checked></lightning-input>
                                        </template>
                                        <template if:false={isIFAClient}>
                                            <lightning-input class="slds-form-element__static slds-form-element" type="checkbox" onchange={handleCheckboxChange}></lightning-input>
                                        </template>
                                    </div>
                                </div>
                            </div>
                            <div class="slds-col slds-size_1-of-2 slds-p-left_medium slds-p-right_none">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Footer Buttons -->
            <div class="slds-text-align_center slds-border_top slds-p-top_medium ifa-footer-buttons">
                <ul class="slds-button-group-row center-align-buttons">
                    <li class="slds-button-group-item visible">
                        <lightning-button label="Cancel" onclick={cancelEditMode}></lightning-button>
                    </li>
                    <li class="slds-button-group-item visible">
                        <template if:true={disableSaveBtn}>
                            <lightning-button label="Save" onclick={procGoldenRecAccess} variant="brand" disabled></lightning-button>
                        </template>
                        <template if:false={disableSaveBtn}>
                            <lightning-button label="Save" onclick={procGoldenRecAccess} variant="brand"></lightning-button>
                        </template>
                    </li>
                </ul>
            </div>
        </template>
        <!-- View Mode -->
        <template if:false={isEditMode}>
            <div class="slds-section__content" aria-hidden="false">
                <div class="slds-grid">
                    <div class="slds-col slds-size_1-of-2 slds-p-left_none">
                        <div class="slds-form__item" role="listitem">
                            <div class="slds-form-element slds-form-element_edit slds-form-element_readonly slds-form-element_stacked slds-hint-parent">
                                <div class="slds-form-element__control">
                                    <div class="slds-form-element__static" >
                                        <div class="slds-form-element">
                                            <div class="slds-form-element slds-form-element_stacked">
                                                <label class="slds-checkbox__label">
                                                    <span class="slds-form-element__label">{ifaCheckboxLabel}</span>
                                                </label>
                                                <!-- Custom Checkbox -->
                                                <div class="slds-form-element__control">
                                                    <template if:true={isIFAClient}>
                                                        <lightning-input class="slds-form-element__static slds-form-element" type="checkbox" checked disabled></lightning-input>
                                                    </template>
                                                    <template if:false={isIFAClient}>
                                                        <lightning-input class="slds-form-element__static slds-form-element" type="checkbox" disabled></lightning-input>
                                                    </template>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Edit Button Icon -->
                                    <span class="edit-button_visibility">
                                        <lightning-button-icon icon-name="utility:edit" variant="bare" disabled={disableEditBtn} onclick={enableEditMode} alternative-text="Process IFA Access" title="Process IFA Access"></lightning-button-icon>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="slds-col slds-size_1-of-2 slds-p-right_none">
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>