import { LightningElement, track, wire, api } from 'lwc'; 
import getDataSharingArragements from '@salesforce/apex/CdrLandingDashboardController.getDataSharingArragementsForSecondaryUsers'; 
import fetchCDRSetting from '@salesforce/apex/CdrLandingDashboardController.fetchCDRSetting'; 
//Toast events
import {ShowToastEvent} from "lightning/platformShowToastEvent" 
//For navigation
import { NavigationMixin } from 'lightning/navigation'; 
//CSS from static resource
import { loadStyle } from 'lightning/platformResourceLoader'; 
import cdrCss from '@salesforce/resourceUrl/CDR_Styles'; 
import LOGIN_LOGO from '@salesforce/resourceUrl/login_logo';
// Custom labels
import AMP_CONTACT_INFO from '@salesforce/label/c.CDR_AMP_Bank_Contact_Info';
import NO_ACTIVE_CONSENT_RECORDS from '@salesforce/label/c.CDR_No_Active_Consent_Records';
import NO_CONSENT_RECORDS from '@salesforce/label/c.CDR_No_Any_Consent_Records';
import CDR_FOR_MORE_INFO_TEXT from '@salesforce/label/c.CDR_For_More_Info_Text';

import CDR_Secondary_User_Sharing from '@salesforce/label/c.CDR_Secondary_User_Sharing';
import CDR_Secondary_User_Sharing_Sub from '@salesforce/label/c.CDR_Secondary_User_Sharing_Sub';

export default class cdrSecondaryUserDashboardLandingPage extends NavigationMixin(LightningElement) {

        
    @track activeArrangements = []; 
    @track archivedArrangements = []; 
    @track cdrFAQLink = '';
    @track cdrJointAcctSharing = false; //SFP-61733

    loaded; 
    error; 
    noConsentRecords = false;   // added by J.Mendoza (SFP-33107) 
    loginLogo = LOGIN_LOGO;     // added by J.Mendoza (SFP-33107) 
    labels = {                  // added by J.Mendoza (SFP-33107) 
        AMP_CONTACT_INFO,
        NO_ACTIVE_CONSENT_RECORDS,
        NO_CONSENT_RECORDS,
        CDR_FOR_MORE_INFO_TEXT,

        CDR_Secondary_User_Sharing,
        CDR_Secondary_User_Sharing_Sub
    };

    ownerAccId;
    ownerName;

    constructor() { 
        super(); 
        Promise.all([ 
            loadStyle(this, cdrCss) 
        ]).then(() => { 
            // console.log("Loaded css"); 
        }).catch(error => { 
            console.error('Error loading static resources', error); 
        }); 
    }

    connectedCallback(){

        if(this.getOwnerAccId()){
            this.ownerAccId = this.getOwnerAccId();
        }

        getDataSharingArragements({ownerAccountId: this.ownerAccId})
        .then(data => {
            if(data){
                //remove spinner when loaded
                this.loaded = true; 
                for(var key in data){ 
                    var arrangementData = data[key]; 
                    var newMap = []; 
                    for (var key2 in arrangementData) { 
                        newMap.push({ 
                            value: arrangementData[key2], 
                            key: key2 
                        }) 
                    } 
    
                    // let consentGrantedDate = new Date(arrangementData['consentGranted']); 
                    let consentGrantedDate = new Date((arrangementData['consentGranted']).replace(/-/g, '/')); 
                    // let consentGrantedDate3 = new Date((arrangementData['consentGranted']).replace(' ', 'T'));
                    let consentGrantedDisplayDate = consentGrantedDate.toLocaleDateString('en-GB', { day: 'numeric', month: 'short', year: 'numeric' });
                    // let consentEndedDate = new Date(arrangementData['consentEnded']); 
                    let consentEndedDate = new Date((arrangementData['consentEnded']).replace(/-/g, '/')); 
                    let consentEndedDisplayDate = consentEndedDate.toLocaleDateString('en-GB', { day: 'numeric', month: 'short', year: 'numeric' }); 
                    this.ownerName = arrangementData['consentAccountOwner'];
    
                    //If the status is Active, store the arrangement to the activeArrangements 
                    if(arrangementData['status'] == 'Active'){ 
                        this.activeArrangements.push({value:newMap,  
                            key:key,  
                            recipientName: arrangementData['recipientName'], 
                            consentGranted: consentGrantedDisplayDate,
                            consentOwner: arrangementData['consentOwner'],
                            recipientIcon: arrangementData['recipientIcon'],
                            status: arrangementData['status']                 
                        }); 
                    } 
                    //If the status is Cancelled, Expired or Once-Off , store the arrangement to the archivedArrangements
                    else { 
                        this.archivedArrangements.push({value:newMap,  
                            key:key,  
                            recipientName: arrangementData['recipientName'], 
                            consentEnded: consentEndedDisplayDate,
                            consentOwner: arrangementData['consentOwner'], 
                            recipientIcon: arrangementData['recipientIcon'],
                            status: arrangementData['status']                 
                        }); 
                    } 
                } 
    
                if(this.activeArrangements.length === 0){ 
                    this.activeArrangements = false;
                }
    
                if(this.archivedArrangements.length === 0){
                    this.archivedArrangements = false;
                }
    
                // SFP-33107 by J.Mendoza 09/10/2020
                if(!this.activeArrangements && !this.archivedArrangements){
                    this.noConsentRecords = true;
                }
            }
            else{
                this.error = error;
                console.log('Error fetching Data Sharing Arrangements: ', error);
                this.showToast('An unexpected error occurred when retrieving data sharing arrangements', 'error', 'Please contact your system administrator.');
            }
        }).catch(error => {
            this.error = error;
            console.log('Error fetching Data Sharing Arrangements: ', error);
            this.showToast('An unexpected error occurred when retrieving data sharing arrangements', 'error', 'Please contact your system administrator.');
        })

        //SFP-41725
        fetchCDRSetting({ 
            cdrRecord: 'CDR Management Settings'
        }).then(data => {
            if(data){
                let cdrMngtSettings = JSON.parse(data);
                this.cdrFAQLink = cdrMngtSettings.CDR_FAQ__c;
                this.cdrJointAcctSharing = cdrMngtSettings.Joint_Accounts_Sharing__c; //SFP-61733
            }      
            else{
                console.log('Error fetching CDR Management Settings: ', error);
            }
        }).catch(error => {
            console.log('Error fetching CDR Management Settings: ', error);
        });  
    }

    //handle toast events
    showToast(toastTitle, toastVariant, toastMessage){
        this.dispatchEvent(
            new ShowToastEvent({
                title: toastTitle,
                variant: toastVariant,
                message: toastMessage
            }),
        );
    }

    //Handle tile clicks
    tileOnclick(event) {
        event.preventDefault();
        //Navigate to /data-sharing-arrangement?arrangement=arrangementId
        this[NavigationMixin.Navigate]({
            type: 'comm__namedPage',
            attributes: {
                pageName: 'secondaryuser-data-sharing-arrangement'
            },
            state: {
                'arrangement': event.currentTarget.dataset.id,
                'account': this.ownerAccId
            }
        });
        return false;
    }


    getOwnerAccId() {
        const queryString = window.location.search;
        
        const urlParams = new URLSearchParams(queryString);

        const account = urlParams.get('account');

        //console.log('account Id: ' + account);

        return account;
        
    }
}