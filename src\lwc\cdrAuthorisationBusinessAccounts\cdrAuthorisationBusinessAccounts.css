.content__body-dashboard {          
    margin-top:0px;
}

a {
    max-width: 35em;
    font-size: 14px;
}

.slds-popover__body, .slds-popover__body ul li {
    font-size: 14px;
    line-height: 160%;
}

.footer_consent {
    padding: 24px 28px;
    text-align: left;
    margin-left: 128px;
    
}

.footer_consent-message {
    display:block;
    font-style: italic;
    text-align: center;
    margin-left: auto;
    margin-right: auto;
}

.m-margin {
    line-height: 160%;
}

span.c-grouped-text {
    line-height: 160%;
}

img.cdr-logo-style {
    width: auto;
    max-height: 80px;
    height: auto;
}

.popover-style {
    max-width: 30em;
    width: auto;
    position:absolute;
    bottom:2rem;
    left:-1rem;
}

.slds-button, .slds-button_stateful{
    min-width: 45px;
    min-height: 45px;
    cursor: pointer;
}

.help-text {
    color: #31528e;
}

.slds-checkbox_faux_container{
    min-width: 45px;
    min-height: 45px;
    cursor:pointer;
}

.unavailable_text{
    width:100%;
}

.unavailable_container{
    text-align: right;
}

.unavailable_icon{
    min-width: 44px;
}


.fa_status{
    padding-top: 10px;
    margin-right: 50px;
}

.status_icon {
    min-width: 44px;
    padding-right: 8px;
}

button.icon-back-style-data-sharing {
    position:relative;
    z-index: 9;
}

.back-button{
    padding-bottom: 2em;
}


.status_icon {
    min-width: 44px;
    padding-right: 8px;
}

.slds-theme_warning {
    background-color: white;
    color: #FEF8EC;
    border-left-width: 3px;
    border-left-style: solid;
    border-left-color: #F3BA40;
    border-radius: 0;
    min-width: unset;
}


.toast-margin{
    margin-top:30px;
}

.toast-message{
    font-size: 14px;
    padding-top: 6px;
    color: black;
    padding-right: 22px;
    font-weight: 600; 
    line-height: 160%;
    
}

.slds-theme_warning{
    background-color :#FEF8EC;
}

.header-text {
    padding-bottom: 0;
}

.company-details{
    padding-top: 40px;
}

.joint-account-sharing_desc{
    float: left;
}