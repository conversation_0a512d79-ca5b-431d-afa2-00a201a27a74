<template>

    <template for:each={_apptDates} for:item="monthYear" >
        <h2 key={monthYear.key}>{monthYear.key}</h2>
        <ul role="list" key={monthYear.key}>
            <template for:each={monthYear.value} for:item="apptDate" >
                <li key={apptDate.id} role="listitem" class={apptDate.class} style={apptDate.style}>
                    <div class="date slds-size_3-of-12"><strong>{apptDate.date.day}</strong>
                        {apptDate.date.month}</div>
                    <div class="description slds-size_9-of-12"><strong class="rowHeading">{apptDate.type}</strong>
                        Upcoming appointment date is scheduled on <strong>{apptDate.date.formatted}</strong>, 
                        with <lightning-formatted-rich-text value={_adviserName}></lightning-formatted-rich-text> for your <strong>{apptDate.type}</strong>.</div>
                </li>
            </template>
        </ul>
    </template>
</template>