.outcomeSelector{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.outcomeSelector.outcomeSelector_show{
    display: block;
}
.outcomeSelector > div{
    display: flex;
    justify-content: center;
    align-items: start;
}

.outcomeSelector .roundedBackground{
    padding: 1rem;
    border-radius: 4px;
    background: #fff;
    min-width: 80%;
    box-shadow: var(--sds-c-modal-shadow, var(--lwc-shadowDropDown,0 2px 3px 0 rgba(0, 0, 0, 0.16)));
    border: 1px solid #dddbda;
    z-index: 9999;
    position: relative;
}

.outcomeSelector .closeMiniModal{
    position: absolute;
    right: 0;
    top: 0;
    padding: 0.5rem;
}

.outcomeSelector .slds-backdrop{
    background: rgb(255,255,255, 0.85);
    position: absolute;
}

.outcomeSelector .item-description{
    font-size: .625rem;
    color: #706e6b;
    display: block;
}
.outcomeSelector .heading{
    color: var(--lwc-colorTextLabel,rgb(62, 62, 60));
    /* font-size: var(--lwc-formLabelFontSize,0.75rem); */
    font-weight: bold;
}

.addButton {
    padding-right: 10px;
}

.docHeader {
    display: inherit;
}