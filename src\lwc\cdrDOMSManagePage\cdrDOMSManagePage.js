/** 
 * <PERSON> (Accenture) SFP-60529
 */
 import { LightningElement, track, wire, api } from 'lwc';
 import { loadStyle } from "lightning/platformResourceLoader";

 //For navigation
 import { NavigationMixin } from 'lightning/navigation';

 // CSS from static resource
 import cdrCss from '@salesforce/resourceUrl/CDR_Styles';
 import CHECK_LOGO from '@salesforce/resourceUrl/check_logo';
 import ERROR_LOGO from '@salesforce/resourceUrl/error_logo';
 import WARNING_LOGO from '@salesforce/resourceUrl/warning_logo';

 // Toast events
 import {ShowToastEvent} from "lightning/platformShowToastEvent"

 // Apex controller methods
 import getAJointFinancialAccountDetails from '@salesforce/apex/CdrDOMSLandingController.getAJointFinancialAccountDetails';
 import dOMSRecordUpdate from '@salesforce/apex/CdrDOMSLandingController.dOMSRecordUpdate';

 //Label
 import disablingFaqs from '@salesforce/label/c.DOMS_Manage_disabling_faqs';
 import enablingFaqs from '@salesforce/label/c.DOMS_Manage_enabling_faqs';
 import enablingConfirmation from '@salesforce/label/c.DOMS_Data_Sharing_enablement_confirmation';
 import disablingConfirmation from '@salesforce/label/c.DOMS_Data_Sharing_disable_confirmation';
 

export default class cdrDOMSManagePage extends NavigationMixin(LightningElement) {

    @track faId;
    @track parameters;
    @track loaded;
    @track isModalOpen;
    @track faStatus;
    @track faSubStatus;
    @track fa;
    @track faAcctLabel;
    @track faBsbLabel;
    @track faIsDOMSStatusEnabled;
    @track farIsThisUserEnabled;
    @track isClosedAccount;
    @track farId;
    @track farIdList = [];
    @track retMessage;
    @track availabilityType; // VALUE REFERENCE 5: (Partial disabled but other joint owner fault) 4: no user enabled   2: you enabled but others don't   3:you dont enabled but others do   1:enabled
    @track initializeToast;
    @track hasError = false;

    svgURL = `${CHECK_LOGO}#check_logo`;
    svgURLError = `${ERROR_LOGO}#error_logo`;
    svgURLWarning = `${WARNING_LOGO}#warning_logo`;

    label = {
        disablingFaqs,
        enablingFaqs,
        enablingConfirmation,
        disablingConfirmation
    };

    constructor() { 
        super(); 
        Promise.all([ 
            loadStyle(this, cdrCss) 
        ]).then(() => { 
            console.log("Loaded css"); 
        }).catch(error => { 
            console.error('Error loading static resources', error); 
        }); 
    }

    //This is the page initialization method
    connectedCallback(){
        this.parameters = this.getQueryParameters();
        this.faId = this.parameters.faId;

        //The CdrDOMSLandingController method call
        getAJointFinancialAccountDetails({
            faId: this.faId
        }).then(data => {
            //console.log('data return');
            if(data){
                //console.log('with data return');
                //console.log(data);
                for(let faData of data){
                    this.faStatus = faData.status;
                    this.faSubStatus = faData.disabledSubStatus;
                    this.fa = faData.finAcct;
                    if(faData.finAcct.Bank_BSB__c != null){
                        this.faBsbLabel = faData.finAcct.Bank_BSB__c.split('').join(' ');
                    }
                    this.faAcctLabel = faData.finAcct.FinServ__FinancialAccountNumber__c.split('').join(' ');
                    this.faIsDOMSStatusEnabled = faData.isDOMSStatusEnabled;
                    this.farIsThisUserEnabled = faData.userFAROfThisFA.Disclosure_Setting__c;
                    this.farId = faData.userFAROfThisFA.Id;
                    this.farIdList.push(this.farId);
                    this.availabilityType = faData.availabilityType;
                    this.isClosedAccount = faData.isClosedAccount;
                    //console.log('Fa name'+this.fa.Name);
                    //console.log('Status'+this.faStatus);
                }
                this.hasError = false;
                this.loaded = true;
            } else {
                //console.log('without data return');              
                this.hasError = true;
                console.error('Error fetching Financial Account information: ', 'No data received'); 
                this.showToast('An unexpected error occurred when retrieving financial accounts', 'error', 'Please contact AMP Bank.');
            }
        }).catch(error => {
            //console.error(error); //This is needed in case there are untracebale error in JavaScript such as the profile doesn't have access to this JS
            this.hasError = true;
            console.error('Error fetching Financial Account information: ', error); 
            this.showToast('An unexpected error occurred when retrieving financial accounts', 'error', 'Please contact AMP Bank.');
        });

    }

    //This method is to changes the FAR disclosure setting when clicking the 'Yes, I confirm' button on the confirmation modal
    handleChangeDisclosure(event){
        event.preventDefault();
        this.loaded = false; //show the spinner during far update

        //The CdrDOMSLandingController method call
        dOMSRecordUpdate({
        farIdList : this.farIdList,
        disclosureSetUpdate : !this.farIsThisUserEnabled
        })
        .then(result => {
            //console.log(result);  
            
            if(result == 'Success'){
                this.farIsThisUserEnabled = !this.farIsThisUserEnabled;
                event.preventDefault();

                //Prepare variables needed by DOMS landing page after changing disclosure settings
                // 4 - no user enabled   2 - you enabled but others don't   3 - you dont enabled but others do     1 - enabled
                if(this.farIsThisUserEnabled && this.availabilityType == 4){ //from 4 - no user enabled
                    //console.log('into 2 - you enabled but others dont');
                    this.newStatus = '2'; //into 2 - you enabled but others don't
                } else if(this.farIsThisUserEnabled && this.availabilityType == 3) { //from 3 you dont enabled but others do
                    //console.log('into 1 - enabled');
                    this.newStatus = '1'; //into 1 - enabled
                }  else if(this.farIsThisUserEnabled && this.availabilityType == 5){
                    //console.log('into 1 - enabled');
                    this.newStatus = '2'; //into 2 - you enabled but others don't
                }else if (!this.farIsThisUserEnabled && this.availabilityType == 1){ //from  1 -enabled
                    //console.log('into 3 - you dont enabled but others do ');
                    this.newStatus = '3'; //into 3 - you dont enabled but others do 
                } else if (!this.farIsThisUserEnabled && this.availabilityType == 2){ //from 2 you enabled but others don't
                    //console.log('into 4 - no user enabled');
                    this.newStatus = '4';  //into 4 - no user enabled
                }
                this.initializeToast = '1'; //Var for banner

                //Go back to DOMS landing page when update success
                this[NavigationMixin.Navigate]({
                    type: 'comm__namedPage',
                    attributes: {
                        pageName: 'joint-accounts-sharing'
                    },
                   state: {
                       'jsharingStatus' : this.newStatus,
                       'notif' : this.initializeToast,
                    }
                });
            } else {
                //Show a toast when update fails
                console.error('Error changing Financial Account disclosure information: ', result); 
                this.showToast('An unexpected error occurred when changing financial account disclosure status', 'error', 'Please contact AMP Bank.');
                this.loaded = true;
            } 

        }).catch (error => {    
            this.isModalOpen = false;
            this.loaded = true;
            console.error('Error changing Financial Account disclosure information: ', error);
            this.showToast('An unexpected error occurred','error','Please contact AMP Bank');
        })
    }

    //Element event action area
    handleCloseModal(event){
        this.isModalOpen = false;
    }
    handleOpenModal(event){
        this.isModalOpen = true;
    }

    //Reusable toast utility
    showToast(toastTitle, toastVariant, toastMessage){
        this.dispatchEvent(
            new ShowToastEvent({
                title: toastTitle,
                variant: toastVariant,
                message: toastMessage
            }),
        );
    }

    //Get the URL parameters (if there's any)
    getQueryParameters() {
        var params = {};
        var search = location.search.substring(1);
        if (search) {
            params = JSON.parse('{"' + search.replace(/&/g, '","').replace(/=/g, '":"') + '"}', (key, value) => {
                return key === "" ? value : decodeURIComponent(value)
            });
        }
        return params;
    }
}