<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <description>Metadata for GreenID</description>
    <fields>
        <fullName>CIVS_Record_Type_Name__c</fullName>
        <description>Contact_Identity_Verification_Source__c object record type name</description>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>CIVS Record Type Name</label>
        <length>50</length>
        <required>false</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>CIV_Created_By_Name__c</fullName>
        <description>Contact_Identity_Verification__c Object created by name</description>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>CIV Created By Name</label>
        <length>50</length>
        <required>false</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>GreenId_Account_PWD_Vixverify__c</fullName>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>GreenId Account PWD Vixverify</label>
        <required>false</required>
        <type>TextArea</type>
    </fields>
    <fields>
        <fullName>GreenId_Config_External_URL__c</fullName>
        <description>GreenIdConfig URL used in GreenID_VF</description>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>GreenId Config External URL</label>
        <required>false</required>
        <type>Url</type>
    </fields>
    <fields>
        <fullName>GreenId_UI_CSS__c</fullName>
        <description>GreenId Ui CSS used in GreenID_VF</description>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>GreenId UI CSS</label>
        <required>false</required>
        <type>Url</type>
    </fields>
    <fields>
        <fullName>GreenId_UI_JS__c</fullName>
        <description>GreenID Ui js used in GreenID_VF</description>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>GreenId UI JS</label>
        <required>false</required>
        <type>Url</type>
    </fields>
    <fields>
        <fullName>Green__c</fullName>
        <description>Account Id used in VIxVerify</description>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>GreenId Account ID Vixverify</label>
        <length>50</length>
        <required>false</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Record_Type__c</fullName>
        <description>Contact_Identity_Verification__c Object record type name</description>
        <externalId>false</externalId>
        <fieldManageability>DeveloperControlled</fieldManageability>
        <label>CIV Record Type Name</label>
        <length>50</length>
        <required>false</required>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <label>GreenId Setup</label>
    <pluralLabel>GreenId Setup</pluralLabel>
    <visibility>Public</visibility>
</CustomObject>
