import { LightningElement, track, wire, api } from 'lwc';
import { loadStyle } from "lightning/platformResourceLoader";
import { NavigationMixin } from 'lightning/navigation'; //For Navigation
import {ShowToastEvent} from "lightning/platformShowToastEvent";
import bankFormsCss from '@salesforce/resourceUrl/bankFormsConsentsCSS'; //CSS from static resource
import thankYouLogo from '@salesforce/resourceUrl/DigitalFormSuccessIcon'; //CSS from static resource
import rejectDFR from '@salesforce/apex/BankFormsConsentController.reject';
import approveDFR from '@salesforce/apex/BankFormsConsentController.approve';

export default class BankFormsConsents extends NavigationMixin(LightningElement) {
    @api redirectUrl;
    @api isDeclined = false;
    @api selectedConsent = [];
    @api isLoaded = false;
    @track showDeclineScreen = false;
    @track requestNumber;
    @track firstName;
    @track trackMessage;

    checkLogo = thankYouLogo;

    constructor() {
        super();
        Promise.all([
            loadStyle(this, bankFormsCss)
        ]).then(() => {
            // console.log("Loaded css");
        }).catch(error => {
            console.error('Error loading static resources', error);
        });
    }

    connectedCallback() {

        let consent = [];
        consent = this.selectedConsent;
        let declined = this.isDeclined;
        let form = consent.dfr.Form_Name__c;

        this.requestNumber = consent.caseRec.Request_ID__c;
        this.firstName = consent.firstName;
        this.showDeclineScreen = declined;

        if (declined) {

            rejectDFR({ dfrId: consent.dfr.Id, caseId: consent.caseRec.Id })
                .then(data => {
                    this.isLoaded = !this.isLoaded;
                    if (data) {
                        if(data == 'Success'){
                            
                        }else{
                            console.error('Error in updating record: ', 'No record is updated');
                            this.showToast('An unexpected error occurred when updating record', 'error', 'Please contact AMP Bank.');
                        }

                    } else {
                        this.hasError = true;
                        console.error('Error in updating record: ', 'No record is updated');
                        this.showToast('An unexpected error occurred when updating record', 'error', 'Please contact AMP Bank.');
                    }

                }).catch(error => {
                    console.error('Error in updating record: ', error);
                    this.hasError = true;
                    this.showToast('An unexpected error occurred when updating record', 'error', 'Please contact AMP Bank.');
                });
        }else{
            approveDFR({ dfrId: consent.dfr.Id, caseId: consent.caseRec.Id, formname: form })
                .then(data => {
                    this.isLoaded = !this.isLoaded;
                    if (data) {
                        this.trackMessage = data;
                    } else {
                        this.hasError = true;
                        console.error('Error in updating record: ', 'No data received');
                        this.showToast('An unexpected error occurred when updating record', 'error', 'Please contact AMP Bank.');
                    }

                }).catch(error => {
                    console.error('Error in updating record: ', error);
                    this.hasError = true;
                    this.showToast('An unexpected error occurred when updating record', 'error', 'Please contact AMP Bank.');
                });
        }

    }

    handleClose(event){
        console.log('redirectURL: ', this.redirectUrl);
        window.location.href = this.redirectUrl;
    }

    //handle toast events
    showToast(toastTitle, toastVariant, toastMessage){
        this.dispatchEvent(
            new ShowToastEvent({
                title: toastTitle,
                variant: toastVariant,
                message: toastMessage
            }),
        );

        //if error occurs, we keep the spinner loading
        if(toastVariant === 'error'){
            //this.loaded = false;
        }
    }
}