<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Member_Name__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Date_of_Birth__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Claim_Number__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Name_of_Insurer__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Date_Ceased_Work__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TPD_TIB_Benefit_Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Acceptance_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Notification_Transfer_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Concurrent_claim__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Initial_Doctors_Opinion_1__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Summary_1__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Initial_Doctors_Opinion_2__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Summary_2__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Release_of_funds_Withdrawal_Type__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Trustee_Rationale_Determination__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Trustee_Assessment_Notes__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Assessment_Completed_By__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Member_Number__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Product__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Occupation__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Injury_Illness_text__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Balance_Current_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Effective_Date_of_Account_Balance__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Residual_insurance_if_applicable__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Premiums_Insurance_Cancelled_Y_N__c</field>
            </layoutItems>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Initial_Doctors_Opinion_Form_dated_1__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Doctors_Name_1__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Initial_Doctors_Opinion_Form_dated_2__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Doctors_Name_2__c</field>
            </layoutItems>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Date_Assessment_Completed__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Case__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Relationship_to_Deceased__c</fields>
        <fields>Email__c</fields>
        <fields>Postal_Address__c</fields>
        <fields>Contact_Phone__c</fields>
        <fields>Dependant_Type__c</fields>
        <fields>Dependant__c</fields>
        <fields>Percentage_of_Benefit__c</fields>
        <relatedList>Involved_Party__c.Assessment_Form__c</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00h9h000000vZdl</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
