<template>
    
    <div class="content__body">
        <br>
        <br>
        <!-- loading -->
        <template if:false={loaded}>
            <lightning-spinner class="loading slds-is-relative" alternative-text="Loading" size="large" style="position: fixed;"></lightning-spinner>
        </template>

        <!-- Loaded -->
        <template if:true={loaded}>
            <!-- Header -->
            <!-- Header text -->
            <!-- SFP-61733 DOMS Start-->

            <!-- Toast notifications -->
            <template if:true={initializeToast}>
                <div data-id="toastModal" class="slds-show slds-p-top_large ">
                    <div  class="slds-notify_container slds-is-relative">
                        <div class="content__body-main-dashboard slds-notify_toast slds-theme_success slds-media slds-var-p-left_large slds-var-p-right_large toast-margin" role="status">
                            <span class="slds-assistive-text">success</span>
                            <span class="slds-icon_container slds-icon-utility-success slds-m-right_small slds-no-flex slds-align-top" title="Success">
                                <svg xmlns="http://www.w3.org/2000/svg" class="svg-check" aria-label="green check" alt="green check">;
                                    <use xlink:href={svgURL}></use>
                                </svg>
                            </span>
                            <div class="slds-notify__content">
                                <template if:true={dataSharingNotifMessage}> <!--SFP-66666-->
                                    <h6 class="toast-message slds-text-heading_small font-style_light-bold ds-text-font-style">Your joint account data sharing notifications changes were saved.</h6>
                                </template>
                                <template if:false={dataSharingNotifMessage}>
                                    <template if:false={recentJointActionEnabled}>
                                        <template if:false={recentJointActionPartialEnabled}>
                                            <h6 class="toast-message slds-text-heading_small font-style_light-bold ds-text-font-style">The joint account has been disabled and will no longer be available for data sharing. </h6>
                                        </template>
                                    </template>
                                </template>
                                <template if:true={recentJointActionEnabled}>
                                    <h6 class="toast-message slds-text-heading_small font-style_light-bold ds-text-font-style">The joint account is now available for data sharing with an accredited data recipient. </h6>
                                </template>
                                <template if:true={recentJointActionPartialEnabled}>
                                    <h6 class="toast-message slds-text-heading_small font-style_light-bold ds-text-font-style">You have successfully allowed sharing, but other account holder(s) have not.
                                        <br><br>Please advise other account holder(s) to allow sharing by logging into My AMP or My AMP app to access data sharing. </h6>
                                </template>
                            </div>
                            <div class="slds-icon_container slds-notify__close slds-icon-text-success">
                                <button class="slds-button slds-button_icon" title="Close" onclick={closeToast} aria-label="cross graphic">
                                    <lightning-icon icon-name="utility:close" size="small" alternative-text="Close" title="Close"></lightning-icon>
                                </button>
                            </div>
                        </div>
                    </div>
                </div> 
            </template>

            <!-- Page header -->
            <h2 class="slds-text-align_left header-text hurme-text m-margin">Joint account data sharing preferences</h2>
            <div class="slds-var-p-top_medium confirmation-text"><p>To share with an accredited data recipient, your joint account must be set up for data sharing</p></div>
        

            <!-- Joint accounts elligible to share title-->
            <div class="slds-text-align_left header-spacing slds-grid">
                <div class="slds-col"> 
                    <h3 class="hurme-text">Joint account(s) eligible to share</h3>
                </div>
            </div>

            <!-- Joint accounts elligible to share list-->
            <!-- Available account selection -->
            <!-- Error on financial account retrieval -->
            <template if:true={hasError}>
                <div class="slds-scoped-notification slds-media slds-media_center slds-scoped-notification_light" role="status">
                    <div class="slds-media__figure">
                        <lightning-icon icon-name="utility:warning" variant="warning"></lightning-icon>
                    </div>
                    <div class="slds-media__body">
                        <p class="authorisation-text">Error retrieving Financial Accounts</p>
                    </div>
                </div>
            </template>
            
            <template if:false={hasError}>
                <!-- Iterated list of available joint financial accounts -->
                <template if:true={availableJointFinancialAccounts}>
                    <div class="accounts-list-box slds-border_left slds-border_bottom slds-border_right">
                        <template iterator:it={availableJointFinancialAccounts}>
                            <div key={it.value.finAcct.Id}>
                                <lightning-layout class="slds-grid_vertical-align-center fin-acct-details financial-accounts-padding">
                                    <lightning-layout-item size="10" flexibility="auto" padding="horizontal-none" class="slds-text-align_left slds-align-middle slds-grid slds-grid_vertical">
                                        <div class="slds-col">
                                            <h6 class="acc-name-label">{it.value.finAcct.Name} </h6>
                                        </div>
                                        <div class="slds-col">
                                            <p class="text-gray fin-accts-number-text"> 
                                                <template if:true={it.value.finAcct.Bank_BSB__c}>
                                                    BSB <span class="acc-bsb-label">{it.value.bsbLabel}</span> &nbsp;
                                                </template> 
                                                    ACC &nbsp;
                                                <span class="acc-bsb-label">{it.value.accLabel}</span>
                                            </p>
                                        </div>
                                        <div class="slds-col fa_status">
                                            <div class="slds-grid slds-grid_vertical-align-center">
                                                <div class="slds-col slds-grow-none " style="max-width: 50px;">
                                                    <template if:true={it.value.isDOMSStatusEnabled}>
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="svg-check status_icon" aria-label="green check">;
                                                            <use xlink:href={svgURL}></use>
                                                        </svg>
                                                    </template>
                                                    <template if:false={it.value.isDOMSStatusEnabled}>
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="svg-joint-warning status_icon" aria-label="yellow warning">;
                                                            <use xlink:href={svgURLWarning}></use>
                                                        </svg>
                                                    </template>
                                                </div>
                                                <div class="slds-col slds-size_8-of-8">
                                                    <div class="slds-col slds-size_6-of-6">
                                                        <h6>{it.value.status}</h6>
                                                    </div>
                                                    <div class="slds-col slds-size_6-of-6">
                                                        <p>{it.value.disabledSubStatus}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="slds-col fa_status">
                                            <template if:true={it.value.isClosedAccount}>
                                                <div class="slds-grid slds-grid_vertical-align-center">
                                                    <div class="slds-col slds-grow-none " style="max-width: 50px;">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="svg-error status_icon" aria-label="gray cross">;
                                                            <use xlink:href={svgURLError}></use>
                                                        </svg>
                                                    </div>
                                                    <div class="slds-col slds-size_8-of-8">
                                                        <div class="slds-col slds-size_6-of-6">
                                                            <h6>Closed joint account</h6>
                                                        </div>
                                                    </div>
                                                </div>
                                            </template>
                                        </div>
                                    </lightning-layout-item>
                                    <lightning-layout-item flexibility="auto" padding="around-none" class="slds-text-align_right slds-align-middle slds-shrink-none">     
                                        <button class="slds-button slds-float_right" data-id={it.value.finAcct.Id} onclick={handleManage} style="height:44px;"><p class="manage-label">Manage</p></button>  
                                    </lightning-layout-item>
                                </lightning-layout>
                                <div if:false={it.last} key={it.value.finAcct.Id} class="slds-border_bottom border-style "></div>
                            </div>
                        </template>
                    </div>
                </template><!--if:true={availableJointFinancialAccounts}-->

                <!-- Show this message when the logged in user is not eligible for CDR or doesn't have any eligible Accounts to share-->
                <template if:false={availableJointFinancialAccounts}>
                    <template if:true={loggedInUserEligible}>
                        <template if:true={customerOwnedJointAccount}>
                            <div class="no-fa-text accounts-list-box slds-text-align_center">
                                <p class="authorisation-text slds-var-p-left_small slds-var-p-top_large slds-var-p-bottom_large">{label.notEligibleForCDR}</p>
                            </div>
                        </template>

                        <!-- SFP-67282 Show this message when the logged in user doesn't own Joint Accounts-->
                        <template if:false={customerOwnedJointAccount}>
                            <div class="no-fa-text accounts-list-box slds-text-align_center">
                                <p class="authorisation-text slds-var-p-left_small slds-var-p-top_large slds-var-p-bottom_large">{label.noJointAccountOwned}</p>
                            </div>
                        </template>
                    </template><!--end of if:true={loggedInUserEligible}-->

                    <template if:false={loggedInUserEligible}>
                        <div class="no-fa-text accounts-list-box slds-text-align_center">
                            <p class="authorisation-text slds-var-p-left_small slds-var-p-top_large slds-var-p-bottom_large">{label.notEligibleForCDR}</p>
                        </div>
                    </template>
                </template><!--end of if:false={availableJointFinancialAccounts}-->
            </template> <!--end of if:false={hasError}-->

            <!-- SFP-66666 Notifications Settings-->
            <div class="slds-var-m-top_large accounts-unavailable-list-box ">
                <lightning-layout>
                    <button class=" button__collapse slds-button_full-width slds-var-p-left_medium slds-var-p-right_medium slds-var-p-bottom_medium slds-var-p-top_medium slds-border_bottom border-style " onclick={handleNotificationsSettings}>
                        <lightning-layout-item size="9" flexibility="auto" padding="around-none" class="slds-text-align_left">  
                            <div class="slds-grid slds-grid_vertical-align-center">
                                <div class="slds-col slds-grow-none slds-p-top_xx-small" style="max-width: 30px; max-height: 30px;">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="svg-notif" aria-label="notif bell">;
                                        <use xlink:href={svgURLNotification}></use>
                                    </svg>
                                </div>
                                <div class="slds-col slds-size_8-of-8">
                                    <div class="slds-col slds-p-left_medium slds-size_6-of-6">
                                        <h6 class="notif_label">Notifications settings for your joint account(s)</h6>
                                    </div>
                                </div>
                            </div>
                        </lightning-layout-item>
                    </button>
                </lightning-layout>
            </div>

            <!-- Things you should know accordion-->
            <div class="slds-var-m-top_large accounts-unavailable-list-box ">
                <lightning-layout>
                    <button class=" button__collapse slds-button_full-width slds-var-p-left_medium slds-var-p-right_medium slds-var-p-bottom_medium slds-var-p-top_medium slds-border_bottom border-style " onclick={handleThingsYouSHouldKnowCollapse}>
                        <lightning-layout-item size="9" flexibility="auto" padding="around-none" class="slds-text-align_left">
                            <h6 class="accordion_label">Things you should know</h6>
                        </lightning-layout-item>
                            <div class="icons-chevron">
                                <!-- If collapsed -->
                                <template if:false={thingsYouShouldKnowCollapsed}>
                                    <lightning-layout-item size="3" flexibility="auto" padding="around-none" class="slds-text-align_right slds-var-p-right_medium slds-float_right">
                                        <lightning-icon icon-name="utility:chevronup" class="slds-current-color" size="small"></lightning-icon>
                                    </lightning-layout-item>
                                </template>
                                
                                <!-- If expanded -->
                                <template if:true={thingsYouShouldKnowCollapsed}>
                                    <lightning-layout-item size="3" flexibility="auto" padding="around-none" class="slds-text-align_right slds-var-p-right_medium slds-float_right">
                                        <lightning-icon icon-name="utility:chevrondown" class="slds-current-color" size="small"></lightning-icon>
                                    </lightning-layout-item>
                                </template>
                            </div>
                    </button>
                </lightning-layout>
            </div>
            <!-- Things you should know accordion If expanded -->
            <template if:false={thingsYouShouldKnowCollapsed}>
                <div class=" accounts-list-box">
                    <div>
                        <lightning-layout class="slds-var-p-right_medium fin-acct-details slds-grid_vertical-align-center financial-accounts-padding">
                            <lightning-layout-item flexibility="auto" padding="horizontal-none" class="slds-text-align_left slds-align-middle ">
                                <div class="things_you_should_know_container linkTextUnderLine">
                                    <lightning-formatted-rich-text value={label.jointGeneralInfo}> </lightning-formatted-rich-text>
                                    <br>
                                    <h5>If you enable data sharing:</h5>
                                    <br>
                                    <lightning-formatted-rich-text value={label.jointEnableInfo}> </lightning-formatted-rich-text>
                                    <br>
                                    <h5>If you disable data sharing:</h5>
                                    <br>
                                    <lightning-formatted-rich-text value={label.jointDisableInfo}> </lightning-formatted-rich-text>
                                </div>
                            </lightning-layout-item>
                        </lightning-layout>
                    </div>
                </div>
            </template> 

        </template> <!-- End of loaded -->
    </div> <!-- End of content -->
</template>