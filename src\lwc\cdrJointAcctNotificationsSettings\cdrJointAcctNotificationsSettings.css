/* SFP-66666 */
/*.status_icon {
    min-width: 44px;
    padding-right: 8px;
}*/

/*.content__body {
    margin-top:-47px;
    padding-bottom: 50px;
}*/

.toggle_button{
    font-size: 16px;
    font-weight: 600;
}

.content__body{
    margin-top:-47px;
    padding-bottom: 50px;
}

.joint-account-sharing-notifications{
    padding-top: 40px;
}

.sharing-notifs{
	padding-left: 24px;
	padding-right: 24px;
    padding-top: 24px;
    padding-bottom: 24px;
}

.data-sharing-notifs-text{
    text-align: left;
    font-weight: 400;
    font-size: 22px;
    line-height: 32px;

}

.confirmation-modal-text{
    text-align: left;
    font-family: Hurme Geometric Sans 1;
    font-weight: 400;
    font-size: 22px;
    line-height: 32px;
}

.confirmation-modal-body{
    padding: 24px;
    background-color: #FFFFFF;
}

.confirmation-modal-footer{
    background-color: #FFFFFF;
}

.confirmation_modal_button{
    width: 100%;
    height: 100%;
    color: #406BBA;
    padding-top: 23px;
    padding-bottom: 23px;
}

.confirmation_modal_button:hover{
    background-color: #F5F5F5;
}

.confirmation_modal_button{
    padding: 24px 15p 24px 15px;
}

.confirmation-modal-header{
    padding: 24px;
    background-color: #F5F5F5;
}

.confirmation-modal-button-container{
    border-style: solid;
    border-color: #F5F5F5;
    border-width: 1px;
}
