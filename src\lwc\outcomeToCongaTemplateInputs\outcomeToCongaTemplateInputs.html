<template>
    <lightning-layout multiple-rows> 
        <lightning-layout-item size="3">
            <div class="slds-var-p-horizontal_medium slds-var-p-vertical_x-small">
                <span style={_outcomeStyle}>{outcome.outcomeLabel}</span>
            </div>
        </lightning-layout-item>

        <lightning-layout-item size="8">
            <div class="slds-var-p-around_xx-small container">
                
                <c-lookup
                    selection={initialSelection}
                    errors={errors}
                    onsearch={handleLookupSearch}
                    onselectionchange={handleLookupSelectionChange}
                    placeholder="Search Conga Template"
                    is-multi-entry>
                </c-lookup>
                
            </div>
        </lightning-layout-item>

        <lightning-layout-item size="1">
            <div class="slds-var-p-around_xx-small">
                <lightning-button-icon 
                    icon-name="utility:delete" 
                    alternative-text="Delete" 
                    class="slds-var-m-left_xx-small" 
                    title="Delete" 
                    onclick={handleRemoveRow}>
                </lightning-button-icon>
            </div>
        </lightning-layout-item>

    </lightning-layout>
</template>